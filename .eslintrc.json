{"extends": ["next/core-web-vitals", "@typescript-eslint/recommended", "prettier"], "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "react", "react-hooks"], "rules": {"@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_"}], "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/explicit-function-return-type": "off", "@typescript-eslint/explicit-module-boundary-types": "off", "@typescript-eslint/no-non-null-assertion": "warn", "react/prop-types": "off", "react/react-in-jsx-scope": "off", "react-hooks/rules-of-hooks": "error", "react-hooks/exhaustive-deps": "warn", "prefer-const": "error", "no-var": "error", "no-console": ["warn", {"allow": ["warn", "error"]}], "eqeqeq": ["error", "always"], "curly": ["error", "all"]}, "env": {"browser": true, "node": true, "es6": true, "jest": true}, "settings": {"react": {"version": "detect"}}, "overrides": [{"files": ["**/__tests__/**/*", "**/*.test.*", "**/*.spec.*"], "env": {"jest": true}, "rules": {"@typescript-eslint/no-explicit-any": "off"}}]}