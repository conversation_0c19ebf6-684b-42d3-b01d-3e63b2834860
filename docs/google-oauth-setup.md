# Google OAuth Setup Guide

This guide will help you set up Google Sign-In for RenovHub.

## Prerequisites

- A Google account
- Access to Google Cloud Console
- Supabase project with authentication enabled

## Step 1: Google Cloud Console Setup

1. **Go to Google Cloud Console**
   - Visit [Google Cloud Console](https://console.cloud.google.com/)
   - Sign in with your Google account

2. **Create or Select a Project**
   - Create a new project or select an existing one
   - Note the project ID for reference

3. **Enable Google+ API**
   - Go to "APIs & Services" > "Library"
   - Search for "Google+ API"
   - Click on it and press "Enable"

4. **Create OAuth 2.0 Credentials**
   - Go to "APIs & Services" > "Credentials"
   - Click "Create Credentials" > "OAuth 2.0 Client IDs"
   - Choose "Web application" as the application type
   - Give it a name (e.g., "RenovHub OAuth")

5. **Configure Authorized Redirect URIs**
   Add these URIs based on your environment:
   
   **Development:**
   ```
   http://localhost:3000/auth/callback
   ```
   
   **Production:**
   ```
   https://yourdomain.com/auth/callback
   ```

6. **Save and Copy Credentials**
   - Copy the Client ID and Client Secret
   - Keep these secure and never commit them to version control

## Step 2: Supabase Configuration

1. **Go to Supabase Dashboard**
   - Navigate to your project dashboard
   - Go to "Authentication" > "Providers"

2. **Enable Google Provider**
   - Find "Google" in the list of providers
   - Toggle it to "Enabled"

3. **Add Google Credentials**
   - Paste your Google Client ID
   - Paste your Google Client Secret
   - Save the configuration

4. **Configure Redirect URLs**
   - In Supabase, go to "Authentication" > "URL Configuration"
   - Add your site URL and redirect URLs

## Step 3: Environment Variables

Add these to your `.env.local` file:

```env
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here
```

## Step 4: Testing

1. **Development Testing**
   - Start your development server: `npm run dev`
   - Go to the login or registration page
   - Click "Continue with Google"
   - Complete the OAuth flow

2. **Production Testing**
   - Deploy your application
   - Update the redirect URIs in Google Cloud Console
   - Test the OAuth flow in production

## Troubleshooting

### Common Issues

1. **"redirect_uri_mismatch" Error**
   - Check that your redirect URIs in Google Cloud Console match exactly
   - Ensure no trailing slashes or extra characters

2. **"invalid_client" Error**
   - Verify your Client ID and Client Secret are correct
   - Check that the Google+ API is enabled

3. **User Profile Not Created**
   - Check Supabase logs for any database errors
   - Ensure your users table has the correct schema

### Debug Steps

1. Check browser console for errors
2. Check Supabase logs in the dashboard
3. Verify environment variables are loaded correctly
4. Test with a fresh incognito/private browser window

## Security Considerations

1. **Never expose Client Secret**
   - Keep it in server-side environment variables only
   - Never include it in client-side code

2. **Use HTTPS in Production**
   - Google OAuth requires HTTPS for production
   - Ensure your redirect URIs use HTTPS

3. **Validate Redirect URIs**
   - Only add necessary redirect URIs
   - Remove development URIs from production

## Additional Resources

- [Google OAuth 2.0 Documentation](https://developers.google.com/identity/protocols/oauth2)
- [Supabase Auth Documentation](https://supabase.com/docs/guides/auth)
- [Next.js Authentication Patterns](https://nextjs.org/docs/authentication)
