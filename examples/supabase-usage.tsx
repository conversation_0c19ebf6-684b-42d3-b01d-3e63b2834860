"use client"

import { useState, useEffect } from 'react'
import { useUser } from '@/contexts/user-context'
import { authService } from '@/services/auth'
import { 
  userService, 
  projectService, 
  contractorService, 
  bidService,
  messageService,
  conversationService 
} from '@/services/database'
import { realtimeService, notificationService } from '@/services/realtime'
import { apiClient } from '@/services/api'

// Example: Authentication Component
export function AuthExample() {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [name, setName] = useState('')
  const [loading, setLoading] = useState(false)
  const { user, isAuthenticated } = useUser()

  const handleLogin = async () => {
    setLoading(true)
    const result = await authService.login({ email, password })
    if (result.success) {
      console.log('Login successful:', result.user)
    } else {
      console.error('Login failed:', result.error)
    }
    setLoading(false)
  }

  const handleRegister = async () => {
    setLoading(true)
    const result = await authService.register({
      email,
      password,
      name,
      role: 'customer'
    })
    if (result.success) {
      console.log('Registration successful:', result.user)
    } else {
      console.error('Registration failed:', result.error)
    }
    setLoading(false)
  }

  if (isAuthenticated) {
    return (
      <div>
        <h2>Welcome, {user?.name}!</h2>
        <p>Role: {user?.role}</p>
        <button onClick={() => authService.logout()}>Logout</button>
      </div>
    )
  }

  return (
    <div>
      <h2>Login / Register</h2>
      <input
        type="email"
        placeholder="Email"
        value={email}
        onChange={(e) => setEmail(e.target.value)}
      />
      <input
        type="password"
        placeholder="Password"
        value={password}
        onChange={(e) => setPassword(e.target.value)}
      />
      <input
        type="text"
        placeholder="Name (for registration)"
        value={name}
        onChange={(e) => setName(e.target.value)}
      />
      <button onClick={handleLogin} disabled={loading}>
        Login
      </button>
      <button onClick={handleRegister} disabled={loading}>
        Register
      </button>
    </div>
  )
}

// Example: Project Management Component
export function ProjectExample() {
  const [projects, setProjects] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const { user } = useUser()

  useEffect(() => {
    if (user) {
      loadProjects()
    }
  }, [user])

  const loadProjects = async () => {
    setLoading(true)
    try {
      if (user?.role === 'customer') {
        const userProjects = await projectService.findByCustomerId(user.id)
        setProjects(userProjects)
      } else {
        const activeProjects = await projectService.findActiveProjects()
        setProjects(activeProjects)
      }
    } catch (error) {
      console.error('Error loading projects:', error)
    }
    setLoading(false)
  }

  const createProject = async () => {
    if (!user) return

    const newProject = {
      title: 'Kitchen Renovation',
      description: 'Complete kitchen remodel with new cabinets and appliances',
      category: 'kitchen',
      customer_id: user.id,
      budget: { min: 10000, max: 20000, currency: 'USD', flexible: true },
      timeline: { duration: 30, flexible: true, urgency: 'within-month' },
      location: {
        address: '123 Main St',
        city: 'San Francisco',
        state: 'CA',
        zipCode: '94102',
        country: 'USA'
      },
      requirements: ['Licensed contractor', 'References required']
    }

    const result = await projectService.create(newProject)
    if (result) {
      console.log('Project created:', result)
      loadProjects()
    }
  }

  return (
    <div>
      <h2>Projects</h2>
      {user?.role === 'customer' && (
        <button onClick={createProject}>Create New Project</button>
      )}
      {loading ? (
        <p>Loading...</p>
      ) : (
        <ul>
          {projects.map((project) => (
            <li key={project.id}>
              <h3>{project.title}</h3>
              <p>{project.description}</p>
              <p>Status: {project.status}</p>
              <p>Category: {project.category}</p>
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}

// Example: Real-time Messages Component
export function MessagesExample({ conversationId }: { conversationId: string }) {
  const [messages, setMessages] = useState<any[]>([])
  const [newMessage, setNewMessage] = useState('')
  const { user } = useUser()

  useEffect(() => {
    // Load initial messages
    loadMessages()

    // Subscribe to real-time updates
    const unsubscribe = realtimeService.subscribeToMessages(
      conversationId,
      (payload) => {
        if (payload.eventType === 'INSERT' && payload.new) {
          setMessages(prev => [...prev, payload.new])
        }
      }
    )

    return () => {
      unsubscribe()
    }
  }, [conversationId])

  const loadMessages = async () => {
    const messageList = await messageService.findByConversationId(conversationId)
    setMessages(messageList)
  }

  const sendMessage = async () => {
    if (!user || !newMessage.trim()) return

    const message = {
      conversation_id: conversationId,
      sender_id: user.id,
      content: newMessage,
      type: 'text'
    }

    const result = await messageService.create(message)
    if (result) {
      setNewMessage('')
    }
  }

  return (
    <div>
      <h2>Messages</h2>
      <div style={{ height: '300px', overflowY: 'scroll', border: '1px solid #ccc' }}>
        {messages.map((message) => (
          <div key={message.id} style={{ padding: '10px', borderBottom: '1px solid #eee' }}>
            <strong>{message.sender_id === user?.id ? 'You' : 'Other'}:</strong>
            <p>{message.content}</p>
            <small>{new Date(message.sent_at).toLocaleString()}</small>
          </div>
        ))}
      </div>
      <div style={{ marginTop: '10px' }}>
        <input
          type="text"
          value={newMessage}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type a message..."
          style={{ width: '70%' }}
        />
        <button onClick={sendMessage} style={{ width: '25%', marginLeft: '5%' }}>
          Send
        </button>
      </div>
    </div>
  )
}

// Example: File Upload Component
export function FileUploadExample() {
  const [uploading, setUploading] = useState(false)
  const [uploadedUrl, setUploadedUrl] = useState<string | null>(null)
  const { user } = useUser()

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file || !user) return

    setUploading(true)
    try {
      const result = await apiClient.uploadToStorage(
        'project-photos',
        `${user.id}/${Date.now()}_${file.name}`,
        file
      )

      if (result.success && result.data) {
        setUploadedUrl(result.data.url)
        console.log('File uploaded successfully:', result.data)
      } else {
        console.error('Upload failed:', result.error)
      }
    } catch (error) {
      console.error('Upload error:', error)
    }
    setUploading(false)
  }

  return (
    <div>
      <h2>File Upload</h2>
      <input
        type="file"
        accept="image/*"
        onChange={handleFileUpload}
        disabled={uploading}
      />
      {uploading && <p>Uploading...</p>}
      {uploadedUrl && (
        <div>
          <p>Upload successful!</p>
          <img src={uploadedUrl} alt="Uploaded" style={{ maxWidth: '200px' }} />
        </div>
      )}
    </div>
  )
}

// Example: Notifications Component
export function NotificationsExample() {
  const { notifications, markNotificationAsRead } = useUser()

  return (
    <div>
      <h2>Notifications ({notifications.filter(n => !n.read).length} unread)</h2>
      {notifications.length === 0 ? (
        <p>No notifications</p>
      ) : (
        <ul>
          {notifications.map((notification) => (
            <li
              key={notification.id}
              style={{
                padding: '10px',
                backgroundColor: notification.read ? '#f5f5f5' : '#e3f2fd',
                marginBottom: '5px',
                cursor: 'pointer'
              }}
              onClick={() => markNotificationAsRead(notification.id)}
            >
              <h4>{notification.title}</h4>
              <p>{notification.message}</p>
              <small>{notification.timestamp.toLocaleString()}</small>
            </li>
          ))}
        </ul>
      )}
    </div>
  )
}
