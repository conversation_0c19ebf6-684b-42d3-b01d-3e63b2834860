# RenovHub Supabase Integration

This document outlines the complete Supabase integration for the RenovHub application, including database setup, authentication, real-time features, and usage examples.

## 🚀 Quick Start

### 1. Environment Setup

The following environment variables are configured in `.env.local`:

```env
# Supabase Configuration - RenovHub Project
NEXT_PUBLIC_SUPABASE_URL=https://copulqrgjadamcfixrta.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME=RenovHub

# Storage Configuration
NEXT_PUBLIC_SUPABASE_STORAGE_URL=https://copulqrgjadamcfixrta.supabase.co/storage/v1
```

### 2. Database Schema

The database includes the following tables:

- **users** - User profiles extending Supabase auth
- **contractors** - Contractor-specific information
- **projects** - Customer project listings
- **bids** - Contractor bids on projects
- **conversations** - Project-related conversations
- **messages** - Individual messages in conversations
- **reviews** - Project completion reviews
- **payments** - Payment transactions

### 3. Authentication

Authentication is handled through Supabase Auth with automatic user profile creation:

```typescript
import { authService } from '@/services/auth'

// Login
const result = await authService.login({ email, password })

// Register
const result = await authService.register({
  email,
  password,
  name,
  role: 'customer' // or 'pro'
})

// Logout
await authService.logout()
```

## 📊 Database Services

### User Service

```typescript
import { userService } from '@/services/database'

// Find user by email
const user = await userService.findByEmail('<EMAIL>')

// Update user preferences
await userService.updatePreferences(userId, preferences)
```

### Project Service

```typescript
import { projectService } from '@/services/database'

// Get customer's projects
const projects = await projectService.findByCustomerId(customerId)

// Get active projects for contractors
const activeProjects = await projectService.findActiveProjects('kitchen')

// Create new project
const project = await projectService.create({
  title: 'Kitchen Renovation',
  description: 'Complete kitchen remodel',
  category: 'kitchen',
  customer_id: userId,
  budget: { min: 10000, max: 20000, currency: 'USD', flexible: true },
  timeline: { duration: 30, flexible: true },
  location: { address: '123 Main St', city: 'San Francisco', state: 'CA' }
})
```

### Contractor Service

```typescript
import { contractorService } from '@/services/database'

// Search contractors
const results = await contractorService.searchContractors({
  category: ['kitchen', 'bathroom'],
  rating: 4.0,
  verified: true,
  sortBy: 'rating',
  sortOrder: 'desc'
})

// Update contractor rating
await contractorService.updateRating(contractorId, newRating)
```

## 🔄 Real-time Features

### Message Subscriptions

```typescript
import { realtimeService } from '@/services/realtime'

// Subscribe to messages in a conversation
const unsubscribe = realtimeService.subscribeToMessages(
  conversationId,
  (payload) => {
    if (payload.eventType === 'INSERT') {
      console.log('New message:', payload.new)
    }
  }
)

// Clean up subscription
unsubscribe()
```

### Project Updates

```typescript
// Subscribe to project updates
const unsubscribe = realtimeService.subscribeToProject(
  projectId,
  (payload) => {
    if (payload.eventType === 'UPDATE') {
      console.log('Project updated:', payload.new)
    }
  }
)
```

### Notifications

```typescript
import { notificationService } from '@/services/realtime'

// Subscribe to user notifications
const unsubscribe = notificationService.subscribeToNotifications(
  userId,
  (notification) => {
    console.log('New notification:', notification)
    // Handle notification (show toast, update UI, etc.)
  }
)
```

## 📁 File Storage

### Upload Files

```typescript
import { apiClient } from '@/services/api'

// Upload to storage bucket
const result = await apiClient.uploadToStorage(
  'project-photos',
  `${userId}/${Date.now()}_${file.name}`,
  file
)

if (result.success) {
  console.log('File URL:', result.data.url)
}
```

### Storage Buckets

- **avatars** - User profile pictures (public, 5MB limit)
- **project-photos** - Project images (public, 10MB limit)
- **documents** - Private documents (private, 50MB limit)
- **attachments** - Message attachments (private, 20MB limit)

## 🔒 Row Level Security (RLS)

All tables have RLS enabled with policies ensuring:

- Users can only access their own data
- Contractors can view active projects in their specialties
- Project owners can view bids on their projects
- Conversation participants can access messages
- Public data (reviews, contractor profiles) is viewable by authenticated users

## 🎯 Usage in Components

### Using the User Context

```typescript
import { useUser } from '@/contexts/user-context'

function MyComponent() {
  const { 
    user, 
    isAuthenticated, 
    loading, 
    notifications, 
    markNotificationAsRead 
  } = useUser()

  if (loading) return <div>Loading...</div>
  if (!isAuthenticated) return <div>Please log in</div>

  return (
    <div>
      <h1>Welcome, {user.name}!</h1>
      <p>Unread notifications: {notifications.filter(n => !n.read).length}</p>
    </div>
  )
}
```

### Real-time Message Component

```typescript
import { useEffect, useState } from 'react'
import { realtimeService, messageService } from '@/services'

function MessageList({ conversationId }) {
  const [messages, setMessages] = useState([])

  useEffect(() => {
    // Load initial messages
    messageService.findByConversationId(conversationId)
      .then(setMessages)

    // Subscribe to new messages
    const unsubscribe = realtimeService.subscribeToMessages(
      conversationId,
      (payload) => {
        if (payload.eventType === 'INSERT') {
          setMessages(prev => [...prev, payload.new])
        }
      }
    )

    return unsubscribe
  }, [conversationId])

  return (
    <div>
      {messages.map(message => (
        <div key={message.id}>{message.content}</div>
      ))}
    </div>
  )
}
```

## 🛠️ Development Commands

```bash
# Install dependencies
pnpm install

# Start development server
pnpm dev

# Run database migrations (if using Supabase CLI)
supabase db push

# Generate TypeScript types
supabase gen types typescript --project-id copulqrgjadamcfixrta > types/database.ts
```

## 📋 Migration Files

Database migrations are located in `supabase/migrations/`:

- `001_initial_schema.sql` - Creates all tables, types, indexes, and triggers
- `002_rls_policies.sql` - Sets up Row Level Security policies

## 🔧 Configuration Files

- `lib/supabase.ts` - Supabase client configuration
- `services/auth.ts` - Authentication service
- `services/database.ts` - Database service layer
- `services/realtime.ts` - Real-time subscriptions
- `services/api.ts` - Updated API client
- `contexts/user-context.tsx` - User context with Supabase integration

## 🚨 Security Considerations

1. **Environment Variables**: Never expose service role keys in client-side code
2. **RLS Policies**: All tables have appropriate RLS policies
3. **File Upload**: Storage policies restrict access to user's own files
4. **Authentication**: JWT tokens are managed automatically by Supabase
5. **Data Validation**: Always validate data on both client and server side

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [Row Level Security](https://supabase.com/docs/guides/auth/row-level-security)
- [Real-time Subscriptions](https://supabase.com/docs/guides/realtime)
- [Storage](https://supabase.com/docs/guides/storage)
