"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/components/ui/use-toast"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { CustomerRoute } from "@/components/route-guard"
import { useUser } from "@/contexts/user-context"
import { ProjectCard, EnhancedCard } from "@/components/ui/enhanced-card"
import { projectService } from "@/services/database"
import { Project } from "@/types"
import {
  Plus,
  Clock,
  CheckCircle,
  Hammer,
  AlertCircle,
  Loader2,
  Calendar,
  DollarSign,
  MapPin,
  Eye,
  Edit,
  MessageCircle,
  TrendingUp,
  Filter,
  Search,
  Grid,
  List,
  MoreHorizontal
} from "lucide-react"
import Link from "next/link"
import { DatabaseStatus } from "@/components/database-status"

// Modern Project Card Component
interface ProjectDisplayCardProps {
  project: any
  viewMode: "grid" | "list"
  onView: () => void
  onEdit: () => void
  onMessage: () => void
  onDelete?: () => void
  formatBudget: (budget: any) => string
  formatLocation: (location: any) => string
  formatDate: (date: Date) => string
  getStatusColor: (status: string) => string
  getPriorityColor: (priority: string) => string
}

function ProjectDisplayCard({
  project,
  viewMode,
  onView,
  onEdit,
  onMessage,
  onDelete,
  formatBudget,
  formatLocation,
  formatDate,
  getStatusColor,
  getPriorityColor
}: ProjectDisplayCardProps) {
  if (viewMode === "list") {
    return (
      <Card className="border-slate-200 hover:shadow-md hover:border-slate-300 transition-all duration-200 group">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-3 mb-3">
                <h3
                  className="text-lg font-semibold text-slate-900 truncate cursor-pointer hover:text-blue-600 transition-colors"
                  onClick={onView}
                >
                  {project.title}
                </h3>
                <Badge className={getStatusColor(project.status)}>
                  {project.status.replace('-', ' ').toUpperCase()}
                </Badge>
                {project.priority && (
                  <Badge variant="outline" className={getPriorityColor(project.priority)}>
                    {project.priority.toUpperCase()}
                  </Badge>
                )}
              </div>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-slate-600">
                <div className="flex items-center space-x-2">
                  <Hammer className="h-4 w-4 text-slate-400 flex-shrink-0" />
                  <span className="capitalize truncate">{project.category}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-slate-400 flex-shrink-0" />
                  <span className="truncate">{formatBudget(project.budget)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-slate-400 flex-shrink-0" />
                  <span className="truncate">{formatLocation(project.location)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-slate-400 flex-shrink-0" />
                  <span className="truncate">{formatDate(new Date(project.created_at))}</span>
                </div>
              </div>
            </div>

            {/* Enhanced Action Buttons */}
            <div className="flex items-center space-x-1 ml-6 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <Button
                variant="ghost"
                size="sm"
                onClick={onView}
                onKeyDown={(e) => e.key === 'Enter' && onView()}
                className="h-8 w-8 p-0 hover:bg-blue-50 hover:text-blue-600 focus:bg-blue-50 focus:text-blue-600 focus:opacity-100"
                title="View Project"
                aria-label="View Project"
              >
                <Eye className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onEdit}
                onKeyDown={(e) => e.key === 'Enter' && onEdit()}
                className="h-8 w-8 p-0 hover:bg-green-50 hover:text-green-600 focus:bg-green-50 focus:text-green-600 focus:opacity-100"
                title="Edit Project"
                aria-label="Edit Project"
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onMessage}
                onKeyDown={(e) => e.key === 'Enter' && onMessage()}
                className="h-8 w-8 p-0 hover:bg-purple-50 hover:text-purple-600 focus:bg-purple-50 focus:text-purple-600 focus:opacity-100"
                title="Message Contractor"
                aria-label="Message Contractor"
              >
                <MessageCircle className="h-4 w-4" />
              </Button>
              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onDelete}
                  onKeyDown={(e) => e.key === 'Enter' && onDelete()}
                  className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 focus:bg-red-50 focus:text-red-600 focus:opacity-100"
                  title="More Options"
                  aria-label="More Options"
                >
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="border-slate-200 hover:shadow-lg hover:border-slate-300 transition-all duration-200 group cursor-pointer">
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0" onClick={onView}>
            <h3 className="text-lg font-semibold text-slate-900 truncate group-hover:text-blue-600 transition-colors mb-1">
              {project.title}
            </h3>
            <p className="text-sm text-slate-600 capitalize flex items-center">
              <Hammer className="h-3 w-3 mr-1 text-slate-400" />
              {project.category}
            </p>
          </div>
          <div className="flex flex-col items-end space-y-2">
            <Badge className={getStatusColor(project.status)}>
              {project.status.replace('-', ' ').toUpperCase()}
            </Badge>
            {project.priority && (
              <Badge variant="outline" className={getPriorityColor(project.priority)}>
                {project.priority.toUpperCase()}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-3">
          <div className="flex items-center space-x-2 text-sm text-slate-600">
            <DollarSign className="h-4 w-4 text-slate-400 flex-shrink-0" />
            <span className="truncate">{formatBudget(project.budget)}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-slate-600">
            <MapPin className="h-4 w-4 text-slate-400 flex-shrink-0" />
            <span className="truncate">{formatLocation(project.location)}</span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-slate-600">
            <Calendar className="h-4 w-4 text-slate-400 flex-shrink-0" />
            <span className="truncate">{formatDate(new Date(project.created_at))}</span>
          </div>
        </div>

        {/* Enhanced Action Buttons */}
        <div className="flex items-center justify-between pt-4 border-t border-slate-100">
          <div className="flex items-center space-x-2 flex-1">
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onView()
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.stopPropagation()
                  onView()
                }
              }}
              className="flex-1 hover:bg-blue-50 hover:border-blue-200 hover:text-blue-600 focus:bg-blue-50 focus:border-blue-200 focus:text-blue-600 transition-colors"
              aria-label="View Project"
            >
              <Eye className="h-4 w-4 mr-1" />
              View
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onEdit()
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.stopPropagation()
                  onEdit()
                }
              }}
              className="flex-1 hover:bg-green-50 hover:border-green-200 hover:text-green-600 focus:bg-green-50 focus:border-green-200 focus:text-green-600 transition-colors"
              aria-label="Edit Project"
            >
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
          </div>
          <div className="flex items-center space-x-1 ml-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onMessage()
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.stopPropagation()
                  onMessage()
                }
              }}
              className="h-8 w-8 p-0 hover:bg-purple-50 hover:text-purple-600 focus:bg-purple-50 focus:text-purple-600 transition-colors"
              title="Message Contractor"
              aria-label="Message Contractor"
            >
              <MessageCircle className="h-4 w-4" />
            </Button>
            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete()
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    e.stopPropagation()
                    onDelete()
                  }
                }}
                className="h-8 w-8 p-0 hover:bg-red-50 hover:text-red-600 focus:bg-red-50 focus:text-red-600 transition-colors"
                title="More Options"
                aria-label="More Options"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export default function ProjectsPage() {
  const { user } = useUser()
  const router = useRouter()
  const { toast } = useToast()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState("")

  useEffect(() => {
    loadProjects()
  }, [user])

  const loadProjects = async () => {
    if (!user) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)
      console.log('Loading projects for user:', user.id)
      const response = await projectService.findByCustomerId(user.id)
      console.log('Projects response:', response)

      if (response.success && response.data) {
        setProjects(response.data as any)
      } else {
        // If database is not set up yet, show a helpful message
        if (response.error?.includes('relation') || response.error?.includes('table')) {
          setError('Database not set up yet. Please run the database migrations.')
        } else {
          setError(response.error || 'Failed to load projects')
        }
        setProjects([])
      }
    } catch (err) {
      setError("Failed to load projects. Please try again.")
      console.error("Error loading projects:", err)
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <Clock className="h-4 w-4 text-status-info" />
      case "completed":
        return <CheckCircle className="h-4 w-4 text-status-success" />
      default:
        return <Clock className="h-4 w-4 text-slate-400" />
    }
  }

  const formatBudget = (budget: any) => {
    if (!budget || typeof budget !== 'object' || !budget.min || !budget.max) {
      return 'Budget not specified'
    }
    return `$${budget.min.toLocaleString()} - $${budget.max.toLocaleString()}`
  }

  const formatLocation = (location: any) => {
    if (!location) return 'Location not specified'
    if (typeof location === 'string') return location
    if (typeof location === 'object' && location.address) return location.address
    if (typeof location === 'object' && location.city) return location.city
    return 'Location not specified'
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    if (diffDays === 1) return "1 day ago"
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} week${Math.ceil(diffDays / 7) > 1 ? 's' : ''} ago`
    return `${Math.ceil(diffDays / 30)} month${Math.ceil(diffDays / 30) > 1 ? 's' : ''} ago`
  }

  // Filter projects based on active tab and search
  const filteredProjects = projects.filter(project => {
    const matchesTab = activeTab === "all" || project.status === activeTab
    const matchesSearch = searchQuery === "" ||
      project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      project.category.toLowerCase().includes(searchQuery.toLowerCase())
    return matchesTab && matchesSearch
  })

  // Get project statistics
  const projectStats = {
    total: projects.length,
    active: projects.filter(p => p.status === 'active').length,
    inProgress: projects.filter(p => p.status === 'in-progress').length,
    completed: projects.filter(p => p.status === 'completed').length,
    draft: projects.filter(p => p.status === 'draft').length
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-blue-100 text-blue-800'
      case 'in-progress': return 'bg-yellow-100 text-yellow-800'
      case 'completed': return 'bg-green-100 text-green-800'
      case 'draft': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800'
      case 'medium': return 'bg-yellow-100 text-yellow-800'
      case 'low': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  // Enhanced action handlers
  const handleViewProject = (projectId: string) => {
    toast({
      title: "Opening project",
      description: "Redirecting to project details...",
    })
    router.push(`/project/${projectId}`)
  }

  const handleEditProject = (projectId: string) => {
    toast({
      title: "Opening editor",
      description: "Redirecting to project editor...",
    })
    router.push(`/project/${projectId}/edit`)
  }

  const handleMessageContractor = (project: any) => {
    if (project.contractor_id) {
      toast({
        title: "Opening messages",
        description: "Starting conversation with contractor...",
      })
      router.push(`/messages?project=${project.id}&contractor=${project.contractor_id}`)
    } else if (project.status === 'active') {
      toast({
        title: "View bids",
        description: "Check available contractor bids...",
      })
      router.push(`/project/${project.id}#bids`)
    } else {
      toast({
        title: "No contractor assigned",
        description: "This project doesn't have an assigned contractor yet.",
        variant: "destructive"
      })
    }
  }

  const handleDeleteProject = (projectId: string) => {
    const confirmed = window.confirm(
      "Are you sure you want to delete this project? This action cannot be undone."
    )

    if (confirmed) {
      toast({
        title: "Project deleted",
        description: "The project has been successfully deleted.",
      })
      // TODO: Implement actual delete API call
      setProjects(prev => prev.filter(p => p.id !== projectId))
    }
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="container-native section-native">
        {/* Mobile-Optimized Header */}
        <div className="flex flex-col gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="space-y-1 sm:space-y-2">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-slate-900">Your Projects</h1>
              <p className="text-sm sm:text-base text-slate-600 max-w-2xl">Manage and track your renovation projects from start to finish</p>
            </div>

            {/* Always show New Project button on desktop, conditionally on mobile */}
            {!loading && !error && (
              <div className="flex-shrink-0">
                <Link href="/">
                  <Button className="w-full sm:w-auto bg-blue-600 hover:bg-blue-700 text-white">
                    <Plus className="h-4 w-4 mr-2" />
                    <span className="hidden sm:inline">New Project</span>
                    <span className="sm:hidden">New</span>
                  </Button>
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Project Statistics Dashboard */}
        {!loading && !error && projects.length > 0 && (
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-6 sm:mb-8">
            <Card className="border-slate-200 hover:shadow-sm transition-shadow">
              <CardContent className="p-4 sm:p-5">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1">{projectStats.total}</p>
                    <p className="text-xs sm:text-sm text-slate-600 font-medium">Total Projects</p>
                  </div>
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-blue-100 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Hammer className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-slate-200 hover:shadow-sm transition-shadow">
              <CardContent className="p-4 sm:p-5">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1">{projectStats.inProgress}</p>
                    <p className="text-xs sm:text-sm text-slate-600 font-medium">In Progress</p>
                  </div>
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-yellow-100 rounded-xl flex items-center justify-center flex-shrink-0">
                    <Clock className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-slate-200 hover:shadow-sm transition-shadow">
              <CardContent className="p-4 sm:p-5">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1">{projectStats.completed}</p>
                    <p className="text-xs sm:text-sm text-slate-600 font-medium">Completed</p>
                  </div>
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-green-100 rounded-xl flex items-center justify-center flex-shrink-0">
                    <CheckCircle className="h-5 w-5 sm:h-6 sm:w-6 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-slate-200 hover:shadow-sm transition-shadow">
              <CardContent className="p-4 sm:p-5">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-2xl sm:text-3xl font-bold text-slate-900 mb-1">{projectStats.active}</p>
                    <p className="text-xs sm:text-sm text-slate-600 font-medium">Active</p>
                  </div>
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-purple-100 rounded-xl flex items-center justify-center flex-shrink-0">
                    <TrendingUp className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Mobile-Responsive Search and Filter Controls */}
        {!loading && !error && projects.length > 0 && (
          <div className="flex flex-col gap-3 sm:flex-row sm:gap-4 mb-4 sm:mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-2.5 sm:py-2 text-sm border border-slate-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div className="flex items-center justify-between sm:justify-start gap-2">
              <span className="text-sm text-slate-600 sm:hidden">View:</span>
              <div className="flex items-center gap-1">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="px-3 h-9"
                  aria-label="Grid view"
                >
                  <Grid className="h-4 w-4" />
                  <span className="ml-1 sm:hidden">Grid</span>
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="px-3 h-9"
                  aria-label="List view"
                >
                  <List className="h-4 w-4" />
                  <span className="ml-1 sm:hidden">List</span>
                </Button>
              </div>
            </div>
          </div>
        )}

        {/* Loading State */}
        {loading && (
          <div className={`grid gap-4 ${viewMode === "grid" ? "grid-cols-1 md:grid-cols-2 lg:grid-cols-3" : "grid-cols-1"}`}>
            {[1, 2, 3].map((i) => (
              <Card key={i} className="border-slate-200">
                <CardContent className="p-6">
                  <div className="animate-pulse space-y-4">
                    <div className="h-4 bg-slate-200 rounded w-3/4"></div>
                    <div className="h-3 bg-slate-200 rounded w-1/2"></div>
                    <div className="h-3 bg-slate-200 rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="space-y-6">
            <Card className="border-red-200 bg-red-50">
              <CardContent className="p-8 text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-slate-900 mb-2">Error Loading Projects</h3>
                <p className="text-slate-600 mb-6">{error}</p>
                <Button onClick={loadProjects} className="bg-red-600 hover:bg-red-700 text-white">
                  <Loader2 className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </CardContent>
            </Card>
            <DatabaseStatus />
          </div>
        )}

        {/* Projects Display */}
        {!loading && !error && (
          <>
            {projects.length === 0 ? (
              <Card className="border-slate-200">
                <CardContent className="p-8 sm:p-12 text-center">
                  <Hammer className="h-12 w-12 sm:h-16 sm:w-16 text-slate-300 mx-auto mb-4 sm:mb-6" />
                  <h3 className="text-lg sm:text-xl font-semibold text-slate-900 mb-2 sm:mb-3">No Projects Yet</h3>
                  <p className="text-sm sm:text-base text-slate-600 max-w-md mx-auto">Start your first renovation project today and connect with qualified contractors. Use the "New Project" button above to get started.</p>
                </CardContent>
              </Card>
            ) : (
              <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 sm:space-y-6">
                {/* Mobile: Horizontal Scroll Tabs */}
                <div className="sm:hidden">
                  <div className="flex space-x-1 overflow-x-auto pb-2">
                    {[
                      { value: 'all', label: 'All', count: projectStats.total },
                      { value: 'active', label: 'Active', count: projectStats.active },
                      { value: 'in-progress', label: 'Progress', count: projectStats.inProgress },
                      { value: 'completed', label: 'Done', count: projectStats.completed },
                      { value: 'draft', label: 'Draft', count: projectStats.draft }
                    ].map((tab) => (
                      <button
                        key={tab.value}
                        onClick={() => setActiveTab(tab.value)}
                        className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                          activeTab === tab.value
                            ? 'bg-blue-600 text-white'
                            : 'bg-white text-slate-600 border border-slate-200 hover:bg-slate-50'
                        }`}
                      >
                        <span>{tab.label}</span>
                        <span className="text-xs opacity-75">({tab.count})</span>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Desktop: Grid Tabs */}
                <TabsList className="hidden sm:grid w-full grid-cols-5 bg-slate-100">
                  <TabsTrigger value="all" className="data-[state=active]:bg-white text-xs sm:text-sm">
                    All ({projectStats.total})
                  </TabsTrigger>
                  <TabsTrigger value="active" className="data-[state=active]:bg-white text-xs sm:text-sm">
                    Active ({projectStats.active})
                  </TabsTrigger>
                  <TabsTrigger value="in-progress" className="data-[state=active]:bg-white text-xs sm:text-sm">
                    In Progress ({projectStats.inProgress})
                  </TabsTrigger>
                  <TabsTrigger value="completed" className="data-[state=active]:bg-white text-xs sm:text-sm">
                    Completed ({projectStats.completed})
                  </TabsTrigger>
                  <TabsTrigger value="draft" className="data-[state=active]:bg-white text-xs sm:text-sm">
                    Draft ({projectStats.draft})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value={activeTab} className="space-y-0">
                  {filteredProjects.length === 0 ? (
                    <Card className="border-slate-200">
                      <CardContent className="p-8 text-center">
                        <Search className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                        <h3 className="text-lg font-medium text-slate-900 mb-2">No projects found</h3>
                        <p className="text-slate-600">Try adjusting your search or filter criteria</p>
                      </CardContent>
                    </Card>
                  ) : (
                    <div className={`grid gap-4 sm:gap-6 ${
                      viewMode === "grid"
                        ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                        : "grid-cols-1"
                    }`}>
                      {filteredProjects.map((project) => (
                        <ProjectDisplayCard
                          key={project.id}
                          project={project}
                          viewMode={viewMode}
                          onView={() => handleViewProject(project.id)}
                          onEdit={() => handleEditProject(project.id)}
                          onMessage={() => handleMessageContractor(project)}
                          onDelete={() => handleDeleteProject(project.id)}
                          formatBudget={formatBudget}
                          formatLocation={formatLocation}
                          formatDate={formatDate}
                          getStatusColor={getStatusColor}
                          getPriorityColor={getPriorityColor}
                        />
                      ))}
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            )}
          </>
        )}
      </div>
    </div>
  )
}
