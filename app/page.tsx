"use client"

import { useState, useRef, useEffect } from "react"
import { ArrowRight, Camera, Mic, MapPin, ChefHat, Bath, Hammer, Palette, DollarSign, Shield, Heart } from 'lucide-react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { Logo } from "@/components/logo"
import { useUser } from "@/contexts/user-context"
import { useRouter } from "next/navigation"
import { useToast } from "@/components/ui/toast-system"
import { TourTrigger } from "@/components/tour-trigger"
import Link from "next/link"

export default function HomePage() {
  const [projectDescription, setProjectDescription] = useState("")
  const [isFocused, setIsFocused] = useState(false)
  const [uploadedImages, setUploadedImages] = useState<File[]>([])
  const [location, setLocation] = useState("")
  const [isRecording, setIsRecording] = useState(false)
  const [isGettingLocation, setIsGettingLocation] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { user } = useUser()
  const router = useRouter()
  const { success, error, info } = useToast()

  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "auto"
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`
    }
  }, [projectDescription])

  // Auto-focus the textarea when the page loads
  useEffect(() => {
    const timer = setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus()
      }
    }, 100) // Small delay to ensure the page is fully loaded

    return () => clearTimeout(timer)
  }, [])

  const handleStartProject = () => {
    if (projectDescription.trim()) {
      const params = new URLSearchParams({
        description: projectDescription,
        ...(location && { location }),
        ...(uploadedImages.length > 0 && { hasImages: 'true' })
      })
      router.push(`/project/create?${params.toString()}`)
    }
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    if (files.length > 0) {
      const newImages = [...uploadedImages, ...files].slice(0, 5) // Limit to 5 images
      setUploadedImages(newImages)
      success(`${files.length} photo${files.length > 1 ? 's' : ''} added successfully`)

      if (files.length + uploadedImages.length > 5) {
        info("Maximum 5 photos allowed. Extra photos were not added.")
      }
    }
  }

  const removeImage = (index: number) => {
    setUploadedImages(prev => prev.filter((_, i) => i !== index))
  }

  const handleLocationRequest = () => {
    setIsGettingLocation(true)

    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          try {
            // Reverse geocoding to get address
            const response = await fetch(
              `https://api.bigdatacloud.net/data/reverse-geocode-client?latitude=${position.coords.latitude}&longitude=${position.coords.longitude}&localityLanguage=en`
            )

            if (!response.ok) {
              throw new Error(`HTTP error! status: ${response.status}`)
            }

            const data = await response.json()

            // Check if we got valid location data
            if (data && (data.city || data.locality || data.principalSubdivision)) {
              const city = data.city || data.locality || 'Unknown City'
              const state = data.principalSubdivision || data.countryName || 'Unknown State'
              const locationString = `${city}, ${state}`
              setLocation(locationString)
              success(`Location set to ${locationString}`)
            } else {
              // Fallback to coordinates if no readable address
              const coords = `${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`
              setLocation(coords)
              info(`Location set to coordinates: ${coords}`)
            }
          } catch (error) {
            // Better error handling
            const errorMessage = error instanceof Error ? error.message : 'Unknown error'
            console.warn('Geocoding failed:', errorMessage)

            // Fallback to coordinates
            const coords = `${position.coords.latitude.toFixed(4)}, ${position.coords.longitude.toFixed(4)}`
            setLocation(coords)
            info(`Location set to coordinates: ${coords}`)
          } finally {
            setIsGettingLocation(false)
          }
        },
        (geolocationError) => {
          const errorMessages = {
            1: 'Location access denied. Please enable location services.',
            2: 'Location unavailable. Please check your connection.',
            3: 'Location request timed out. Please try again.'
          }

          const errorCode = geolocationError.code as keyof typeof errorMessages
          const errorMessage = errorMessages[errorCode] || 'Unable to get your location.'

          console.warn('Geolocation error:', errorMessage)
          setIsGettingLocation(false)
          error(errorMessage)

          // Fallback to manual input
          setTimeout(() => {
            const manualLocation = prompt('Please enter your location (City, State):')
            if (manualLocation && manualLocation.trim()) {
              setLocation(manualLocation.trim())
              success(`Location set to ${manualLocation.trim()}`)
            }
          }, 100)
        }
      )
    } else {
      setIsGettingLocation(false)
      error('Geolocation not supported in your browser.')

      setTimeout(() => {
        const manualLocation = prompt('Please enter your location (City, State):')
        if (manualLocation && manualLocation.trim()) {
          setLocation(manualLocation.trim())
          success(`Location set to ${manualLocation.trim()}`)
        }
      }, 100)
    }
  }

  const handleVoiceInput = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = (window as any).webkitSpeechRecognition || (window as any).SpeechRecognition
      const recognition = new SpeechRecognition()

      recognition.continuous = false
      recognition.interimResults = false
      recognition.lang = 'en-US'

      recognition.onstart = () => {
        setIsRecording(true)
      }

      recognition.onresult = (event: any) => {
        const transcript = event.results[0][0].transcript
        setProjectDescription(prev => prev + (prev ? ' ' : '') + transcript)
        setIsRecording(false)
        success('Voice input added successfully')
      }

      recognition.onerror = () => {
        setIsRecording(false)
        error('Voice recognition failed. Please try again.')
      }

      recognition.onend = () => {
        setIsRecording(false)
      }

      recognition.start()
    } else {
      error('Speech recognition is not supported in your browser.')
    }
  }

  return (
    <div className="min-h-screen bg-white">
      <UnifiedNavigation />

      {/* Hero Section - Mobile-Native */}
      <div className="relative">
        <div className="container-native section-native-spacious">
          {/* Professional Hero - DriveTime Inspired */}
          <div className="text-center mb-8 sm:mb-12 lg:mb-16">
            <div className="space-y-6 sm:space-y-8">
              {/* Bold Professional Title */}
              <h1 data-tour="hero-title" className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold text-raisin-black leading-tight max-w-4xl mx-auto px-2">
                Renovate with{' '}
                <span className="bg-gradient-to-r from-philippine-green via-cerulean-blue to-philippine-green bg-clip-text text-transparent font-extrabold">
                  Trusted Professionals
                </span>
              </h1>

              <p className="text-base sm:text-lg md:text-xl text-raisin-black/70 max-w-3xl mx-auto leading-relaxed px-4 font-medium">
                Describe your project and get matched with verified contractors in your area
              </p>

            </div>
          </div>

          {/* Professional Project Input Section */}
          <div className="max-w-4xl mx-auto">
            <div data-tour="project-form" className={`bg-white/95 backdrop-blur-xl border border-slate-200/60 rounded-3xl p-8 sm:p-10 lg:p-12 shadow-xl transition-all duration-500 relative overflow-hidden ${isFocused ? "shadow-2xl border-philippine-green/30 transform scale-[1.02]" : ""}`}>
              {/* Subtle background gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-philippine-green/5 via-transparent to-cerulean-blue/5 opacity-50"></div>

              <div className="relative z-10">

              {/* Clean Textarea with Voice Input - Mobile-Native */}
              <div className="relative">
                <Textarea
                  ref={textareaRef}
                  placeholder="I want to transform my kitchen with modern cabinets, quartz countertops..."
                  value={projectDescription}
                  onChange={(e) => setProjectDescription(e.target.value)}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  className="min-h-[120px] sm:min-h-[140px] text-base border-2 border-slate-200 focus:border-philippine-green focus:ring-4 focus:ring-philippine-green/10 rounded-2xl resize-none pr-12 transition-all duration-300 hover:border-slate-300 focus:scale-[1.02]"
                  maxLength={500}
                  aria-label="Describe your renovation project"
                  aria-describedby="project-description-help"
                />

                {/* Voice Input Button - Curved design */}
                <button
                  data-tour="voice-input"
                  onClick={handleVoiceInput}
                  disabled={isRecording}
                  className={`absolute top-3 right-3 p-2.5 rounded-xl transition-all duration-300 touch-target-enhanced ${
                    isRecording
                      ? "bg-red-500 text-white animate-pulse shadow-lg"
                      : "bg-slate-100 text-slate-600 hover:bg-slate-200 hover:scale-110 shadow-md"
                  }`}
                  aria-label={isRecording ? "Recording voice input" : "Start voice input"}
                >
                  <Mic className="h-4 w-4" />
                </button>

                <div className="absolute bottom-2 right-3 text-xs text-slate-400" aria-live="polite">
                  <span className={projectDescription.length > 450 ? 'text-amber-600 font-medium' : ''}>{projectDescription.length}</span>/500
                </div>
                <div id="project-description-help" className="sr-only">
                  Describe your renovation project in detail. Maximum 500 characters.
                </div>
              </div>

              {/* Enhanced Photo preview - Mobile-optimized */}
              {uploadedImages.length > 0 && (
                <div className="mt-6 p-4 bg-slate-50 rounded-xl border border-slate-200">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-2">
                      <Camera className="h-4 w-4 text-slate-600" />
                      <span className="text-sm font-semibold text-slate-700">
                        {uploadedImages.length} photo{uploadedImages.length > 1 ? 's' : ''}
                      </span>
                    </div>
                    <span className="text-xs text-slate-500 bg-white px-2 py-1 rounded-md">
                      {5 - uploadedImages.length} remaining
                    </span>
                  </div>
                  <div className="grid grid-cols-3 sm:grid-cols-5 gap-3">
                    {uploadedImages.map((file, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`Upload ${index + 1}`}
                          className="w-full aspect-square object-cover rounded-lg border border-slate-200 shadow-sm"
                        />
                        <button
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 w-7 h-7 bg-red-500 hover:bg-red-600 text-white rounded-full text-sm font-bold opacity-100 sm:opacity-0 group-hover:opacity-100 transition-all duration-300 shadow-lg hover:shadow-xl touch-target-enhanced flex items-center justify-center hover:scale-110"
                          aria-label={`Remove photo ${index + 1}`}
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Enhanced Location display - Mobile-optimized */}
              {location && (
                <div className="mt-6 p-4 bg-green-50 rounded-xl border border-green-200">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                        <MapPin className="h-4 w-4 text-green-600" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-green-700">Location Set</p>
                        <p className="text-sm text-green-800 truncate">{location}</p>
                      </div>
                    </div>
                    <button
                      onClick={() => setLocation("")}
                      className="text-philippine-green hover:text-philippine-green/80 text-sm px-4 py-2 rounded-xl hover:bg-philippine-green/10 transition-all duration-300 font-semibold touch-target-enhanced flex-shrink-0 border border-philippine-green/20 hover:border-philippine-green/30"
                      aria-label="Change location"
                    >
                      Change
                    </button>
                  </div>
                </div>
              )}

              {/* Clean action buttons - Separate Mobile/Desktop */}
              <div className="mt-4 pt-4 border-t border-slate-100">
                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={handleImageUpload}
                  className="hidden"
                />

                {/* Enhanced Mobile Layout: Curved buttons */}
                <div className="flex sm:hidden items-center gap-4">
                  <button
                    data-tour="photos-button"
                    onClick={() => fileInputRef.current?.click()}
                    className={`flex items-center space-x-2 px-6 py-4 rounded-2xl border-2 text-sm font-semibold transition-all duration-300 flex-1 justify-center min-h-[52px] touch-target-enhanced hover:scale-105 ${
                      uploadedImages.length > 0
                        ? "bg-gradient-to-r from-philippine-green/10 to-philippine-green/5 text-philippine-green border-philippine-green/20 hover:from-philippine-green/20 hover:to-philippine-green/10 shadow-lg"
                        : "bg-slate-50/80 text-raisin-black/70 border-slate-200 hover:bg-slate-100/80 hover:border-slate-300 shadow-md"
                    }`}
                    aria-label={`Add photos${uploadedImages.length > 0 ? ` (${uploadedImages.length})` : ''}`}
                  >
                    <Camera className="h-4 w-4 flex-shrink-0" />
                    <span>Photos</span>
                    {uploadedImages.length > 0 && <span className="text-xs">({uploadedImages.length})</span>}
                  </button>

                  <button
                    data-tour="location-button"
                    onClick={handleLocationRequest}
                    disabled={isGettingLocation}
                    className={`flex items-center space-x-2 px-6 py-4 rounded-2xl border-2 text-sm font-semibold transition-all duration-300 flex-1 justify-center min-h-[52px] touch-target-enhanced hover:scale-105 ${
                      location
                        ? "bg-gradient-to-r from-philippine-green/10 to-philippine-green/5 text-philippine-green border-philippine-green/20 hover:from-philippine-green/20 hover:to-philippine-green/10 shadow-lg"
                        : "bg-slate-50/80 text-raisin-black/70 border-slate-200 hover:bg-slate-100/80 hover:border-slate-300 shadow-md"
                    } ${isGettingLocation ? 'opacity-75 animate-pulse' : ''}`}
                    aria-label={isGettingLocation ? 'Getting your location' : location ? 'Location set' : 'Add location'}
                  >
                    <MapPin className={`h-4 w-4 flex-shrink-0 ${isGettingLocation ? 'animate-spin' : ''}`} />
                    <span>{isGettingLocation ? 'Getting' : location ? 'Located' : 'Location'}</span>
                  </button>
                </div>

                {/* Desktop Layout: Curved buttons */}
                <div className="hidden sm:flex flex-col sm:flex-row items-stretch sm:items-center justify-between gap-4">
                  <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4">
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className={`flex items-center justify-center space-x-2 px-6 py-4 rounded-2xl border-2 transition-all duration-300 touch-target-enhanced text-sm font-semibold hover:scale-105 ${
                        uploadedImages.length > 0
                          ? "bg-gradient-to-r from-philippine-green/10 to-philippine-green/5 text-philippine-green border-philippine-green/20 hover:from-philippine-green/20 hover:to-philippine-green/10 shadow-lg"
                          : "bg-slate-50/80 text-raisin-black/70 border-slate-200 hover:bg-slate-100/80 hover:border-slate-300 shadow-md"
                      }`}
                      aria-label={`Add photos to your project${uploadedImages.length > 0 ? ` (${uploadedImages.length} added)` : ''}`}
                    >
                      <Camera className="h-4 w-4 flex-shrink-0" aria-hidden="true" />
                      <span>
                        Photos {uploadedImages.length > 0 && `(${uploadedImages.length})`}
                      </span>
                    </button>

                    <button
                      onClick={handleLocationRequest}
                      disabled={isGettingLocation}
                      className={`flex items-center justify-center space-x-2 px-6 py-4 rounded-2xl border-2 transition-all duration-300 touch-target-enhanced text-sm font-semibold hover:scale-105 ${
                        location
                          ? "bg-gradient-to-r from-philippine-green/10 to-philippine-green/5 text-philippine-green border-philippine-green/20 hover:from-philippine-green/20 hover:to-philippine-green/10 shadow-lg"
                          : "bg-slate-50/80 text-raisin-black/70 border-slate-200 hover:bg-slate-100/80 hover:border-slate-300 shadow-md"
                      } ${isGettingLocation ? 'opacity-75 animate-pulse' : ''}`}
                    >
                      <MapPin className={`h-4 w-4 flex-shrink-0 ${isGettingLocation ? 'animate-spin' : ''}`} />
                      <span>
                        {isGettingLocation ? 'Getting...' : location ? 'Located' : 'Location'}
                      </span>
                    </button>
                  </div>

                  {projectDescription && (
                    <Link href="/project/create" className="w-full sm:w-auto">
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-philippine-green to-philippine-green/90 hover:from-philippine-green/90 hover:to-philippine-green text-white shadow-xl hover:shadow-2xl transition-all duration-300 touch-target-enhanced hover:scale-105 w-full sm:w-auto"
                        aria-label="Start your renovation project"
                      >
                        <span>Get Free Quotes</span>
                        <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" aria-hidden="true" />
                      </Button>
                    </Link>
                  )}
                </div>

                {/* Mobile Action Button */}
                {projectDescription && (
                  <div className="flex sm:hidden mt-4">
                    <Link href="/project/create" className="w-full">
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-philippine-green to-philippine-green/90 hover:from-philippine-green/90 hover:to-philippine-green text-white shadow-xl hover:shadow-2xl transition-all duration-300 touch-target-enhanced hover:scale-105 w-full rounded-2xl"
                        aria-label="Start your renovation project"
                      >
                        <span>Get Free Quotes</span>
                        <ArrowRight className="h-4 w-4 ml-2 flex-shrink-0" aria-hidden="true" />
                      </Button>
                    </Link>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

          {/* Enhanced Get Inspired Section - Mobile-First */}
          {!projectDescription && (
            <div data-tour="get-inspired" className="mt-12 sm:mt-16 lg:mt-20 -mx-4 sm:-mx-6 lg:-mx-8 xl:-mx-16 2xl:-mx-24">
              {/* Full Width Faded Background */}
              <div className="bg-gradient-to-br from-slate-50/80 via-blue-50/40 to-slate-50/80 py-8 sm:py-12 lg:py-16">
                <div className="container-mobile">
                  <div className="text-center mb-8 sm:mb-10 lg:mb-12">
                    <h3 className="text-xl sm:text-2xl lg:text-3xl font-bold text-slate-900 mb-3 sm:mb-4 px-2">Get Inspired</h3>
                    <p className="text-base sm:text-lg text-slate-600 max-w-2xl mx-auto px-4">
                      Browse popular renovation projects or click any category to automatically fill your project description
                    </p>
                  </div>

              {/* Minimized Project Categories - Circular Design */}
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8 max-w-4xl mx-auto mb-8 sm:mb-12 lg:mb-16">
                {[
                  {
                    name: "Kitchen Renovation",
                    icon: ChefHat,
                    description: "Modern kitchens with premium finishes",
                    color: "from-orange-500 to-red-500",
                    bgColor: "bg-orange-50",
                    borderColor: "border-orange-200/60"
                  },
                  {
                    name: "Bathroom Remodel",
                    icon: Bath,
                    description: "Spa-like bathrooms with luxury touches",
                    color: "from-blue-500 to-cyan-500",
                    bgColor: "bg-blue-50",
                    borderColor: "border-blue-200/60"
                  },
                  {
                    name: "Flooring Installation",
                    icon: Hammer,
                    description: "Hardwood, tile, and luxury vinyl",
                    color: "from-amber-500 to-yellow-500",
                    bgColor: "bg-amber-50",
                    borderColor: "border-amber-200/60"
                  },
                  {
                    name: "House Painting",
                    icon: Palette,
                    description: "Interior and exterior painting services",
                    color: "from-purple-500 to-pink-500",
                    bgColor: "bg-purple-50",
                    borderColor: "border-purple-200/60"
                  }
                ].map((example, index) => (
                  <div key={index} className="flex flex-col items-center space-y-2">
                    <button
                      onClick={() => setProjectDescription(`I want to ${example.name.toLowerCase()} with ${example.description.toLowerCase()}`)}
                      className={`group w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 ${example.bgColor} border ${example.borderColor} rounded-full hover:shadow-xl hover:scale-[1.05] transition-all duration-300 hover:bg-white touch-target-enhanced relative overflow-hidden flex items-center justify-center`}
                    >
                      <div className={`w-12 h-12 lg:w-16 lg:h-16 bg-gradient-to-br ${example.color} rounded-full flex items-center justify-center shadow-lg group-hover:shadow-2xl group-hover:scale-110 transition-all duration-500 relative`}>
                        <example.icon className="h-6 w-6 lg:h-8 lg:w-8 text-white" />
                        <div className="absolute inset-0 bg-white/20 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                      </div>

                      {/* Subtle background pattern for circular design */}
                      <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300 flex items-center justify-center">
                        <example.icon className="w-16 h-16 lg:w-20 lg:h-20 text-slate-900" />
                      </div>
                    </button>
                    <h4 className="text-xs lg:text-sm font-bold text-slate-900 text-center leading-tight">{example.name}</h4>
                  </div>
                ))}
                </div>
                </div>
              </div>
            </div>
          )}

          {/* Professional Key Benefits */}
          <div className="text-center py-8 sm:py-12">
            <div className="flex flex-wrap items-center justify-center gap-6 sm:gap-8 text-sm sm:text-base">
              <div className="flex items-center space-x-2 sm:space-x-3">
                <DollarSign className="w-4 h-4 sm:w-5 sm:h-5 text-philippine-green flex-shrink-0" />
                <span className="text-raisin-black font-semibold">Free Quotes</span>
              </div>
              <div className="flex items-center space-x-2 sm:space-x-3">
                <Shield className="w-4 h-4 sm:w-5 sm:h-5 text-cerulean-blue flex-shrink-0" />
                <span className="text-raisin-black font-semibold">Verified Professionals</span>
              </div>
              <div className="flex items-center space-x-2 sm:space-x-3">
                <Heart className="w-4 h-4 sm:w-5 sm:h-5 text-purple-600 flex-shrink-0" />
                <span className="text-raisin-black font-semibold">No Commitment</span>
              </div>
            </div>
          </div>

          {/* CTA Section */}
          <div data-tour="cta-button" className="text-center py-8 sm:py-12">
            <Link href="/project/create" className="inline-block">
              <Button size="lg" className="bg-gradient-to-r from-philippine-green to-philippine-green/90 hover:from-philippine-green/90 hover:to-philippine-green text-white shadow-xl hover:shadow-2xl transition-all duration-300 touch-target-enhanced hover:scale-105">
                Start Your Project Free
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>

          {/* Enhanced How It Works Section */}
          <div data-tour="how-it-works" className="bg-white/80 backdrop-blur-sm rounded-2xl p-4 sm:p-6 md:p-8 border border-slate-200/60 max-w-6xl mx-auto shadow-lg">
                <div className="text-center mb-6 sm:mb-8">
                  <h4 className="text-xl sm:text-2xl font-bold text-slate-900 mb-3 sm:mb-4">How RenovHub Works</h4>
                  <p className="text-sm sm:text-base text-slate-600 max-w-2xl mx-auto">
                    Get started with your renovation project in four simple steps
                  </p>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 relative">
                  {[
                    {
                      step: "1",
                      title: "Describe Your Project",
                      description: "Tell us about your renovation vision, budget, and timeline",
                      color: "bg-blue-600"
                    },
                    {
                      step: "2",
                      title: "Get Matched",
                      description: "We connect you with verified contractors in your area",
                      color: "bg-purple-600"
                    },
                    {
                      step: "3",
                      title: "Accept Offer",
                      description: "Review proposals and choose the best contractor for your project",
                      color: "bg-orange-600"
                    },
                    {
                      step: "4",
                      title: "Start Building",
                      description: "Manage your project and bring your dream to life",
                      color: "bg-green-600"
                    }
                  ].map((step, index) => (
                    <div key={index} className="text-center relative">
                      <div className={`w-12 h-12 sm:w-14 sm:h-14 md:w-16 md:h-16 ${step.color} rounded-xl flex items-center justify-center text-white font-bold text-base sm:text-lg mx-auto mb-3 sm:mb-4 shadow-lg`}>
                        {step.step}
                      </div>
                      <h5 className="text-base sm:text-lg font-bold text-slate-900 mb-2 sm:mb-3">{step.title}</h5>
                      <p className="text-slate-600 leading-relaxed text-xs sm:text-sm">{step.description}</p>

                      {/* Stylish Faded Arrow */}
                      {index < 3 && (
                        <div className="hidden md:block absolute top-8 -right-3 transform translate-x-full">
                          <svg
                            className="w-6 h-6 text-slate-300"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M13 7l5 5m0 0l-5 5m5-5H6"
                              className="opacity-60"
                            />
                          </svg>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

          {/* Clean CTA Section */}
          <div className="text-center mt-16 mb-16">
            <div className="space-y-6">
              <h3 className="text-2xl lg:text-3xl font-bold text-slate-900">
                Ready to transform your space?
              </h3>
              <p className="text-lg text-slate-600 max-w-2xl mx-auto leading-relaxed">
                Start your renovation journey today with trusted professionals in your area. Get free quotes with no commitment.
              </p>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
                <Link href="/project/create">
                  <Button size="lg" className="bg-gradient-to-r from-philippine-green to-philippine-green/90 hover:from-philippine-green/90 hover:to-philippine-green text-white px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 touch-target-enhanced hover:scale-105 rounded-2xl">
                    Start Your Project Free
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Button>
                </Link>
                <Link href="/contractors">
                  <Button variant="outline" size="lg" className="bg-white/80 border-2 border-slate-200 hover:border-philippine-green/30 hover:bg-slate-50/80 px-10 py-4 transition-all duration-300 touch-target-enhanced hover:scale-105 rounded-2xl backdrop-blur-sm">
                    Browse Contractors
                  </Button>
                </Link>
              </div>
            </div>
          </div>

        </div>
      </div>

      <TourTrigger showWelcome={true} autoStart={false} />
    </div>
  )
}
