"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>lide<PERSON> } from "@/components/ui/slider"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { useContractors } from "@/hooks/use-contractors"
import { SearchIcon, Filter, MapPin, Star, CheckCircle, X, SlidersHorizontal, Loader2 } from "lucide-react"
import Link from "next/link"

interface SearchFilters {
  query: string
  category: string
  priceRange: [number, number]
  distance: number
  rating: number
  verified: boolean
}

interface Contractor {
  id: string
  name: string
  specialty: string
  rating: number
  reviewCount: number
  location: string
  distance: number
  price: number
  priceLabel: string
  verified: boolean
  image: string
}

export default function SearchPage() {
  const [isFilterOpen, setIsFilterOpen] = useState(false)
  const [localFilters, setLocalFilters] = useState<SearchFilters>({
    query: "",
    category: "all",
    priceRange: [0, 100000],
    distance: 25,
    rating: 0,
    verified: false,
  })

  // Use the real contractors hook
  const {
    contractors,
    loading,
    error,
    total,
    searchContractors,
    clearFilters
  } = useContractors({
    autoFetch: true,
    initialFilters: {
      search: localFilters.query,
      category: localFilters.category === 'all' ? [] : [localFilters.category],
      rating: localFilters.rating,
      verified: localFilters.verified ? true : undefined,
      sortBy: 'rating',
      sortOrder: 'desc'
    }
  })

  const categories = [
    { id: "all", label: "All Categories" },
    { id: "kitchen", label: "Kitchen Remodeling" },
    { id: "bathroom", label: "Bathroom Renovation" },
    { id: "flooring", label: "Flooring" },
    { id: "painting", label: "Painting" },
    { id: "electrical", label: "Electrical" },
    { id: "plumbing", label: "Plumbing" },
    { id: "landscaping", label: "Landscaping" },
  ]

  // Handle filter changes and trigger search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchContractors({
        search: localFilters.query,
        category: localFilters.category === 'all' ? [] : [localFilters.category],
        rating: localFilters.rating,
        verified: localFilters.verified ? true : undefined,
        sortBy: 'rating',
        sortOrder: 'desc'
      })
    }, 500) // Debounce search

    return () => clearTimeout(timeoutId)
  }, [localFilters, searchContractors])

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setLocalFilters((prev) => ({ ...prev, [key]: value }))
  }

  const handleClearFilters = () => {
    setLocalFilters({
      query: "",
      category: "all",
      priceRange: [0, 100000],
      distance: 25,
      rating: 0,
      verified: false,
    })
    clearFilters()
  }

  const formatPrice = (price: number) => {
    if (price >= 1000) {
      return `$${(price / 1000).toFixed(0)}K`
    }
    return `$${price}`
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-7xl mx-auto px-6 py-12">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-lg sm:text-xl lg:text-2xl font-light text-slate-900 mb-2">Find Contractors</h1>
          <p className="text-slate-500">Search for qualified professionals for your renovation project</p>
        </div>

        {/* Search Bar */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-1">
            <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search by contractor name, specialty, or keyword..."
              value={localFilters.query}
              onChange={(e) => handleFilterChange("query", e.target.value)}
              className="pl-10 border-slate-200 focus:border-slate-300 bg-white"
            />
          </div>
          <Button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            variant="outline"
            className="bg-white border-slate-200 hover:border-slate-300"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {Object.values(filters).some((value) => {
              if (typeof value === "boolean" && value) return true
              if (Array.isArray(value) && (value[0] > 0 || value[1] < 100000)) return true
              if (typeof value === "number" && value > 0 && value < 25) return true
              if (typeof value === "string" && value !== "" && value !== "all") return true
              return false
            }) && (
              <span className="ml-2 w-5 h-5 rounded-full bg-slate-900 text-white text-xs flex items-center justify-center">
                !
              </span>
            )}
          </Button>
        </div>

        {/* Filters Panel */}
        {isFilterOpen && (
          <div className="bg-white border border-slate-200 rounded-xl p-6 mb-8 shadow-sm animate-in">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-medium text-slate-900">Search Filters</h2>
              <Button variant="ghost" size="sm" onClick={clearFilters} className="text-slate-500 hover:text-slate-700">
                Clear All
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Category Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">Category</h3>
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => handleFilterChange("category", category.id)}
                      className={`px-3 py-1 rounded-full text-sm transition-colors ${
                        localFilters.category === category.id
                          ? "bg-slate-900 text-white"
                          : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                      }`}
                    >
                      {category.label}
                    </button>
                  ))}
                </div>
              </div>

              {/* Price Range Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">
                  Price Range: {formatPrice(localFilters.priceRange[0])} - {formatPrice(localFilters.priceRange[1])}
                </h3>
                <div className="px-2">
                  <Slider
                    defaultValue={[0, 100000]}
                    min={0}
                    max={100000}
                    step={1000}
                    value={localFilters.priceRange}
                    onValueChange={(value) => handleFilterChange("priceRange", value)}
                    className="mb-6"
                  />
                  <div className="flex justify-between text-xs text-slate-500">
                    <span>$0</span>
                    <span>$25K</span>
                    <span>$50K</span>
                    <span>$75K</span>
                    <span>$100K+</span>
                  </div>
                </div>
              </div>

              {/* Distance Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">Distance: {localFilters.distance} miles</h3>
                <div className="px-2">
                  <Slider
                    defaultValue={[25]}
                    min={1}
                    max={50}
                    step={1}
                    value={[localFilters.distance]}
                    onValueChange={(value) => handleFilterChange("distance", value[0])}
                    className="mb-6"
                  />
                  <div className="flex justify-between text-xs text-slate-500">
                    <span>1 mile</span>
                    <span>25 miles</span>
                    <span>50 miles</span>
                  </div>
                </div>
              </div>

              {/* Rating Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">Minimum Rating: {localFilters.rating}+ stars</h3>
                <div className="flex items-center space-x-2">
                  {[0, 3, 3.5, 4, 4.5, 5].map((rating) => (
                    <button
                      key={rating}
                      onClick={() => handleFilterChange("rating", rating)}
                      className={`px-3 py-1 rounded-lg text-sm transition-colors ${
                        localFilters.rating === rating
                          ? "bg-slate-900 text-white"
                          : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                      }`}
                    >
                      {rating === 0 ? "Any" : rating}
                    </button>
                  ))}
                </div>
              </div>

              {/* Verified Filter */}
              <div>
                <h3 className="text-sm font-medium text-slate-900 mb-3">Verification</h3>
                <button
                  onClick={() => handleFilterChange("verified", !localFilters.verified)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm transition-colors ${
                    localFilters.verified ? "bg-slate-900 text-white" : "bg-slate-100 text-slate-700 hover:bg-slate-200"
                  }`}
                >
                  <CheckCircle className="h-4 w-4" />
                  <span>Verified Contractors Only</span>
                </button>
              </div>
            </div>

            <div className="flex justify-end mt-8">
              <Button onClick={() => setIsFilterOpen(false)} className="bg-slate-900 hover:bg-slate-800 text-white">
                Apply Filters
              </Button>
            </div>
          </div>
        )}

        {/* Results Count & Sort */}
        <div className="flex items-center justify-between mb-6">
          <p className="text-slate-500">
            {loading ? "Searching..." : error ? "Error loading contractors" : `${contractors.length} contractors found`}
          </p>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-slate-500">Sort by:</span>
            <select className="text-sm border-none bg-transparent focus:outline-none text-slate-900 font-medium">
              <option>Relevance</option>
              <option>Rating: High to Low</option>
              <option>Distance: Near to Far</option>
              <option>Price: Low to High</option>
            </select>
          </div>
        </div>

        {/* Results Grid */}
        {isLoading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <div key={i} className="bg-white border border-slate-100 rounded-xl overflow-hidden animate-pulse">
                <div className="h-48 bg-slate-200" />
                <div className="p-6 space-y-4">
                  <div className="h-6 bg-slate-200 rounded w-3/4" />
                  <div className="h-4 bg-slate-200 rounded w-1/2" />
                  <div className="h-4 bg-slate-200 rounded w-full" />
                  <div className="h-10 bg-slate-200 rounded" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="bg-white border border-slate-100 rounded-xl p-12 text-center">
            <X className="h-12 w-12 text-red-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">Error loading contractors</h3>
            <p className="text-slate-500 mb-6">{error}</p>
            <Button onClick={() => window.location.reload()} variant="outline" className="bg-transparent border-slate-200">
              Try Again
            </Button>
          </div>
        ) : contractors.length === 0 ? (
          <div className="bg-white border border-slate-100 rounded-xl p-12 text-center">
            <X className="h-12 w-12 text-slate-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">No contractors found</h3>
            <p className="text-slate-500 mb-6">Try adjusting your filters or search terms</p>
            <Button onClick={handleClearFilters} variant="outline" className="bg-transparent border-slate-200">
              Clear All Filters
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {contractors.map((contractor) => (
              <div
                key={contractor.id}
                className="bg-white border border-slate-100 rounded-xl overflow-hidden hover:shadow-md transition-all duration-200"
              >
                {/* Image */}
                <div className="relative h-48 bg-slate-100 overflow-hidden">
                  <img
                    src={contractor.avatar_url || "/placeholder.svg"}
                    alt={contractor.business_name || contractor.users?.name}
                    className="w-full h-full object-cover"
                  />
                  {contractor.verified && (
                    <div className="absolute top-3 left-3 bg-slate-900 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </div>
                  )}
                  <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm text-slate-900 px-2 py-1 rounded-full text-xs font-medium">
                    {contractor.hourly_rate || 'Contact for pricing'}
                  </div>
                </div>

                {/* Content */}
                <div className="p-6">
                  <h3 className="font-medium text-slate-900 mb-1">
                    {contractor.business_name || contractor.users?.name}
                  </h3>
                  <p className="text-sm text-slate-600 mb-3">
                    {contractor.specialties?.[0] || contractor.category || 'General Contractor'}
                  </p>

                  <div className="flex items-center justify-between mb-4 text-sm">
                    <div className="flex items-center">
                      <Star className="h-3 w-3 text-yellow-400 fill-current mr-1" />
                      <span className="text-slate-700">
                        {contractor.rating || 'New'} ({contractor.total_reviews || 0})
                      </span>
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-3 w-3 text-slate-400 mr-1" />
                      <span className="text-slate-500">{contractor.location || 'Location'}</span>
                    </div>
                  </div>

                  <Link href={`/contractors/${contractor.id}`}>
                    <Button className="w-full bg-slate-900 hover:bg-slate-800 text-white">View Profile</Button>
                  </Link>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Mobile Filter Button */}
        <div className="md:hidden fixed bottom-6 right-6">
          <Button
            onClick={() => setIsFilterOpen(!isFilterOpen)}
            className="h-14 w-14 rounded-full bg-slate-900 hover:bg-slate-800 text-white shadow-lg"
          >
            <SlidersHorizontal className="h-6 w-6" />
          </Button>
        </div>
      </div>
    </div>
  )
}
