"use client"

import { useState, useEffect } from "react"
import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { CustomerRoute } from "@/components/route-guard"
import { RoleSwitcher } from "@/components/role-switcher"
import { <PERSON>readcrumb, PageHeader } from "@/components/breadcrumb"
import { useUser } from "@/contexts/user-context"
import { useContractors } from "@/hooks/use-contractors"
import { Search, MapPin, Star, Filter, Heart, MessageCircle, Eye, Hammer, Wrench, PaintBucket, Zap, CheckCircle } from "lucide-react"
import Link from "next/link"
import { LikeButton, VerticalActionButtons, RatingDisplay } from "@/components/ui/interactive-elements"
import { ContractorCardSkeleton } from "@/components/ui/loading-skeleton"
import { ProgressiveImage } from "@/components/ui/progressive-image"

interface Contractor {
  id: string
  name: string
  specialty: string
  rating: number
  reviewCount: number
  location: string
  priceRange: string
  verified: boolean
  responseTime: string
  completedProjects: number
  image: string
}

export default function ContractorsPage() {
  const { user } = useUser()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedFilter, setSelectedFilter] = useState("all")

  const {
    contractors,
    loading: isLoading,
    error,
    total,
    searchContractors
  } = useContractors({
    autoFetch: true,
    initialFilters: {
      limit: 12,
      sortBy: 'rating',
      sortOrder: 'desc'
    }
  })

  const filters = [
    { id: "all", label: "All Contractors" },
    { id: "kitchen", label: "Kitchen" },
    { id: "bathroom", label: "Bathroom" },
    { id: "flooring", label: "Flooring" },
    { id: "painting", label: "Painting" },
  ]

  // Handle search and filtering
  const handleSearch = React.useCallback((query: string) => {
    setSearchQuery(query)
    searchContractors({
      search: query,
      category: selectedFilter === "all" ? [] : [selectedFilter as any],
      page: 1
    })
  }, [selectedFilter, searchContractors])

  const handleFilterChange = React.useCallback((filterId: string) => {
    setSelectedFilter(filterId)
    searchContractors({
      search: searchQuery,
      category: filterId === "all" ? [] : [filterId as any],
      page: 1
    })
  }, [searchQuery, searchContractors])

  // Debounced search
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (searchQuery !== undefined) {
        handleSearch(searchQuery)
      }
    }, 300)
    return () => clearTimeout(timer)
  }, [searchQuery])

  return (
    <CustomerRoute>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />

      <div className="container-mobile section-mobile">
        {/* Breadcrumbs - Mobile-First */}
        <div className="mb-4 sm:mb-6">
          <Breadcrumb />
        </div>

        {/* Header - Mobile-First */}
        <PageHeader
          title="Find Contractors"
          description="Browse verified professionals in your area"
          className="mb-8 sm:mb-10 lg:mb-12"
        />

        {/* Search and Filters - Mobile-First */}
        <div className="mb-6 sm:mb-8 space-y-4 sm:space-y-6">
          <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" aria-hidden="true" />
              <Input
                placeholder="Search contractors or specialties..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="input-mobile pl-10"
                aria-label="Search contractors by name or specialty"
                role="searchbox"
              />
            </div>
            <Button
              variant="outline"
              className="btn-mobile-secondary justify-center"
              aria-label="Open filter options"
            >
              <Filter className="h-4 w-4 mr-2 flex-shrink-0" aria-hidden="true" />
              Filters
            </Button>
          </div>

          {/* Category Filters - Mobile-First Touch Targets */}
          <div className="flex flex-wrap gap-2" role="group" aria-label="Filter contractors by category">
            {filters.map((filter) => (
              <button
                key={filter.id}
                onClick={() => handleFilterChange(filter.id)}
                className={`px-3 sm:px-4 py-2.5 sm:py-3 rounded-full text-sm font-medium transition-colors touch-target-enhanced ${
                  selectedFilter === filter.id
                    ? "bg-slate-900 text-white"
                    : "bg-slate-100 text-slate-600 hover:bg-slate-200 active:bg-slate-300"
                }`}
                aria-pressed={selectedFilter === filter.id}
                aria-label={`Filter by ${filter.label}`}
              >
                {filter.label}
              </button>
            ))}
          </div>
        </div>

        {/* Results */}
        <div className="mb-6">
          <p className="text-slate-500" aria-live="polite" aria-atomic="true">
            {isLoading ? 'Searching...' : `${contractors.length} contractor${contractors.length !== 1 ? "s" : ""} found`}
            {total > contractors.length && ` (${total} total)`}
          </p>
        </div>

        {/* Contractors Grid - Enhanced Mobile Layout */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6" role="list" aria-label="Available contractors">
          {isLoading ? (
            // Loading skeleton
            Array.from({ length: 6 }).map((_, index) => (
              <ContractorCardSkeleton key={index} />
            ))
          ) : (
            contractors.map((contractor) => (
            <article
              key={contractor.id}
              className="card-native-minimal overflow-hidden group"
              role="listitem"
              aria-labelledby={`contractor-${contractor.id}-name`}
            >
              {/* Image - Mobile-Native */}
              <div className="relative h-40 sm:h-48 bg-slate-100 overflow-hidden">
                <ProgressiveImage
                  src={contractor.users?.avatar_url || "/placeholder.svg"}
                  alt={`${contractor.business_name || contractor.users?.name} - contractor`}
                  className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  width={300}
                  height={200}
                />

                {/* Verification Badge - Mobile-Native */}
                {contractor.users?.verified && (
                  <div
                    className="absolute bottom-2 right-2 bg-emerald-500 text-white p-1 sm:p-1.5 rounded-full shadow-lg"
                    aria-label="Verified contractor"
                    role="img"
                  >
                    <CheckCircle className="h-3 w-3 sm:h-4 sm:w-4" aria-hidden="true" />
                  </div>
                )}

                {/* Vertical Action Buttons - Mobile-Native */}
                <div className="absolute top-2 right-2">
                  <VerticalActionButtons
                    itemId={contractor.id}
                    itemType="contractor"
                  />
                </div>
              </div>

              {/* Content - Mobile-Native */}
              <div className="p-3 sm:p-4 lg:p-6">
                <div className="mb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 id={`contractor-${contractor.id}-name`} className="font-medium text-slate-900 mb-1">
                        {contractor.business_name || contractor.users?.name}
                      </h3>
                      <p className="text-sm text-slate-600">
                        {contractor.specialties && contractor.specialties.length > 0
                          ? contractor.specialties.join(', ')
                          : 'General Contractor'}
                      </p>
                    </div>

                    {/* Rating and Reviews - Using enhanced component */}
                    <RatingDisplay
                      rating={contractor.rating_average || 0}
                      reviewCount={contractor.rating_count || 0}
                      size="sm"
                      showCounts={true}
                    />
                  </div>
                </div>

                <div className="flex items-center mb-4 text-sm text-slate-500">
                  <MapPin className="h-4 w-4 text-slate-400 mr-2" />
                  {contractor.users?.location || 'Location not specified'}
                </div>

                <div className="space-y-2 mb-4 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-500">Tier:</span>
                    <span className="font-medium text-slate-900 capitalize">{contractor.tier || 'Basic'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-500">Response Time:</span>
                    <span className="font-medium text-slate-900">
                      {contractor.response_time ? `< ${contractor.response_time}h` : '< 24h'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-500">Completion Rate:</span>
                    <span className="font-medium text-slate-900">
                      {contractor.completion_rate ? `${contractor.completion_rate}%` : 'N/A'}
                    </span>
                  </div>
                </div>

                {/* Actions - Vertical layout with enhanced mobile touch targets */}
                <div className="space-y-2">
                  <Link href={`/contractors/${contractor.id}`} className="block">
                    <Button
                      variant="outline"
                      size="sm"
                      className="w-full bg-transparent border-slate-200 hover:border-slate-300 h-10 touch-target-enhanced"
                    >
                      <Eye className="h-4 w-4 mr-2" />
                      View Profile
                    </Button>
                  </Link>
                  <Button size="sm" className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white h-10 touch-target-enhanced hover:scale-105 transition-all duration-200">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Get Free Quote
                  </Button>
                </div>
              </div>
            </article>
            ))
          )}
        </div>

        {/* Enhanced Empty State */}
        {!isLoading && contractors.length === 0 && (
          <div className="text-center py-16">
            <div className="text-slate-400 mb-6">
              <Search className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-xl font-semibold text-slate-900 mb-3">No contractors match your criteria</h3>
            <p className="text-slate-600 mb-8 max-w-md mx-auto">
              We couldn't find any contractors matching your search. Try broadening your criteria or explore all available professionals.
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Button
                onClick={() => {
                  setSearchQuery("")
                  setSelectedFilter("all")
                }}
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3"
              >
                View All Contractors
              </Button>
              <Link href="/project/create">
                <Button variant="outline" className="px-6 py-3">
                  Post Your Project Instead
                </Button>
              </Link>
            </div>
          </div>
        )}
      </div>

      </div>
    </CustomerRoute>
  )
}
