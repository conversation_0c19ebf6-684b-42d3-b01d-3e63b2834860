"use client"

import { useState, useRef, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { Heart, X, Star, MapPin, MessageCircle, ArrowLeft, RotateCcw } from "lucide-react"
import Link from "next/link"

interface Contractor {
  id: string
  name: string
  specialty: string
  rating: number
  reviewCount: number
  location: string
  image: string
  bio: string
  priceRange: string
  responseTime: string
}

export default function SwipePage() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [liked, setLiked] = useState<string[]>([])
  const [passed, setPassed] = useState<string[]>([])
  const [isAnimating, setIsAnimating] = useState(false)
  const cardRef = useRef<HTMLDivElement>(null)

  const [contractors, setContractors] = useState<Contractor[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const loadContractors = async () => {
      try {
        setLoading(true)
        // TODO: Replace with actual contractor service call
        // const response = await contractorService.searchContractors({
        //   verified: true,
        //   sortBy: 'rating',
        //   sortOrder: 'desc',
        //   limit: 10
        // })

        // For now, use empty array until database integration is complete
        setContractors([])
        setLoading(false)
      } catch (error) {
        console.error("Error loading contractors:", error)
        setContractors([])
        setLoading(false)
      }
    }

    loadContractors()
  }, [])

  const currentContractor = contractors[currentIndex]

  const handleSwipe = (direction: "left" | "right") => {
    if (isAnimating || !currentContractor) return

    setIsAnimating(true)

    if (direction === "right") {
      setLiked((prev) => [...prev, currentContractor.id])
    } else {
      setPassed((prev) => [...prev, currentContractor.id])
    }

    // Animate card out
    if (cardRef.current) {
      cardRef.current.style.transform = `translateX(${direction === "right" ? "100%" : "-100%"}) rotate(${direction === "right" ? "15deg" : "-15deg"})`
      cardRef.current.style.opacity = "0"
    }

    setTimeout(() => {
      setCurrentIndex((prev) => prev + 1)
      setIsAnimating(false)

      // Reset card position
      if (cardRef.current) {
        cardRef.current.style.transform = "translateX(0) rotate(0)"
        cardRef.current.style.opacity = "1"
      }
    }, 300)
  }

  const handleUndo = () => {
    if (currentIndex === 0) return

    const prevIndex = currentIndex - 1
    const prevContractor = contractors[prevIndex]

    setCurrentIndex(prevIndex)
    setLiked((prev) => prev.filter((id) => id !== prevContractor.id))
    setPassed((prev) => prev.filter((id) => id !== prevContractor.id))
  }

  if (currentIndex >= contractors.length) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="max-w-md mx-auto px-6 py-12 text-center">
          <div className="bg-white border border-slate-100 rounded-xl p-12 shadow-sm">
            <Heart className="h-16 w-16 text-slate-300 mx-auto mb-6" />
            <h2 className="text-2xl font-light text-slate-900 mb-4">All done!</h2>
            <p className="text-slate-500 mb-8">
              You've reviewed all available contractors. Check your matches or browse more.
            </p>
            <div className="space-y-3">
              <Link href="/contractors">
                <Button className="w-full bg-slate-900 hover:bg-slate-800 text-white">Browse All Contractors</Button>
              </Link>
              <Button
                onClick={() => {
                  setCurrentIndex(0)
                  setLiked([])
                  setPassed([])
                }}
                variant="outline"
                className="w-full bg-transparent border-slate-200"
              >
                Start Over
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-md mx-auto px-6 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <Link href="/contractors">
            <Button variant="ghost" size="sm" className="text-slate-500 hover:text-slate-700 p-2">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>

          <div className="text-center">
            <h1 className="text-lg font-medium text-slate-900">Find Contractors</h1>
            <p className="text-sm text-slate-500">
              {currentIndex + 1} of {contractors.length}
            </p>
          </div>

          <Button
            onClick={handleUndo}
            variant="ghost"
            size="sm"
            className="text-slate-500 hover:text-slate-700 p-2"
            disabled={currentIndex === 0}
          >
            <RotateCcw className="h-4 w-4" />
          </Button>
        </div>

        {/* Progress Bar */}
        <div className="w-full bg-slate-200 rounded-full h-1 mb-8">
          <div
            className="bg-slate-900 h-1 rounded-full transition-all duration-300"
            style={{ width: `${((currentIndex + 1) / contractors.length) * 100}%` }}
          />
        </div>

        {/* Card */}
        <div className="relative h-[600px] mb-8">
          <div
            ref={cardRef}
            className="absolute inset-0 bg-white border border-slate-100 rounded-2xl shadow-lg overflow-hidden transition-all duration-300 ease-out"
            style={{ transform: "translateX(0) rotate(0)", opacity: 1 }}
          >
            {/* Image */}
            <div className="h-64 bg-slate-100 overflow-hidden">
              <img
                src={currentContractor?.image || "/placeholder.svg"}
                alt={currentContractor?.name}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Content */}
            <div className="p-6">
              <div className="mb-4">
                <h2 className="text-xl font-medium text-slate-900 mb-1">{currentContractor?.name}</h2>
                <p className="text-slate-600">{currentContractor?.specialty}</p>
              </div>

              <div className="flex items-center space-x-4 mb-4 text-sm text-slate-500">
                <div className="flex items-center">
                  <Star className="h-4 w-4 text-yellow-400 mr-1 fill-current" />
                  {currentContractor?.rating} ({currentContractor?.reviewCount})
                </div>
                <div className="flex items-center">
                  <MapPin className="h-4 w-4 mr-1" />
                  {currentContractor?.location}
                </div>
              </div>

              <p className="text-slate-700 leading-relaxed mb-6">{currentContractor?.bio}</p>

              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-500">Price Range:</span>
                  <span className="font-medium text-slate-900">{currentContractor?.priceRange}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">Response Time:</span>
                  <span className="font-medium text-slate-900">{currentContractor?.responseTime}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-center space-x-6">
          <Button
            onClick={() => handleSwipe("left")}
            disabled={isAnimating}
            className="w-16 h-16 rounded-full bg-white border-2 border-slate-200 hover:border-red-300 hover:bg-red-50 text-slate-600 hover:text-red-600 shadow-lg"
          >
            <X className="h-6 w-6" />
          </Button>

          <Button variant="outline" className="bg-white border-slate-200 hover:border-slate-300 shadow-sm">
            <MessageCircle className="h-4 w-4 mr-2" />
            Message
          </Button>

          <Button
            onClick={() => handleSwipe("right")}
            disabled={isAnimating}
            className="w-16 h-16 rounded-full bg-white border-2 border-slate-200 hover:border-green-300 hover:bg-green-50 text-slate-600 hover:text-green-600 shadow-lg"
          >
            <Heart className="h-6 w-6" />
          </Button>
        </div>

        {/* Instructions */}
        <div className="text-center mt-8">
          <p className="text-sm text-slate-500">Swipe right to like • Swipe left to pass</p>
        </div>
      </div>
    </div>
  )
}
