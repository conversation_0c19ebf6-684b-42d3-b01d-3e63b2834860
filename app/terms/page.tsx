"use client"

import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { ArrowLeft } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function TermsPage() {
  const { user } = useUser()

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-8">
          <Link href="/login">
            <Button variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h1 className="text-3xl font-light text-slate-900 mb-2">Terms of Service</h1>
          <p className="text-slate-500">Last updated: December 15, 2024</p>
        </div>

        <EnhancedCard className="p-8">
          <div className="prose prose-slate max-w-none">
            <h2>1. Acceptance of Terms</h2>
            <p>
              By accessing and using RenovHub ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
            </p>

            <h2>2. Description of Service</h2>
            <p>
              RenovHub is a platform that connects homeowners with verified contractors for home renovation and improvement projects. We provide tools for project posting, contractor discovery, bidding, communication, and project management.
            </p>

            <h2>3. User Accounts</h2>
            <h3>3.1 Registration</h3>
            <p>
              To use certain features of the Service, you must register for an account. You agree to provide accurate, current, and complete information during the registration process and to update such information to keep it accurate, current, and complete.
            </p>
            
            <h3>3.2 Account Security</h3>
            <p>
              You are responsible for safeguarding the password and for maintaining the confidentiality of your account. You agree not to disclose your password to any third party and to take sole responsibility for any activities or actions under your account.
            </p>

            <h2>4. User Conduct</h2>
            <p>You agree not to use the Service to:</p>
            <ul>
              <li>Post false, inaccurate, misleading, defamatory, or libelous content</li>
              <li>Impersonate any person or entity or falsely state or misrepresent your affiliation</li>
              <li>Engage in any unlawful activities or encourage others to do so</li>
              <li>Violate any applicable local, state, national, or international law</li>
              <li>Transmit any material that contains viruses or other harmful computer code</li>
            </ul>

            <h2>5. Contractor Verification</h2>
            <p>
              While we strive to verify contractor credentials, licenses, and insurance, we do not guarantee the accuracy of all information provided. Users should conduct their own due diligence before entering into any agreements.
            </p>

            <h2>6. Payment Terms</h2>
            <h3>6.1 Service Fees</h3>
            <p>
              RenovHub charges service fees for certain transactions. These fees will be clearly disclosed before any transaction is completed.
            </p>
            
            <h3>6.2 Payment Processing</h3>
            <p>
              Payments are processed through third-party payment processors. By using our payment services, you agree to the terms and conditions of our payment processors.
            </p>

            <h2>7. Dispute Resolution</h2>
            <p>
              In the event of disputes between users, RenovHub may provide mediation services but is not obligated to resolve disputes. Users are encouraged to resolve disputes directly with each other.
            </p>

            <h2>8. Limitation of Liability</h2>
            <p>
              RenovHub shall not be liable for any indirect, incidental, special, consequential, or punitive damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses.
            </p>

            <h2>9. Privacy Policy</h2>
            <p>
              Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices.
            </p>

            <h2>10. Modifications to Terms</h2>
            <p>
              RenovHub reserves the right to modify these terms at any time. We will notify users of any material changes via email or through the Service. Continued use of the Service after such modifications constitutes acceptance of the updated terms.
            </p>

            <h2>11. Termination</h2>
            <p>
              Either party may terminate this agreement at any time. RenovHub reserves the right to suspend or terminate accounts that violate these terms or engage in prohibited activities.
            </p>

            <h2>12. Governing Law</h2>
            <p>
              These terms shall be governed by and construed in accordance with the laws of the State of California, without regard to its conflict of law provisions.
            </p>

            <h2>13. Contact Information</h2>
            <p>
              If you have any questions about these Terms of Service, please contact us at:
            </p>
            <p>
              Email: <EMAIL><br />
              Address: 123 Innovation Drive, San Francisco, CA 94102<br />
              Phone: (*************
            </p>

            <hr className="my-8" />
            
            <p className="text-sm text-slate-500">
              By using RenovHub, you acknowledge that you have read, understood, and agree to be bound by these Terms of Service.
            </p>
          </div>
        </EnhancedCard>
      </div>

      <RoleSwitcher />
    </div>
  )
}
