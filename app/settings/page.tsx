"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Shield, Bell, CreditCard, User, Trash2, Eye, EyeOff, Save, Camera } from "lucide-react"

export default function SettingsPage() {
  const { user } = useUser()
  const [activeTab, setActiveTab] = useState("account")
  const [showPassword, setShowPassword] = useState(false)
  const [settings, setSettings] = useState({
    // Account
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",

    // Notifications
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    marketingEmails: false,
    projectUpdates: true,
    bidAlerts: true,
    messageAlerts: true,

    // Privacy
    profileVisibility: "public",
    showEmail: false,
    showPhone: false,
    dataSharing: false,
    analytics: true,
  })

  const tabs = [
    { id: "account", label: "Account", icon: User },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "privacy", label: "Privacy & Security", icon: Shield },
    { id: "billing", label: "Billing", icon: CreditCard },
  ]

  const handleSave = () => {
    // Save settings logic
    console.log("Settings saved:", settings)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-lg sm:text-xl lg:text-2xl font-light text-slate-900 mb-2">Settings</h1>
          <p className="text-slate-500">Manage your account preferences and privacy settings</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white border border-slate-100 rounded-xl p-4 shadow-sm">
              <div className="space-y-2">
                {tabs.map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`w-full flex items-center space-x-3 p-3 rounded-lg text-left transition-colors ${
                      activeTab === tab.id ? "bg-slate-900 text-white" : "hover:bg-slate-50 text-slate-700"
                    }`}
                  >
                    <tab.icon className="h-5 w-5" />
                    <span className="font-medium">{tab.label}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            <div className="bg-white border border-slate-100 rounded-xl shadow-sm">
              {/* Account Tab */}
              {activeTab === "account" && (
                <div className="p-8">
                  <h2 className="text-xl font-medium text-slate-900 mb-6">Account Settings</h2>

                  <div className="space-y-8">
                    <div>
                      <h3 className="font-medium text-slate-900 mb-4">Change Password</h3>
                      <div className="space-y-4 max-w-md">
                        <div>
                          <Label htmlFor="current-password">Current Password</Label>
                          <div className="relative mt-1">
                            <Input
                              id="current-password"
                              type={showPassword ? "text" : "password"}
                              value={settings.currentPassword}
                              onChange={(e) => setSettings((prev) => ({ ...prev, currentPassword: e.target.value }))}
                              className="border-slate-200 focus:border-slate-300 pr-10"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                            >
                              {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </button>
                          </div>
                        </div>

                        <div>
                          <Label htmlFor="new-password">New Password</Label>
                          <Input
                            id="new-password"
                            type="password"
                            value={settings.newPassword}
                            onChange={(e) => setSettings((prev) => ({ ...prev, newPassword: e.target.value }))}
                            className="border-slate-200 focus:border-slate-300 mt-1"
                          />
                        </div>

                        <div>
                          <Label htmlFor="confirm-password">Confirm New Password</Label>
                          <Input
                            id="confirm-password"
                            type="password"
                            value={settings.confirmPassword}
                            onChange={(e) => setSettings((prev) => ({ ...prev, confirmPassword: e.target.value }))}
                            className="border-slate-200 focus:border-slate-300 mt-1"
                          />
                        </div>

                        <Button className="bg-slate-900 hover:bg-slate-800 text-white">Update Password</Button>
                      </div>
                    </div>

                    <div className="border-t border-slate-100 pt-8">
                      <h3 className="font-medium text-slate-900 mb-4 text-red-600">Danger Zone</h3>
                      <div className="p-4 border border-red-200 rounded-lg bg-red-50">
                        <h4 className="font-medium text-red-900 mb-2">Delete Account</h4>
                        <p className="text-sm text-red-700 mb-4">
                          Once you delete your account, there is no going back. Please be certain.
                        </p>
                        <Button
                          variant="outline"
                          className="border-red-300 text-red-700 hover:bg-red-50 bg-transparent"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete Account
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Notifications Tab */}
              {activeTab === "notifications" && (
                <div className="p-8">
                  <h2 className="text-xl font-medium text-slate-900 mb-6">Notification Preferences</h2>

                  <div className="space-y-8">
                    <div>
                      <h3 className="font-medium text-slate-900 mb-4">General Notifications</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Email Notifications</p>
                            <p className="text-sm text-slate-500">Receive notifications via email</p>
                          </div>
                          <Switch
                            checked={settings.emailNotifications}
                            onCheckedChange={(checked) =>
                              setSettings((prev) => ({ ...prev, emailNotifications: checked }))
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Push Notifications</p>
                            <p className="text-sm text-slate-500">Receive push notifications in your browser</p>
                          </div>
                          <Switch
                            checked={settings.pushNotifications}
                            onCheckedChange={(checked) =>
                              setSettings((prev) => ({ ...prev, pushNotifications: checked }))
                            }
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">SMS Notifications</p>
                            <p className="text-sm text-slate-500">Receive important updates via text</p>
                          </div>
                          <Switch
                            checked={settings.smsNotifications}
                            onCheckedChange={(checked) =>
                              setSettings((prev) => ({ ...prev, smsNotifications: checked }))
                            }
                          />
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-slate-100 pt-8">
                      <h3 className="font-medium text-slate-900 mb-4">Project Notifications</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Project Updates</p>
                            <p className="text-sm text-slate-500">Milestone completions and progress updates</p>
                          </div>
                          <Switch
                            checked={settings.projectUpdates}
                            onCheckedChange={(checked) => setSettings((prev) => ({ ...prev, projectUpdates: checked }))}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Bid Alerts</p>
                            <p className="text-sm text-slate-500">New bids on your projects</p>
                          </div>
                          <Switch
                            checked={settings.bidAlerts}
                            onCheckedChange={(checked) => setSettings((prev) => ({ ...prev, bidAlerts: checked }))}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Message Alerts</p>
                            <p className="text-sm text-slate-500">New messages from contractors</p>
                          </div>
                          <Switch
                            checked={settings.messageAlerts}
                            onCheckedChange={(checked) => setSettings((prev) => ({ ...prev, messageAlerts: checked }))}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-slate-100 pt-8">
                      <h3 className="font-medium text-slate-900 mb-4">Marketing</h3>
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-slate-900">Marketing Emails</p>
                          <p className="text-sm text-slate-500">Tips, trends, and special offers</p>
                        </div>
                        <Switch
                          checked={settings.marketingEmails}
                          onCheckedChange={(checked) => setSettings((prev) => ({ ...prev, marketingEmails: checked }))}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Privacy Tab */}
              {activeTab === "privacy" && (
                <div className="p-8">
                  <h2 className="text-xl font-medium text-slate-900 mb-6">Privacy & Security</h2>

                  <div className="space-y-8">
                    <div>
                      <h3 className="font-medium text-slate-900 mb-4">Profile Visibility</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Show Email Address</p>
                            <p className="text-sm text-slate-500">Allow contractors to see your email</p>
                          </div>
                          <Switch
                            checked={settings.showEmail}
                            onCheckedChange={(checked) => setSettings((prev) => ({ ...prev, showEmail: checked }))}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Show Phone Number</p>
                            <p className="text-sm text-slate-500">Allow contractors to see your phone</p>
                          </div>
                          <Switch
                            checked={settings.showPhone}
                            onCheckedChange={(checked) => setSettings((prev) => ({ ...prev, showPhone: checked }))}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-slate-100 pt-8">
                      <h3 className="font-medium text-slate-900 mb-4">Data & Analytics</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Data Sharing</p>
                            <p className="text-sm text-slate-500">Share anonymized data to improve our service</p>
                          </div>
                          <Switch
                            checked={settings.dataSharing}
                            onCheckedChange={(checked) => setSettings((prev) => ({ ...prev, dataSharing: checked }))}
                          />
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-slate-900">Analytics</p>
                            <p className="text-sm text-slate-500">Help us improve by sharing usage analytics</p>
                          </div>
                          <Switch
                            checked={settings.analytics}
                            onCheckedChange={(checked) => setSettings((prev) => ({ ...prev, analytics: checked }))}
                          />
                        </div>
                      </div>
                    </div>

                    <div className="border-t border-slate-100 pt-8">
                      <h3 className="font-medium text-slate-900 mb-4">Data Export</h3>
                      <p className="text-sm text-slate-500 mb-4">
                        Download a copy of your data including projects, messages, and account information.
                      </p>
                      <Button variant="outline" className="bg-transparent border-slate-200 hover:border-slate-300">
                        Export My Data
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Billing Tab */}
              {activeTab === "billing" && (
                <div className="p-8">
                  <h2 className="text-xl font-medium text-slate-900 mb-6">Billing & Payments</h2>

                  <div className="space-y-8">
                    <div>
                      <h3 className="font-medium text-slate-900 mb-4">Payment Methods</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-slate-100 rounded-lg flex items-center justify-center">
                              <CreditCard className="h-5 w-5 text-slate-600" />
                            </div>
                            <div>
                              <p className="font-medium text-slate-900">•••• •••• •••• 4242</p>
                              <p className="text-sm text-slate-500">Expires 12/25</p>
                            </div>
                          </div>
                          <div className="flex space-x-2">
                            <Button size="sm" variant="outline" className="bg-transparent border-slate-200">
                              Edit
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="bg-transparent border-slate-200 text-red-600"
                            >
                              Remove
                            </Button>
                          </div>
                        </div>

                        <Button variant="outline" className="bg-transparent border-slate-200 hover:border-slate-300">
                          Add Payment Method
                        </Button>
                      </div>
                    </div>

                    <div className="border-t border-slate-100 pt-8">
                      <h3 className="font-medium text-slate-900 mb-4">Billing History</h3>
                      <div className="space-y-3">
                        {[
                          {
                            date: "Dec 1, 2024",
                            amount: "$750.00",
                            description: "Kitchen Remodel - Milestone 3",
                            status: "Paid",
                          },
                          {
                            date: "Nov 20, 2024",
                            amount: "$500.00",
                            description: "Kitchen Remodel - Milestone 2",
                            status: "Paid",
                          },
                          {
                            date: "Nov 15, 2024",
                            amount: "$300.00",
                            description: "Kitchen Remodel - Milestone 1",
                            status: "Paid",
                          },
                        ].map((transaction, index) => (
                          <div
                            key={index}
                            className="flex items-center justify-between p-4 border border-slate-100 rounded-lg"
                          >
                            <div>
                              <p className="font-medium text-slate-900">{transaction.description}</p>
                              <p className="text-sm text-slate-500">{transaction.date}</p>
                            </div>
                            <div className="text-right">
                              <p className="font-medium text-slate-900">{transaction.amount}</p>
                              <p className="text-sm text-green-600">{transaction.status}</p>
                            </div>
                          </div>
                        ))}
                      </div>

                      <Button variant="ghost" size="sm" className="w-full mt-4 text-slate-500 hover:text-slate-700">
                        View All Transactions
                      </Button>
                    </div>
                  </div>
                </div>
              )}

              {/* Save Button */}
              <div className="border-t border-slate-100 p-6">
                <div className="flex justify-end">
                  <Button onClick={handleSave} className="bg-slate-900 hover:bg-slate-800 text-white">
                    Save Changes
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <RoleSwitcher />
    </div>
  )
}
