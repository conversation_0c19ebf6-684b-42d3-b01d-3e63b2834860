"use client"

import React, { useState, useEffect } from 'react'
import { UnifiedNavigation } from '@/components/unified-navigation'
import { ConversationList } from '@/components/messaging/conversation-list'
import { MessageThread } from '@/components/messaging/message-thread'
import { MessagingErrorBoundary } from '@/components/messaging/messaging-error-boundary'
import { useUser } from '@/contexts/user-context'
import { useMessaging } from '@/hooks/use-messaging'
import { useRealtimeMessaging } from '@/hooks/use-realtime-messaging'
import { useProjects } from '@/hooks/use-projects'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { MessageCircle, Plus, Loader2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

export default function MessagesPage() {
  const { user } = useUser()
  const { toast } = useToast()
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null)
  const [showNewConversationDialog, setShowNewConversationDialog] = useState(false)
  const [selectedProjectId, setSelectedProjectId] = useState<string>('')

  const {
    conversations,
    messages,
    currentConversation,
    loading,
    error,
    createConversation,
    sendMessage,
    markAsRead,
    selectConversation,
    refetch
  } = useMessaging({
    userId: user?.id || '',
    autoFetch: !!user
  })

  // Get user's projects for conversation creation
  const { projects, loading: projectsLoading } = useProjects({
    autoFetch: true,
    includeCompleted: false
  })

  // Initialize real-time messaging for selected conversation
  useRealtimeMessaging({
    conversationId: selectedConversationId || undefined
  })

  // Handle URL parameters for direct conversation access
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search)
    const conversationParam = urlParams.get('conversation')
    if (conversationParam && conversationParam !== selectedConversationId) {
      setSelectedConversationId(conversationParam)
      selectConversation(conversationParam)
    }
  }, [selectedConversationId, selectConversation])

  // Handle conversation selection
  const handleSelectConversation = (conversationId: string) => {
    setSelectedConversationId(conversationId)
    selectConversation(conversationId)
  }

  // Handle creating new conversation
  const handleCreateConversation = async () => {
    if (!selectedProjectId || !user) {
      toast({
        title: "Error",
        description: "Please select a project",
        variant: "destructive"
      })
      return
    }

    try {
      // Find the selected project to get contractor information
      const selectedProject = projects.find(p => p.id === selectedProjectId)
      if (!selectedProject) {
        toast({
          title: "Error",
          description: "Selected project not found",
          variant: "destructive"
        })
        return
      }

      // Determine participants based on user role and project status
      let participants = [user.id]

      if (user.role === 'customer' && selectedProject.contractor_id) {
        // Customer starting conversation with assigned contractor
        participants.push(selectedProject.contractor_id)
      } else if (user.role === 'pro' && selectedProject.customer_id) {
        // Contractor starting conversation with customer
        participants.push(selectedProject.customer_id)
      }

      if (participants.length < 2) {
        toast({
          title: "Error",
          description: "Cannot create conversation: missing participant information",
          variant: "destructive"
        })
        return
      }

      const conversationId = await createConversation(selectedProjectId, participants)
      if (conversationId) {
        setSelectedConversationId(conversationId)
        setShowNewConversationDialog(false)
        setSelectedProjectId('')
        toast({
          title: "Success",
          description: "Conversation created successfully"
        })
      }
    } catch (error) {
      console.error('Error creating conversation:', error)
      toast({
        title: "Error",
        description: "Failed to create conversation",
        variant: "destructive"
      })
    }
  }

  // Handle sending messages
  const handleSendMessage = async (content: string, attachments?: File[]) => {
    if (!currentConversation) return false

    try {
      const success = await sendMessage(content, attachments)
      if (success) {
        // Mark message as read immediately for current user
        await markAsRead(currentConversation.id)
      }
      return success
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send message",
        variant: "destructive"
      })
      return false
    }
  }

  // Handle marking messages as read
  const handleMarkAsRead = async (_messageId: string) => {
    if (currentConversation) {
      await markAsRead(currentConversation.id)
    }
  }

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-red-300" />
            <p className="text-red-500 mb-4">{error}</p>
            <Button onClick={refetch} variant="outline">
              Try Again
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Show login required state
  if (!user) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="flex items-center justify-center h-96">
          <div className="text-center">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 text-slate-300" />
            <h3 className="text-lg font-medium text-slate-900 mb-2">
              Authentication Required
            </h3>
            <p className="text-slate-500 mb-4">
              Please log in to access your messages
            </p>
            <Button onClick={() => window.location.href = '/login'}>
              Sign In
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      <UnifiedNavigation />
      <div className="container-native section-native">
        <div className="flex flex-col gap-4 sm:gap-6 mb-6 sm:mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="space-y-2">
              <h1 className="text-2xl sm:text-3xl font-bold text-raisin-black">Messages</h1>
              <p className="text-base text-raisin-black/70">Communicate with contractors and manage project discussions</p>
            </div>

            <Dialog open={showNewConversationDialog} onOpenChange={setShowNewConversationDialog}>
              <DialogTrigger asChild>
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-philippine-green to-philippine-green/90 hover:from-philippine-green/90 hover:to-philippine-green text-white shadow-lg hover:shadow-xl transition-all duration-300 touch-target-enhanced hover:scale-105 rounded-2xl w-full sm:w-auto"
                >
                  <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
                  New Conversation
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle className="text-xl font-bold text-raisin-black">Start New Conversation</DialogTitle>
                </DialogHeader>
                <div className="space-y-6">
                  <div>
                    <label className="text-sm font-semibold text-raisin-black mb-3 block">
                      Select Project
                    </label>
                    <Select value={selectedProjectId} onValueChange={setSelectedProjectId}>
                      <SelectTrigger className="rounded-xl border-2 border-slate-200 focus:border-philippine-green">
                        <SelectValue placeholder="Choose a project...">
                          {selectedProjectId && (() => {
                            const selectedProject = projects.find(p => p.id === selectedProjectId)
                            if (selectedProject) {
                              const category = selectedProject.category ?
                                selectedProject.category.charAt(0).toUpperCase() + selectedProject.category.slice(1).replace(/[_-]/g, ' ') :
                                'Project'
                              const brief = selectedProject.title ?
                                selectedProject.title.length > 30 ? selectedProject.title.substring(0, 30) + '...' : selectedProject.title :
                                'Project details'
                              return (
                                <div className="flex flex-col items-start">
                                  <div className="font-medium">{category}</div>
                                  <div className="text-xs text-slate-500">{brief}</div>
                                </div>
                              )
                            }
                            return null
                          })()}
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent className="rounded-xl">
                        {projects.map((project) => (
                          <SelectItem key={project.id} value={project.id} className="rounded-lg">
                            <div className="flex flex-col items-start">
                              <div className="font-medium text-slate-900">
                                {project.category ?
                                  project.category.charAt(0).toUpperCase() + project.category.slice(1).replace(/[_-]/g, ' ') :
                                  'Project'
                                }
                              </div>
                              <div className="text-xs text-slate-500 truncate max-w-[200px]">
                                {project.title ?
                                  project.title.length > 40 ? project.title.substring(0, 40) + '...' : project.title :
                                  project.description ?
                                    project.description.length > 40 ? project.description.substring(0, 40) + '...' : project.description :
                                    'Project details'
                                }
                              </div>
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="flex justify-end space-x-3">
                    <Button
                      variant="outline"
                      onClick={() => setShowNewConversationDialog(false)}
                      className="rounded-xl border-2 border-slate-200 hover:border-slate-300"
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleCreateConversation}
                      disabled={!selectedProjectId || projectsLoading}
                      className="bg-gradient-to-r from-philippine-green to-philippine-green/90 hover:from-philippine-green/90 hover:to-philippine-green text-white rounded-xl"
                    >
                      {projectsLoading ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : null}
                      Create
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        <MessagingErrorBoundary>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-220px)] sm:h-[calc(100vh-200px)]">
            {/* Conversation List - Enhanced */}
            <div className="lg:col-span-1 bg-white/95 backdrop-blur-xl border border-slate-200/60 rounded-3xl p-6 shadow-xl flex flex-col h-full">
              <div className="mb-4">
                <h2 className="text-lg font-bold text-raisin-black mb-2">Conversations</h2>
                <p className="text-sm text-raisin-black/60">Your active project discussions</p>
              </div>
              <ConversationList
                conversations={conversations}
                selectedConversationId={selectedConversationId}
                onSelectConversation={handleSelectConversation}
                onNewConversation={() => setShowNewConversationDialog(true)}
                loading={loading}
                currentUserId={user?.id || ''}
              />
            </div>

            {/* Message Thread - Enhanced */}
            <div className="lg:col-span-2 bg-white/95 backdrop-blur-xl border border-slate-200/60 rounded-3xl shadow-xl flex flex-col h-full overflow-hidden">
              {selectedConversationId && currentConversation ? (
                <MessageThread
                  messages={messages}
                  currentUserId={user?.id || ''}
                  onSendMessage={handleSendMessage}
                  onMarkAsRead={handleMarkAsRead}
                  loading={loading}
                  conversationTitle={currentConversation.projects?.title}
                />
              ) : (
                <div className="flex-1 flex items-center justify-center text-center p-8">
                  <div className="max-w-md">
                    <div className="w-20 h-20 bg-gradient-to-br from-philippine-green/10 to-cerulean-blue/10 rounded-full flex items-center justify-center mx-auto mb-6">
                      <MessageCircle className="h-10 w-10 text-philippine-green" />
                    </div>
                    <h3 className="text-xl font-bold text-raisin-black mb-3">Select a conversation</h3>
                    <p className="text-raisin-black/60 mb-6 leading-relaxed">
                      Choose a conversation from the list to start messaging with contractors about your projects.
                    </p>
                    <Button
                      onClick={() => setShowNewConversationDialog(true)}
                      className="bg-gradient-to-r from-philippine-green to-philippine-green/90 hover:from-philippine-green/90 hover:to-philippine-green text-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Start New Conversation
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </MessagingErrorBoundary>
      </div>
    </div>
  )
}
