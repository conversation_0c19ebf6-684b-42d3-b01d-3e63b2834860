"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Search, MessageCircle, Phone, Mail, ChevronDown, ChevronRight, HelpCircle, Book, Video, FileText, Lightbulb, Shield, CreditCard, Hammer, Users } from "lucide-react"

export default function HelpPage() {
  const { user } = useUser()
  const [searchQuery, setSearchQuery] = useState("")
  const [expandedFaq, setExpandedFaq] = useState<string | null>(null)
  const [contactForm, setContactForm] = useState({
    name: "",
    email: "",
    subject: "",
    message: "",
  })

  const faqs = [
    {
      id: "1",
      question: "How does RenovHub work?",
      answer:
        "RenovHub connects homeowners with verified contractors. Simply post your project, receive bids from qualified professionals, and hire the best fit for your needs.",
    },
    {
      id: "2",
      question: "Are all contractors verified?",
      answer:
        "Yes, all contractors on our platform go through a comprehensive verification process including background checks, license verification, and insurance confirmation.",
    },
    {
      id: "3",
      question: "How do I pay for my project?",
      answer:
        "We offer secure escrow payment protection. Funds are held safely and released based on project milestones, ensuring both parties are protected.",
    },
    {
      id: "4",
      question: "What if I'm not satisfied with the work?",
      answer:
        "We have a comprehensive dispute resolution process and guarantee system. Our support team will work with you to resolve any issues.",
    },
    {
      id: "5",
      question: "How much does RenovHub cost?",
      answer:
        "RenovHub is free for homeowners to post projects and receive bids. We only charge a small service fee when you successfully hire a contractor.",
    },
  ]

  const categories = [
    {
      title: "Getting Started",
      icon: HelpCircle,
      articles: ["How to post your first project", "Understanding contractor bids", "Setting your budget and timeline"],
    },
    {
      title: "Working with Contractors",
      icon: MessageCircle,
      articles: ["How to communicate effectively", "Managing project milestones", "Handling changes and updates"],
    },
    {
      title: "Payments & Billing",
      icon: Mail,
      articles: ["How escrow payments work", "Understanding fees", "Payment methods accepted"],
    },
  ]

  const handleSubmitContact = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log("Contact form submitted:", contactForm)
  }

  const filteredFaqs = faqs.filter(
    (faq) =>
      faq.question.toLowerCase().includes(searchQuery.toLowerCase()) ||
      faq.answer.toLowerCase().includes(searchQuery.toLowerCase()),
  )

  const helpCategories = [
    {
      title: "Getting Started",
      icon: <Lightbulb className="h-6 w-6 text-brand-primary" />,
      description: "Learn the basics of using RenovHub",
      articles: ["How to post a project", "Finding the right contractor", "Understanding bids"]
    },
    {
      title: "Safety & Security",
      icon: <Shield className="h-6 w-6 text-brand-secondary" />,
      description: "Stay safe and secure on our platform",
      articles: ["Contractor verification", "Payment protection", "Dispute resolution"]
    },
    {
      title: "Payments & Billing",
      icon: <CreditCard className="h-6 w-6 text-brand-accent" />,
      description: "Understand our payment system",
      articles: ["How payments work", "Escrow protection", "Refund policy"]
    },
    {
      title: "For Contractors",
      icon: <Hammer className="h-6 w-6 text-brand-primary" />,
      description: "Resources for professional contractors",
      articles: ["Creating your profile", "Bidding on projects", "Building your reputation"]
    }
  ]

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-lg sm:text-xl lg:text-2xl font-light text-slate-900 mb-4">How can we help?</h1>
          <p className="text-slate-500 mb-8">Find answers to common questions or get in touch with our support team</p>

          {/* Search */}
          <div className="max-w-md mx-auto relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search for help..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 border-slate-200 focus:border-slate-300"
            />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <div className="text-center p-6 border border-slate-100 rounded-xl hover:shadow-sm transition-shadow">
            <MessageCircle className="h-8 w-8 text-slate-400 mx-auto mb-3" />
            <h3 className="font-medium text-slate-900 mb-2">Live Chat</h3>
            <p className="text-sm text-slate-500 mb-4">Get instant help from our support team</p>
            <Button size="sm" className="bg-slate-900 hover:bg-slate-800 text-white">
              Start Chat
            </Button>
          </div>

          <div className="text-center p-6 border border-slate-100 rounded-xl hover:shadow-sm transition-shadow">
            <Phone className="h-8 w-8 text-slate-400 mx-auto mb-3" />
            <h3 className="font-medium text-slate-900 mb-2">Call Us</h3>
            <p className="text-sm text-slate-500 mb-4">Speak with a support representative</p>
            <Button size="sm" variant="outline" className="bg-transparent border-slate-200 hover:border-slate-300">
              (*************
            </Button>
          </div>

          <div className="text-center p-6 border border-slate-100 rounded-xl hover:shadow-sm transition-shadow">
            <Mail className="h-8 w-8 text-slate-400 mx-auto mb-3" />
            <h3 className="font-medium text-slate-900 mb-2">Email Support</h3>
            <p className="text-sm text-slate-500 mb-4">Send us a detailed message</p>
            <Button size="sm" variant="outline" className="bg-transparent border-slate-200 hover:border-slate-300">
              Send Email
            </Button>
          </div>
        </div>

        {/* Help Categories */}
        <div className="mb-12">
          <h2 className="text-2xl font-medium text-slate-900 mb-8 text-center">Browse by Category</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {helpCategories.map((category, index) => (
              <EnhancedCard key={index} variant="elevated" className="text-center hover:shadow-lg transition-shadow cursor-pointer">
                <div className="p-6">
                  <div className="mb-4">{category.icon}</div>
                  <h3 className="font-medium text-slate-900 mb-2">{category.title}</h3>
                  <p className="text-sm text-slate-600 mb-4">{category.description}</p>
                  <div className="space-y-1">
                    {category.articles.map((article, idx) => (
                      <p key={idx} className="text-xs text-slate-500">{article}</p>
                    ))}
                  </div>
                </div>
              </EnhancedCard>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* FAQ Section */}
            <div>
              <h2 className="text-xl font-medium text-slate-900 mb-6">Frequently Asked Questions</h2>

              <div className="space-y-4">
                {filteredFaqs.map((faq) => (
                  <div key={faq.id} className="border border-slate-100 rounded-xl overflow-hidden">
                    <button
                      onClick={() => setExpandedFaq(expandedFaq === faq.id ? null : faq.id)}
                      className="w-full p-4 text-left flex items-center justify-between hover:bg-slate-50 transition-colors"
                    >
                      <span className="font-medium text-slate-900">{faq.question}</span>
                      {expandedFaq === faq.id ? (
                        <ChevronDown className="h-4 w-4 text-slate-400" />
                      ) : (
                        <ChevronRight className="h-4 w-4 text-slate-400" />
                      )}
                    </button>
                    {expandedFaq === faq.id && (
                      <div className="px-4 pb-4">
                        <p className="text-slate-600 leading-relaxed">{faq.answer}</p>
                      </div>
                    )}
                  </div>
                ))}
              </div>

              {filteredFaqs.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-slate-500">No results found for "{searchQuery}"</p>
                </div>
              )}
            </div>

            {/* Contact Form */}
            <div>
              <h2 className="text-xl font-medium text-slate-900 mb-6">Still need help?</h2>

              <div className="border border-slate-100 rounded-xl p-6">
                <form onSubmit={handleSubmitContact} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">Name</label>
                      <Input
                        value={contactForm.name}
                        onChange={(e) => setContactForm((prev) => ({ ...prev, name: e.target.value }))}
                        className="border-slate-200 focus:border-slate-300"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-slate-700 mb-2">Email</label>
                      <Input
                        type="email"
                        value={contactForm.email}
                        onChange={(e) => setContactForm((prev) => ({ ...prev, email: e.target.value }))}
                        className="border-slate-200 focus:border-slate-300"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Subject</label>
                    <Input
                      value={contactForm.subject}
                      onChange={(e) => setContactForm((prev) => ({ ...prev, subject: e.target.value }))}
                      className="border-slate-200 focus:border-slate-300"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">Message</label>
                    <Textarea
                      value={contactForm.message}
                      onChange={(e) => setContactForm((prev) => ({ ...prev, message: e.target.value }))}
                      className="border-slate-200 focus:border-slate-300 min-h-[120px]"
                      placeholder="Please describe your issue or question..."
                      required
                    />
                  </div>

                  <Button type="submit" className="bg-slate-900 hover:bg-slate-800 text-white">
                    Send Message
                  </Button>
                </form>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            <div>
              <h3 className="font-medium text-slate-900 mb-4">Browse by Category</h3>

              <div className="space-y-3">
                {categories.map((category, index) => (
                  <div key={index} className="border border-slate-100 rounded-xl p-4">
                    <div className="flex items-center space-x-3 mb-3">
                      <category.icon className="h-5 w-5 text-slate-400" />
                      <h4 className="font-medium text-slate-900">{category.title}</h4>
                    </div>
                    <ul className="space-y-2">
                      {category.articles.map((article, articleIndex) => (
                        <li key={articleIndex}>
                          <button className="text-sm text-slate-600 hover:text-slate-900 transition-colors">
                            {article}
                          </button>
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>

            <div className="border border-slate-100 rounded-xl p-4">
              <h4 className="font-medium text-slate-900 mb-3">Contact Hours</h4>
              <div className="space-y-2 text-sm text-slate-600">
                <div className="flex justify-between">
                  <span>Monday - Friday:</span>
                  <span>9AM - 6PM PST</span>
                </div>
                <div className="flex justify-between">
                  <span>Saturday:</span>
                  <span>10AM - 4PM PST</span>
                </div>
                <div className="flex justify-between">
                  <span>Sunday:</span>
                  <span>Closed</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <RoleSwitcher />
    </div>
  )
}
