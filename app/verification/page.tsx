"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { CheckCircle, Upload, Shield, AlertCircle, Clock, FileText, User, Briefcase, Award } from "lucide-react"

interface VerificationStep {
  id: string
  title: string
  description: string
  status: "completed" | "current" | "pending" | "rejected"
}

export default function VerificationPage() {
  const [currentStep, setCurrentStep] = useState("personal")
  const [formData, setFormData] = useState({
    // Personal Info
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    state: "",
    zip: "",
    
    // Business Info
    businessName: "",
    businessType: "",
    businessAddress: "",
    businessCity: "",
    businessState: "",
    businessZip: "",
    yearsInBusiness: "",
    employeeCount: "",
    
    // License & Insurance
    licenseNumber: "",
    licenseType: "",
    licenseState: "",
    licenseExpiration: "",
    insuranceProvider: "",
    insurancePolicy: "",
    insuranceCoverage: "",
    insuranceExpiration: "",
    
    // Services & References
    services: "",
    specialties: "",
    references: "",
  })

  const steps: VerificationStep[] = [
    {
      id: "personal",
      title: "Personal Information",
      description: "Provide your contact details",
      status: currentStep === "personal" ? "current" : currentStep === "business" || currentStep === "license" || currentStep === "services" || currentStep === "review" ? "completed" : "pending",
    },
    {
      id: "business",
      title: "Business Information",
      description: "Tell us about your business",
      status: currentStep === "business" ? "current" : currentStep === "license" || currentStep === "services" || currentStep === "review" ? "completed" : "pending",
    },
    {
      id: "license",
      title: "License & Insurance",
      description: "Upload your credentials",
      status: currentStep === "license" ? "current" : currentStep === "services" || currentStep === "review" ? "completed" : "pending",
    },
    {
      id: "services",
      title: "Services & References",
      description: "Describe your expertise",
      status: currentStep === "services" ? "current" : currentStep === "review" ? "completed" : "pending",
    },
    {
      id: "review",
      title: "Review & Submit",
      description: "Verify your information",
      status: currentStep === "review" ? "current" : "pending",
    },
  ]

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleNext = () => {
    if (currentStep === "personal") setCurrentStep("business")
    else if (currentStep === "business") setCurrentStep("license")
    else if (currentStep === "license") setCurrentStep("services")
    else if (currentStep === "services") setCurrentStep("review")
    else if (currentStep === "review") {
      // Submit verification
      alert("Verification submitted successfully!")
    }
  }

  const handleBack = () => {
    if (currentStep === "business") setCurrentStep("personal")
    else if (currentStep === "license") setCurrentStep("business")
    else if (currentStep === "services") setCurrentStep("license")
    else if (currentStep === "review") setCurrentStep("services")
  }

  const getStepIcon = (status: string) => {
    switch (status) {
      case "completed":
        return <CheckCircle className="h-6 w-6 text-green-600" />
      case "current":
        return <Clock className="h-6 w-6 text-blue-600" />
      case "rejected":
        return <AlertCircle className="h-6 w-6 text-red-600" />
      default:
        return <div className="h-6 w-6 rounded-full border-2 border-slate-300" />
    }
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-6xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-12 text-center">
          <div className="inline-flex items-center justify-center p-3 bg-blue-50 rounded-full mb-4">
            <Shield className="h-8 w-8 text-blue-600" />
          </div>
          <h1 className="text-3xl font-light text-slate-900 mb-2">Contractor Verification</h1>
          <p className="text-slate-500 max-w-xl mx-auto">
            Complete the verification process to become a verified contractor on RenovHub. This helps build trust with potential clients.
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex flex-col md:flex-row justify-between max-w-4xl mx-auto">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-start md:items-center md:flex-col text-center mb-6 md:mb-0">
                <div className="mr-4 md:mr-0 md:mb-3">
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full ${
                      step.status === "completed"
                        ? "bg-green-100"
                        : step.status === "current"
                        ? "bg-blue-100"
                        : step.status === "rejected"
                        ? "bg-red-100"
                        : "bg-slate-100"
                    }`}
                  >
                    {getStepIcon(step.status)}
                  </div>
                </div>
                <div className="md:max-w-[120px]">
                  <p className="font-medium text-slate-900 text-sm">{step.title}</p>
                  <p className="text-xs text-slate-500 hidden md:block">{step.description}</p>
                </div>
                {index < steps.length - 1 && (
                  <div className="hidden md:block absolute left-[calc(50%+60px)] right-[calc(50%-60px)] top-5 h-0.5 bg-slate-200">
                    <div
                      className={`h-full bg-green-500 transition-all duration-300 ${
                        step.status === "completed" ? "w-full" : "w-0"
                      }`}
                    />
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Content */}
        <Card className="max-w-4xl mx-auto bg-white border-0 shadow-sm p-8">
          {/* Personal Information */}
          {currentStep === "personal" && (
            <div className="animate-in">
              <div className="flex items-center mb-6">
                <User className="h-5 w-5 text-slate-400 mr-2" />
                <h2 className="text-xl font-medium text-slate-900">Personal Information</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">First Name</label>
                  <Input
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Last Name</label>
                  <Input
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Email Address</label>
                  <Input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Phone Number</label>
                  <Input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-slate-700 mb-2">Address</label>
                  <Input
                    name="address"
                    value={formData.address}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">City</label>
                  <Input
                    name="city"
                    value={formData.city}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">State</label>
                    <Input
                      name="state"
                      value={formData.state}
                      onChange={handleInputChange}
                      className="border-slate-200 focus:border-slate-300"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">ZIP Code</label>
                    <Input
                      name="zip"
                      value={formData.zip}
                      onChange={handleInputChange}
                      className="border-slate-200 focus:border-slate-300"
                      required
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Business Information */}
          {currentStep === "business" && (
            <div className="animate-in">
              <div className="flex items-center mb-6">
                <Briefcase className="h-5 w-5 text-slate-400 mr-2" />
                <h2 className="text-xl font-medium text-slate-900">Business Information</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Business Name</label>
                  <Input
                    name="businessName"
                    value={formData.businessName}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Business Type</label>
                  <select
                    name="businessType"
                    value={formData.businessType}
                    onChange={handleInputChange}
                    className="w-full rounded-md border border-slate-200 py-2 px-3 text-sm focus:border-slate-300 focus:outline-none"
                    required
                  >
                    <option value="">Select Business Type</option>
                    <option value="sole_proprietor">Sole Proprietorship</option>
                    <option value="llc">LLC</option>
                    <option value="corporation">Corporation</option>
                    <option value="partnership">Partnership</option>
                  </select>
                </div>
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-slate-700 mb-2">Business Address</label>
                  <Input
                    name="businessAddress"
                    value={formData.businessAddress}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">City</label>
                  <Input
                    name="businessCity"
                    value={formData.businessCity}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">State</label>
                    <Input
                      name="businessState"
                      value={formData.businessState}
                      onChange={handleInputChange}
                      className="border-slate-200 focus:border-slate-300"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">ZIP Code</label>
                    <Input
                      name="businessZip"
                      value={formData.businessZip}
                      onChange={handleInputChange}
                      className="border-slate-200 focus:border-slate-300"
                      required
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Years in Business</label>
                  <Input
                    type="number"
                    name="yearsInBusiness"
                    value={formData.yearsInBusiness}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Number of Employees</label>
                  <Input
                    type="number"
                    name="employeeCount"
                    value={formData.employeeCount}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
              </div>
            </div>
          )}

          {/* License & Insurance */}
          {currentStep === "license" && (
            <div className="animate-in">
              <div className="flex items-center mb-6">
                <FileText className="h-5 w-5 text-slate-400 mr-2" />
                <h2 className="text-xl font-medium text-slate-900">License & Insurance</h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">License Number</label>
                  <Input
                    name="licenseNumber"
                    value={formData.licenseNumber}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">License Type</label>
                  <Input
                    name="licenseType"
                    value={formData.licenseType}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">License State</label>
                  <Input
                    name="licenseState"
                    value={formData.licenseState}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">License Expiration</label>
                  <Input
                    type="date"
                    name="licenseExpiration"
                    value={formData.licenseExpiration}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-slate-700 mb-2">Upload License Document</label>
                  <div className="border-2 border-dashed border-slate-200 rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                    <p className="text-sm text-slate-500 mb-2">Drag and drop your license document here</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-transparent border-slate-200 hover:border-slate-300"
                    >
                      Browse Files
                    </Button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Insurance Provider</label>
                  <Input
                    name="insuranceProvider"
                    value={formData.insuranceProvider}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Policy Number</label>
                  <Input
                    name="insurancePolicy"
                    value={formData.insurancePolicy}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Coverage Amount</label>
                  <Input
                    name="insuranceCoverage"
                    value={formData.insuranceCoverage}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Expiration Date</label>
                  <Input
                    type="date"
                    name="insuranceExpiration"
                    value={formData.insuranceExpiration}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-slate-700 mb-2">Upload Insurance Certificate</label>
                  <div className="border-2 border-dashed border-slate-200 rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                    <p className="text-sm text-slate-500 mb-2">Drag and drop your insurance certificate here</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-transparent border-slate-200 hover:border-slate-300"
                    >
                      Browse Files
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Services & References */}
          {currentStep === "services" && (
            <div className="animate-in">
              <div className="flex items-center mb-6">
                <Award className="h-5 w-5 text-slate-400 mr-2" />
                <h2 className="text-xl font-medium text-slate-900">Services & References</h2>
              </div>

              <div className="space-y-6 mb-8">
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Services Offered</label>
                  <Textarea
                    name="services"
                    value={formData.services}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300 min-h-[100px]"
                    placeholder="List the services you offer (e.g., Kitchen Remodeling, Bathroom Renovation, etc.)"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Specialties</label>
                  <Textarea
                    name="specialties"
                    value={formData.specialties}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300 min-h-[100px]"
                    placeholder="Describe your areas of expertise and specialization"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">References</label>
                  <Textarea
                    name="references"
                    value={formData.references}
                    onChange={handleInputChange}
                    className="border-slate-200 focus:border-slate-300 min-h-[100px]"
                    placeholder="Provide at least 3 references with contact information"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">Upload Portfolio Images</label>
                  <div className="border-2 border-dashed border-slate-200 rounded-lg p-6 text-center">
                    <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
                    <p className="text-sm text-slate-500 mb-2">Drag and drop images of your past projects</p>
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-transparent border-slate-200 hover:border-slate-300"
                    >
                      Browse Files
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Review & Submit */}
          {currentStep === "review" && (
            <div className="animate-in">
              <div className="flex items-center mb-6">
                <CheckCircle className="h-5 w-5 text-slate-400 mr-2" />
                <h2 className="text-xl font-medium text-slate-900">Review & Submit</h2>
              </div>

              <div className="space-y-6 mb-8">
                <div className="bg-slate-50 p-6 rounded-lg">
                  <h3 className="font-medium text-slate-900 mb-4">Personal Information</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-slate-500">Name:</span>
                      <span className="ml-2 text-slate-900">John Doe</span>
                    </div>
                    <div>
                      <span className="text-slate-500">Email:</span>
                      <span className="ml-2 text-slate-900"><EMAIL></span>
                    </div>
                    <div>
                      <span className="text-slate-500">Phone:</span>
                      <span className="ml-2 text-slate-900">(*************</span>
                    </div>
                    <div>
                      <span className="text-slate-500">Business:</span>
                      <span className="ml-2 text-slate-900">Elite Construction LLC</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </Card>
      </div>
    </div>
  )
}
