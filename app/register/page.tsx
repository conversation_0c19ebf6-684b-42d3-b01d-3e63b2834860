"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Logo } from "@/components/logo"
import { authService } from "@/services/auth"
import { useUser } from "@/contexts/user-context"
import { AlertCircle, Loader2, Eye, EyeOff, User, Briefcase } from "lucide-react"
import { GoogleSignInButtonSecondary } from "@/components/ui/google-signin-button"
import Link from "next/link"

export default function RegisterPage() {
  const router = useRouter()
  const { setUser } = useUser()
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    password: "",
    confirmPassword: "",
    role: "customer" as "customer" | "pro",
    phone: "",
    location: ""
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError(null)

    // Validation
    if (formData.password !== formData.confirmPassword) {
      setError("Passwords do not match")
      setLoading(false)
      return
    }

    if (formData.password.length < 6) {
      setError("Password must be at least 6 characters")
      setLoading(false)
      return
    }

    try {
      const result = await authService.register({
        name: formData.name,
        email: formData.email,
        password: formData.password,
        role: formData.role,
        phone: formData.phone,
        location: formData.location
      })
      
      if (result.success && result.user) {
        setUser(result.user)
        
        // Redirect based on user role
        if (result.user.role === 'pro') {
          router.push('/pro/onboarding') // Pro users go through onboarding first
        } else {
          router.push('/onboarding') // Customer onboarding
        }
      } else {
        setError(result.error || 'Registration failed')
      }
    } catch (err) {
      setError('An unexpected error occurred')
      console.error('Registration error:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    if (error) setError(null)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 flex items-center justify-center safe-area-inset">
      <div className="w-full max-w-lg mx-auto px-4 sm:px-6">
        {/* Logo - Enhanced */}
        <div className="text-center mb-8 sm:mb-10 lg:mb-12">
          <Logo size="xl" className="justify-center mb-6 sm:mb-8" />
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-raisin-black mb-3">Create your account</h1>
          <p className="text-base sm:text-lg text-raisin-black/70 leading-relaxed">Join thousands of homeowners and contractors</p>
        </div>

        {/* Role Selection - Enhanced */}
        <div className="mb-8 sm:mb-10">
          <Label className="text-base font-semibold text-raisin-black mb-4 sm:mb-6 block">I am a:</Label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
            <button
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, role: "customer" }))}
              className={`p-6 sm:p-8 rounded-2xl border-2 transition-all duration-300 touch-target-enhanced hover:scale-105 min-h-[120px] sm:min-h-[140px] flex flex-col items-center justify-center text-center ${
                formData.role === "customer"
                  ? "border-cerulean-blue bg-gradient-to-br from-cerulean-blue/10 to-cerulean-blue/5 text-cerulean-blue shadow-lg"
                  : "border-slate-200 hover:border-slate-300 hover:bg-slate-50"
              }`}
            >
              <User className="h-8 w-8 sm:h-10 sm:w-10 mx-auto mb-3 sm:mb-4" />
              <div className="text-lg sm:text-xl font-bold mb-2">Homeowner</div>
              <div className="text-sm sm:text-base text-slate-600 leading-relaxed">I need renovation work done</div>
            </button>
            <button
              type="button"
              onClick={() => setFormData(prev => ({ ...prev, role: "pro" }))}
              className={`p-6 sm:p-8 rounded-2xl border-2 transition-all duration-300 touch-target-enhanced hover:scale-105 min-h-[120px] sm:min-h-[140px] flex flex-col items-center justify-center text-center ${
                formData.role === "pro"
                  ? "border-philippine-green bg-gradient-to-br from-philippine-green/10 to-philippine-green/5 text-philippine-green shadow-lg"
                  : "border-slate-200 hover:border-slate-300 hover:bg-slate-50"
              }`}
            >
              <Briefcase className="h-8 w-8 sm:h-10 sm:w-10 mx-auto mb-3 sm:mb-4" />
              <div className="text-lg sm:text-xl font-bold mb-2">Contractor</div>
              <div className="text-sm sm:text-base text-slate-600 leading-relaxed">I provide renovation services</div>
            </button>
          </div>
        </div>

        {/* Google Sign-Up Option */}
        <div className="mb-8">
          <div className="text-center mb-4">
            <p className="text-sm text-slate-600 font-medium">Quick sign-up with Google</p>
          </div>
          <GoogleSignInButtonSecondary
            role={formData.role}
            className="w-full rounded-2xl border-2 border-slate-200 hover:border-slate-300 hover:bg-slate-50 transition-all duration-300"
            onError={(error) => setError(error)}
          >
            Sign up with Google
          </GoogleSignInButtonSecondary>

          {/* Divider */}
          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-slate-200"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-gradient-to-br from-slate-50 via-white to-blue-50/30 text-slate-500 font-medium">Or sign up with email</span>
            </div>
          </div>
        </div>

        {/* Registration Form - Enhanced */}
        <div className="bg-white/95 backdrop-blur-xl border border-slate-200/60 rounded-3xl p-8 sm:p-10 shadow-xl">
          <form onSubmit={handleSubmit} className="space-y-4 sm:space-y-5">
            {/* Error Message - Mobile-First */}
            {error && (
              <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-xl">
                <AlertCircle className="h-4 w-4 text-red-500 flex-shrink-0" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            )}

            {/* Name Field - Mobile-First */}
            <div className="space-y-2">
              <Label htmlFor="name">Full Name</Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter your full name"
                required
                className="input-mobile"
                disabled={loading}
              />
            </div>

            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter your email"
                required
                disabled={loading}
              />
            </div>

            {/* Phone Field */}
            <div className="space-y-2">
              <Label htmlFor="phone">Phone Number</Label>
              <Input
                id="phone"
                name="phone"
                type="tel"
                value={formData.phone}
                onChange={handleInputChange}
                placeholder="(*************"
                disabled={loading}
              />
            </div>

            {/* Location Field */}
            <div className="space-y-2">
              <Label htmlFor="location">Location</Label>
              <Input
                id="location"
                name="location"
                type="text"
                value={formData.location}
                onChange={handleInputChange}
                placeholder="City, State"
                disabled={loading}
              />
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={handleInputChange}
                  placeholder="Create a password"
                  required
                  disabled={loading}
                  className="pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                  disabled={loading}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {/* Confirm Password Field */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  placeholder="Confirm your password"
                  required
                  disabled={loading}
                  className="pr-10"
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
                  disabled={loading}
                >
                  {showConfirmPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
            </div>

            {/* Terms Agreement */}
            <div className="flex items-start space-x-2">
              <input
                id="terms"
                type="checkbox"
                required
                className="mt-1 rounded border-slate-300 text-brand-primary focus:ring-brand-primary"
              />
              <Label htmlFor="terms" className="text-sm text-slate-600 leading-relaxed">
                I agree to the{' '}
                <Link href="/terms" className="text-brand-primary hover:text-brand-primary/80">
                  Terms of Service
                </Link>
                {' '}and{' '}
                <Link href="/privacy" className="text-brand-primary hover:text-brand-primary/80">
                  Privacy Policy
                </Link>
              </Label>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              size="lg"
              className="w-full bg-gradient-to-r from-philippine-green to-philippine-green/90 hover:from-philippine-green/90 hover:to-philippine-green text-white shadow-xl hover:shadow-2xl transition-all duration-300 touch-target-enhanced hover:scale-105 rounded-2xl py-4"
              disabled={loading}
            >
              {loading ? (
                <>
                  <Loader2 className="h-5 w-5 mr-3 animate-spin" />
                  Creating account...
                </>
              ) : (
                'Create Account'
              )}
            </Button>
          </form>
        </div>

        {/* Sign In Link */}
        <div className="text-center mt-8">
          <p className="text-base text-slate-600">
            Already have an account?{' '}
            <Link href="/login" className="text-philippine-green hover:text-philippine-green/80 font-semibold transition-colors duration-300">
              Sign in
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
