"use client"

import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { UnifiedNavigation } from '@/components/unified-navigation'
import { RoleSwitcher } from '@/components/role-switcher'
import { BidSubmissionForm } from '@/components/bidding/bid-submission-form'
import { useProjects } from '@/hooks/use-projects'
import { Loader2 } from 'lucide-react'

export default function SubmitBidPage() {
  const params = useParams()
  const router = useRouter()
  const projectId = params?.id as string
  
  const [project, setProject] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  
  useEffect(() => {
    const loadProject = async () => {
      if (!projectId) return
      
      try {
        // In a real implementation, you'd fetch the project details
        // For now, we'll use a mock project
        setProject({
          id: projectId,
          title: 'Kitchen Renovation Project',
          description: 'Complete kitchen remodel including cabinets, countertops, and appliances'
        })
      } catch (error) {
        console.error('Error loading project:', error)
      } finally {
        setLoading(false)
      }
    }
    
    loadProject()
  }, [projectId])

  const handleBidSuccess = () => {
    router.push('/pro/bids')
  }

  const handleCancel = () => {
    router.back()
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />
        <div className="container-native section-native">
          <div className="flex items-center justify-center py-12">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
          </div>
        </div>
        <RoleSwitcher />
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />
        <div className="container-native section-native">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-slate-900 mb-4">Project Not Found</h1>
            <p className="text-slate-600 mb-6">The project you're looking for doesn't exist or has been removed.</p>
            <button 
              onClick={() => router.push('/pro/projects')}
              className="text-blue-600 hover:text-blue-700"
            >
              Back to Projects
            </button>
          </div>
        </div>
        <RoleSwitcher />
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <UnifiedNavigation />
      
      <div className="container-native section-native">
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-slate-900 mb-2">Submit Your Bid</h1>
          <p className="text-slate-600">
            Create a competitive bid for this project. Be detailed and professional to increase your chances of being selected.
          </p>
        </div>

        <BidSubmissionForm
          projectId={projectId}
          projectTitle={project.title}
          onSuccess={handleBidSuccess}
          onCancel={handleCancel}
        />
      </div>

      <RoleSwitcher />
    </div>
  )
}
