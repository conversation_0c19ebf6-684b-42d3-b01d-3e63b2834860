"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { useUser } from "@/contexts/user-context"
import { 
  ArrowLeft, 
  Calendar, 
  MapPin, 
  DollarSign, 
  Clock, 
  MessageCircle, 
  Phone, 
  Star,
  CheckCircle,
  AlertCircle,
  Camera,
  FileText,
  User
} from "lucide-react"

interface Project {
  id: string
  title: string
  description: string
  category: string
  status: string
  budget: string
  timeline: string
  location: string
  clientName: string
  clientEmail: string
  clientPhone: string
  startDate: string
  endDate: string
  progress: number
  images: string[]
  requirements: string[]
  milestones: Array<{
    id: string
    title: string
    description: string
    completed: boolean
    dueDate: string
  }>
}

export default function ProProjectDetailsPage() {
  const { user } = useUser()
  const router = useRouter()
  const params = useParams()
  const [project, setProject] = useState<Project | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Simulate loading project data
    setTimeout(() => {
      setProject({
        id: params.id as string,
        title: "Modern Kitchen Renovation",
        description: "Complete kitchen remodel including new cabinets, countertops, appliances, and flooring. Looking for a modern, functional design with high-quality materials.",
        category: "Kitchen Renovation",
        status: "in-progress",
        budget: "$25,000 - $35,000",
        timeline: "4-6 weeks",
        location: "San Francisco, CA",
        clientName: "Sarah Johnson",
        clientEmail: "<EMAIL>",
        clientPhone: "(*************",
        startDate: "2024-02-15",
        endDate: "2024-03-30",
        progress: 65,
        images: [
          "/api/placeholder/400/300",
          "/api/placeholder/400/300",
          "/api/placeholder/400/300"
        ],
        requirements: [
          "Quartz countertops",
          "Stainless steel appliances",
          "Hardwood flooring",
          "Custom cabinetry",
          "Under-cabinet lighting"
        ],
        milestones: [
          {
            id: "1",
            title: "Demolition",
            description: "Remove existing cabinets and appliances",
            completed: true,
            dueDate: "2024-02-20"
          },
          {
            id: "2",
            title: "Electrical & Plumbing",
            description: "Update electrical and plumbing systems",
            completed: true,
            dueDate: "2024-02-25"
          },
          {
            id: "3",
            title: "Cabinet Installation",
            description: "Install new custom cabinets",
            completed: false,
            dueDate: "2024-03-05"
          },
          {
            id: "4",
            title: "Countertop Installation",
            description: "Install quartz countertops",
            completed: false,
            dueDate: "2024-03-15"
          },
          {
            id: "5",
            title: "Final Touches",
            description: "Install appliances and finishing touches",
            completed: false,
            dueDate: "2024-03-25"
          }
        ]
      })
      setLoading(false)
    }, 1000)
  }, [params.id])

  const getStatusColor = (status: string) => {
    switch (status) {
      case "in-progress": return "bg-blue-100 text-blue-800"
      case "completed": return "bg-emerald-100 text-emerald-800"
      case "pending": return "bg-amber-100 text-amber-800"
      default: return "bg-slate-100 text-slate-800"
    }
  }

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "in-progress": return "In Progress"
      case "completed": return "Completed"
      case "pending": return "Pending"
      default: return status
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <UnifiedNavigation />
        <div className="container-premium section-premium">
          <div className="text-center py-20">
            <div className="animate-pulse text-slate-500 text-lg">Loading project details...</div>
          </div>
        </div>
      </div>
    )
  }

  if (!project) {
    return (
      <div className="min-h-screen bg-white">
        <UnifiedNavigation />
        <div className="container-premium section-premium">
          <div className="text-center py-20">
            <h1 className="text-2xl font-semibold text-slate-900 mb-4">Project Not Found</h1>
            <p className="text-slate-600 mb-8">The project you're looking for doesn't exist.</p>
            <Button onClick={() => router.push('/pro/dashboard')} variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Header */}
        <div className="mb-8">
          <Button onClick={() => router.back()} variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Projects
          </Button>
          
          <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
            <div>
              <h1 className="text-3xl font-semibold text-slate-900 mb-2">{project.title}</h1>
              <div className="flex flex-wrap items-center gap-4">
                <Badge className={getStatusColor(project.status)}>
                  {getStatusLabel(project.status)}
                </Badge>
                <div className="flex items-center text-slate-600">
                  <MapPin className="h-4 w-4 mr-1" />
                  {project.location}
                </div>
                <div className="flex items-center text-slate-600">
                  <Calendar className="h-4 w-4 mr-1" />
                  {project.timeline}
                </div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3">
              <Button variant="outline">
                <MessageCircle className="h-4 w-4 mr-2" />
                Message Client
              </Button>
              <Button variant="outline">
                <Phone className="h-4 w-4 mr-2" />
                Call Client
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Progress Overview */}
            <div className="card-premium p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-slate-900">Project Progress</h2>
                <span className="text-2xl font-bold text-slate-900">{project.progress}%</span>
              </div>
              
              <div className="w-full bg-slate-200 rounded-full h-3 mb-6">
                <div 
                  className="bg-gradient-to-r from-blue-500 to-emerald-500 h-3 rounded-full transition-all duration-500"
                  style={{ width: `${project.progress}%` }}
                />
              </div>

              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-semibold text-slate-900">{project.milestones.filter(m => m.completed).length}</div>
                  <div className="text-sm text-slate-600">Completed</div>
                </div>
                <div>
                  <div className="text-2xl font-semibold text-slate-900">{project.milestones.filter(m => !m.completed).length}</div>
                  <div className="text-sm text-slate-600">Remaining</div>
                </div>
              </div>
            </div>

            {/* Project Description */}
            <div className="card-premium p-6">
              <h2 className="text-xl font-semibold text-slate-900 mb-4">Project Description</h2>
              <p className="text-slate-700 leading-relaxed">{project.description}</p>
            </div>

            {/* Milestones */}
            <div className="card-premium p-6">
              <h2 className="text-xl font-semibold text-slate-900 mb-6">Project Milestones</h2>
              <div className="space-y-4">
                {project.milestones.map((milestone) => (
                  <div key={milestone.id} className="flex items-start space-x-4 p-4 rounded-lg border border-slate-200">
                    <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 mt-1 ${
                      milestone.completed ? "bg-emerald-500" : "bg-slate-300"
                    }`}>
                      {milestone.completed && <CheckCircle className="h-4 w-4 text-white" />}
                    </div>
                    <div className="flex-1">
                      <h3 className={`font-semibold ${milestone.completed ? "text-slate-900" : "text-slate-700"}`}>
                        {milestone.title}
                      </h3>
                      <p className="text-slate-600 text-sm mt-1">{milestone.description}</p>
                      <p className="text-xs text-slate-500 mt-2">Due: {milestone.dueDate}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Client Information */}
            <div className="card-premium p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">Client Information</h3>
              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
                    <User className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <p className="font-medium text-slate-900">{project.clientName}</p>
                    <p className="text-sm text-slate-600">Project Owner</p>
                  </div>
                </div>
                
                <div className="space-y-2 pt-4 border-t border-slate-200">
                  <div className="flex items-center text-slate-600">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    <span className="text-sm">{project.clientEmail}</span>
                  </div>
                  <div className="flex items-center text-slate-600">
                    <Phone className="h-4 w-4 mr-2" />
                    <span className="text-sm">{project.clientPhone}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Project Details */}
            <div className="card-premium p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">Project Details</h3>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-slate-700">Budget</label>
                  <p className="text-slate-900">{project.budget}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-slate-700">Timeline</label>
                  <p className="text-slate-900">{project.timeline}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-slate-700">Start Date</label>
                  <p className="text-slate-900">{project.startDate}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-slate-700">End Date</label>
                  <p className="text-slate-900">{project.endDate}</p>
                </div>
              </div>
            </div>

            {/* Requirements */}
            <div className="card-premium p-6">
              <h3 className="text-lg font-semibold text-slate-900 mb-4">Requirements</h3>
              <ul className="space-y-2">
                {project.requirements.map((requirement, index) => (
                  <li key={index} className="flex items-center text-slate-700">
                    <CheckCircle className="h-4 w-4 text-emerald-500 mr-2 flex-shrink-0" />
                    <span className="text-sm">{requirement}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
