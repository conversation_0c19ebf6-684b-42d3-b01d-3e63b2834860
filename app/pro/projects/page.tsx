"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { PageHeader } from "@/components/breadcrumb"
import { ProRoute } from "@/components/route-guard"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard, ProjectCard } from "@/components/ui/enhanced-card"
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { projectService, contractorService } from "@/services/database"
import { Project } from "@/types"
import { 
  Hammer, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  Loader2, 
  Calendar,
  DollarSign,
  MapPin,
  User,
  Filter,
  Search
} from "lucide-react"
import { Input } from "@/components/ui/input"
import Link from "next/link"

export default function ProProjectsPage() {
  const { user } = useUser()
  const [projects, setProjects] = useState<Project[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState("available")
  const [searchQuery, setSearchQuery] = useState("")

  useEffect(() => {
    loadProjects()
  }, [activeTab, user])

  const loadProjects = async () => {
    if (!user) {
      setLoading(false)
      return
    }

    try {
      setLoading(true)
      setError(null)

      // Get contractor ID from user
      const contractorResponse = await contractorService.findByUserId(user.id)

      if (!contractorResponse.success) {
        setError("Failed to load contractor profile. Please try again.")
        setProjects([])
        return
      }

      const contractorId = contractorResponse.data?.id

      if (!contractorId) {
        setError("No contractor profile found. Please complete your contractor setup.")
        setProjects([])
        return
      }

      let response
      if (activeTab === "available") {
        response = await projectService.findAvailableProjects(contractorId)
      } else {
        // For other tabs, we'd need different methods
        // For now, just return empty array
        response = { success: true, data: [] }
      }

      if (response.success && response.data) {
        setProjects(response.data as any)
      } else {
        setError(response.error || "Failed to load projects")
        setProjects([])
      }
    } catch (err) {
      setError("Failed to load projects. Please try again.")
      console.error("Error loading projects:", err)
      setProjects([])
    } finally {
      setLoading(false)
    }
  }



  const formatBudget = (budget: Project['budget']) => {
    return `$${budget.min.toLocaleString()} - $${budget.max.toLocaleString()}`
  }

  const formatLocation = (location: Project['location']) => {
    if (typeof location === 'string') return location
    return location.address
  }

  const formatDate = (date: Date) => {
    const now = new Date()
    const diffTime = Math.abs(now.getTime() - date.getTime())
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    if (diffDays === 1) return "1 day ago"
    if (diffDays < 7) return `${diffDays} days ago`
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} week${Math.ceil(diffDays / 7) > 1 ? 's' : ''} ago`
    return `${Math.ceil(diffDays / 30)} month${Math.ceil(diffDays / 30) > 1 ? 's' : ''} ago`
  }

  const filteredProjects = projects.filter(project =>
    project.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    project.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    formatLocation(project.location).toLowerCase().includes(searchQuery.toLowerCase())
  )

  return (
    <ProRoute>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />

        <div className="container mx-auto px-6 py-8">
          <PageHeader
            title="Projects"
            description="Find and manage renovation projects"
          />

        {/* Search and Filters */}
        <div className="flex items-center space-x-4 mb-8">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input
              placeholder="Search projects..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <Button variant="outline" className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <span>Filters</span>
          </Button>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="grid w-full grid-cols-3 lg:w-auto">
            <TabsTrigger value="available">Available Projects</TabsTrigger>
            <TabsTrigger value="in-progress">In Progress</TabsTrigger>
            <TabsTrigger value="completed">Completed</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            {/* Loading State */}
            {loading && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <EnhancedCard key={i} loading={true} />
                ))}
              </div>
            )}

            {/* Error State */}
            {error && (
              <EnhancedCard className="p-6 text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">Error Loading Projects</h3>
                <p className="text-slate-500 mb-4">{error}</p>
                <Button onClick={loadProjects} className="bg-brand-secondary hover:bg-brand-secondary/90">
                  <Loader2 className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </EnhancedCard>
            )}

            {/* Projects Grid */}
            {!loading && !error && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredProjects.length === 0 ? (
                  <div className="col-span-full">
                    <EnhancedCard className="p-12 text-center">
                      <Hammer className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-slate-900 mb-2">
                        {searchQuery ? "No matching projects" : `No ${activeTab} projects`}
                      </h3>
                      <p className="text-slate-500">
                        {searchQuery ? "Try adjusting your search terms" : "Check back later for new opportunities"}
                      </p>
                    </EnhancedCard>
                  </div>
                ) : (
                  filteredProjects.map((project) => (
                    <ProjectCard
                      key={project.id}
                      project={{
                        id: project.id,
                        title: project.title,
                        category: project.category,
                        status: project.status,
                        budget: formatBudget(project.budget),
                        location: formatLocation(project.location),
                        createdAt: formatDate(project.createdAt),
                        bidsCount: project.bids.length
                      }}
                      variant="contractor"
                      onView={(id) => console.log('View project:', id)}
                    />
                  ))
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
        </div>
      </div>
    </ProRoute>
  )
}
