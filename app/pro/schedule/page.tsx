"use client"

import React, { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { PageHeader } from "@/components/breadcrumb"
import { ProRoute } from "@/components/route-guard"
import { useUser } from "@/contexts/user-context"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON>bsTrigger, TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { 
  Calendar,
  Clock, 
  MapPin,
  User,
  Plus,
  ChevronLeft,
  ChevronRight,
  MoreVertical,
  Edit,
  Trash2
} from "lucide-react"

interface ScheduleEvent {
  id: string
  title: string
  type: "project" | "consultation" | "estimate" | "meeting"
  client: string
  location: string
  startTime: Date
  endTime: Date
  status: "scheduled" | "in-progress" | "completed" | "cancelled"
  notes?: string
}

function ProSchedulePage() {
  const { user } = useUser()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState<"week" | "month">("week")
  const [events] = useState<ScheduleEvent[]>([
    {
      id: "1",
      title: "Kitchen Consultation",
      type: "consultation",
      client: "<PERSON>",
      location: "123 Main St, San Francisco",
      startTime: new Date(2024, 11, 15, 9, 0),
      endTime: new Date(2024, 11, 15, 10, 30),
      status: "scheduled",
      notes: "Initial consultation for kitchen remodel"
    },
    {
      id: "2",
      title: "Bathroom Installation",
      type: "project",
      client: "Sarah Johnson",
      location: "456 Oak Ave, Oakland",
      startTime: new Date(2024, 11, 15, 14, 0),
      endTime: new Date(2024, 11, 15, 17, 0),
      status: "in-progress",
      notes: "Day 3 of bathroom renovation"
    },
    {
      id: "3",
      title: "Deck Estimate",
      type: "estimate",
      client: "Mike Brown",
      location: "789 Pine St, Berkeley",
      startTime: new Date(2024, 11, 16, 11, 0),
      endTime: new Date(2024, 11, 16, 12, 0),
      status: "scheduled",
      notes: "Estimate for composite deck construction"
    }
  ])

  const getEventTypeColor = (type: ScheduleEvent['type']) => {
    switch (type) {
      case "project":
        return "bg-blue-100 text-blue-800 border-blue-200"
      case "consultation":
        return "bg-green-100 text-green-800 border-green-200"
      case "estimate":
        return "bg-yellow-100 text-yellow-800 border-yellow-200"
      case "meeting":
        return "bg-purple-100 text-purple-800 border-purple-200"
      default:
        return "bg-slate-100 text-slate-800 border-slate-200"
    }
  }

  const getStatusColor = (status: ScheduleEvent['status']) => {
    switch (status) {
      case "scheduled":
        return "bg-blue-50 text-blue-700 border-blue-200"
      case "in-progress":
        return "bg-green-50 text-green-700 border-green-200"
      case "completed":
        return "bg-slate-50 text-slate-700 border-slate-200"
      case "cancelled":
        return "bg-red-50 text-red-700 border-red-200"
      default:
        return "bg-slate-50 text-slate-700 border-slate-200"
    }
  }

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      month: 'long',
      day: 'numeric'
    })
  }

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)
    if (viewMode === 'week') {
      newDate.setDate(currentDate.getDate() + (direction === 'next' ? 7 : -7))
    } else {
      newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1))
    }
    setCurrentDate(newDate)
  }

  const todaysEvents = events.filter(event => {
    const today = new Date()
    const eventDate = event.startTime
    return eventDate.toDateString() === today.toDateString()
  })

  const upcomingEvents = events.filter(event => {
    const today = new Date()
    const eventDate = event.startTime
    return eventDate > today
  }).slice(0, 5)

  return (
    <div className="min-h-screen bg-white">
      <UnifiedNavigation />
      <div className="container-native section-native">
        {/* Mobile-Native Header */}
        <div className="flex flex-col gap-3 sm:gap-4 mb-5 sm:mb-6">
          <div className="space-y-1">
            <h1 className="text-native-title">Schedule</h1>
            <p className="text-native-subtitle">Manage your appointments and project timelines</p>
          </div>

          <Button size="sm" className="btn-native-primary w-full sm:w-auto justify-center">
            <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
            Add Event
          </Button>
        </div>

        {/* View Controls - Mobile-Native */}
        <div className="flex flex-col gap-3 sm:gap-4 mb-5 sm:mb-6">
          {/* Mobile: Stacked layout */}
          <div className="flex sm:hidden items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateDate('prev')}
                className="p-2 rounded-lg"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <h2 className="text-base font-medium text-slate-900">
                {currentDate.toLocaleDateString('en-US', {
                  month: 'short',
                  year: 'numeric'
                })}
              </h2>

              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateDate('next')}
                className="p-2 rounded-lg"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "week" | "month")}>
              <TabsList className="h-8">
                <TabsTrigger value="week" className="text-xs px-3">Week</TabsTrigger>
                <TabsTrigger value="month" className="text-xs px-3">Month</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          {/* Desktop: Original layout */}
          <div className="hidden sm:flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateDate('prev')}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <h2 className="text-xl font-medium text-slate-900">
                {currentDate.toLocaleDateString('en-US', {
                  month: 'long',
                  year: 'numeric'
                })}
              </h2>

              <Button
                variant="outline"
                size="sm"
                onClick={() => navigateDate('next')}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "week" | "month")}>
              <TabsList>
                <TabsTrigger value="week">Week</TabsTrigger>
                <TabsTrigger value="month">Month</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
          {/* Main Calendar/Schedule View */}
          <div className="lg:col-span-2">
            <div className="card-native-minimal mobile-spacing-md">
              <h3 className="text-native-title mb-4 sm:mb-6">
                {viewMode === 'week' ? 'This Week' : 'This Month'}
              </h3>

              <div className="space-y-3 sm:space-y-4">
                {events.map((event) => (
                  <div
                    key={event.id}
                    className="list-item-native border border-slate-200 rounded-xl hover:bg-slate-50 transition-colors"
                  >
                    {/* Mobile Layout */}
                    <div className="flex sm:hidden flex-col space-y-2 w-full">
                      <div className="flex items-start justify-between">
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-slate-900 text-sm">{event.title}</h4>
                          <p className="text-xs text-slate-600">{event.client}</p>
                        </div>
                        <Button variant="ghost" size="sm" className="p-1 h-6 w-6">
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2 text-xs text-slate-500">
                          <Clock className="h-3 w-3" />
                          <span>{formatTime(event.startTime)} - {formatTime(event.endTime)}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Badge className={`${getEventTypeColor(event.type)} text-[10px] px-1.5 py-0.5`}>
                            {event.type}
                          </Badge>
                          <Badge variant="outline" className={`${getStatusColor(event.status)} text-[10px] px-1.5 py-0.5`}>
                            {event.status}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    {/* Desktop Layout */}
                    <div className="hidden sm:flex items-start space-x-4 w-full">
                      <div className="flex-shrink-0">
                        <div className="w-3 h-3 rounded-full bg-blue-500 mt-2"></div>
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium text-slate-900">{event.title}</h4>
                            <p className="text-sm text-slate-600 mt-1">{event.client}</p>
                          </div>

                          <div className="flex items-center space-x-2">
                            <Badge className={getEventTypeColor(event.type)}>
                              {event.type}
                            </Badge>
                            <Badge variant="outline" className={getStatusColor(event.status)}>
                              {event.status}
                            </Badge>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4 mt-3 text-sm text-slate-500">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(event.startTime)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Clock className="h-4 w-4" />
                            <span>{formatTime(event.startTime)} - {formatTime(event.endTime)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-4 w-4" />
                            <span className="truncate">{event.location}</span>
                          </div>
                        </div>

                        {event.notes && (
                          <p className="text-sm text-slate-600 mt-2">{event.notes}</p>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar - Mobile-Native */}
          <div className="space-y-4 sm:space-y-6">
            {/* Today's Schedule */}
            <div className="card-native-minimal mobile-spacing-md">
              <h3 className="text-native-title mb-3 sm:mb-4">Today's Schedule</h3>

              {todaysEvents.length === 0 ? (
                <p className="text-slate-500 text-center py-4 text-sm">No appointments today</p>
              ) : (
                <div className="space-y-2 sm:space-y-3">
                  {todaysEvents.map((event) => (
                    <div key={event.id} className="flex items-center space-x-3 p-2 sm:p-3 bg-slate-50 rounded-lg">
                      <div className="w-2 h-2 rounded-full bg-blue-500 flex-shrink-0"></div>
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-slate-900 truncate text-sm">{event.title}</p>
                        <p className="text-xs text-slate-500">{formatTime(event.startTime)}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Upcoming Events */}
            <div className="card-native-minimal mobile-spacing-md">
              <h3 className="text-native-title mb-3 sm:mb-4">Upcoming</h3>

              <div className="space-y-2 sm:space-y-3">
                {upcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center space-x-3 p-2 sm:p-3 border border-slate-200 rounded-lg">
                    <div className="flex-shrink-0">
                      <User className="h-3 w-3 sm:h-4 sm:w-4 text-slate-400" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="font-medium text-slate-900 truncate text-sm">{event.title}</p>
                      <p className="text-xs text-slate-500">{event.client}</p>
                      <p className="text-xs text-slate-400">
                        {event.startTime.toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric'
                        })} at {formatTime(event.startTime)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Actions */}
            <div className="card-native-minimal mobile-spacing-md">
              <h3 className="text-native-title mb-3 sm:mb-4">Quick Actions</h3>

              <div className="space-y-2">
                <Button variant="outline" className="btn-native-secondary w-full justify-start text-sm">
                  <Plus className="h-4 w-4 mr-2 flex-shrink-0" />
                  Schedule Consultation
                </Button>
                <Button variant="outline" className="btn-native-secondary w-full justify-start text-sm">
                  <Calendar className="h-4 w-4 mr-2 flex-shrink-0" />
                  Block Time Off
                </Button>
                <Button variant="outline" className="btn-native-secondary w-full justify-start text-sm">
                  <Edit className="h-4 w-4 mr-2 flex-shrink-0" />
                  Update Availability
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default function ProtectedProSchedulePage() {
  return (
    <ProRoute>
      <ProSchedulePage />
    </ProRoute>
  )
}
