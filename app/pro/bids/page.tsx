"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { useBidding } from "@/hooks/use-bidding"
import { EnhancedCard } from "@/components/ui/enhanced-card"
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import {
  DollarSign,
  Clock,
  CheckCircle,
  AlertCircle,
  Loader2,
  Calendar,
  MapPin,
  User,
  TrendingUp,
  XCircle,
  Eye
} from "lucide-react"
import Link from "next/link"

// Using the database bid structure from Tables<'bids'>
// interface Bid is now replaced by Tables<'bids'> from the hook

export default function ProBidsPage() {
  const { user } = useUser()
  const { bids, loading, error, refetch, withdrawBid } = useBidding({
    autoFetch: true,
    contractorId: user?.id
  })
  const [activeTab, setActiveTab] = useState("pending")

  // Filter bids based on active tab
  const filteredBids = bids.filter(bid => {
    switch (activeTab) {
      case 'pending':
        return bid.status === 'pending' || bid.status === 'submitted'
      case 'accepted':
        return bid.status === 'accepted'
      case 'rejected':
        return bid.status === 'rejected'
      case 'withdrawn':
        return bid.status === 'withdrawn'
      default:
        return true
    }
  })

  const handleWithdrawBid = async (bidId: string) => {
    const success = await withdrawBid(bidId)
    if (success) {
      refetch()
    }
  }

  const getStatusBadge = (status: Bid['status']) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200 bg-yellow-50">Pending</Badge>
      case "accepted":
        return <Badge variant="outline" className="text-green-600 border-green-200 bg-green-50">Accepted</Badge>
      case "rejected":
        return <Badge variant="outline" className="text-red-600 border-red-200 bg-red-50">Rejected</Badge>
      case "withdrawn":
        return <Badge variant="outline" className="text-slate-600 border-slate-200 bg-slate-50">Withdrawn</Badge>
      default:
        return null
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending":
        return <Clock className="h-4 w-4 text-yellow-600" />
      case "accepted":
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case "rejected":
        return <XCircle className="h-4 w-4 text-red-600" />
      case "withdrawn":
        return <XCircle className="h-4 w-4 text-slate-600" />
      default:
        return null
    }
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const isExpiringSoon = (expiresAt: Date) => {
    const now = new Date()
    const diffTime = expiresAt.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays <= 2 && diffDays > 0
  }

  const isExpired = (expiresAt: Date) => {
    return new Date() > expiresAt
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-7xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-light text-slate-900 mb-2">My Bids</h1>
          <p className="text-slate-500">Track your project bids and proposals</p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-500 mb-1">Total Bids</p>
                <p className="text-2xl font-light text-slate-900">{bids.length}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-brand-secondary" />
            </div>
          </EnhancedCard>
          
          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-500 mb-1">Pending</p>
                <p className="text-2xl font-light text-slate-900">
                  {bids.filter(b => b.status === 'pending' || b.status === 'submitted').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </EnhancedCard>
          
          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-500 mb-1">Accepted</p>
                <p className="text-2xl font-light text-slate-900">
                  {bids.filter(b => b.status === 'accepted').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </EnhancedCard>
          
          <EnhancedCard className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-slate-500 mb-1">Win Rate</p>
                <p className="text-2xl font-light text-slate-900">
                  {bids.length > 0 ? Math.round((bids.filter(b => b.status === 'accepted').length / bids.length) * 100) : 0}%
                </p>
              </div>
              <TrendingUp className="h-8 w-8 text-brand-primary" />
            </div>
          </EnhancedCard>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-8">
          <TabsList className="grid w-full grid-cols-4 lg:w-auto">
            <TabsTrigger value="pending">Pending</TabsTrigger>
            <TabsTrigger value="accepted">Accepted</TabsTrigger>
            <TabsTrigger value="rejected">Rejected</TabsTrigger>
            <TabsTrigger value="withdrawn">Withdrawn</TabsTrigger>
          </TabsList>

          <TabsContent value={activeTab} className="mt-6">
            {/* Loading State */}
            {loading && (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <EnhancedCard key={i} loading={true} />
                ))}
              </div>
            )}

            {/* Error State */}
            {error && (
              <EnhancedCard className="p-6 text-center">
                <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-slate-900 mb-2">Error Loading Bids</h3>
                <p className="text-slate-500 mb-4">{error}</p>
                <Button onClick={loadBids} className="bg-brand-secondary hover:bg-brand-secondary/90">
                  <Loader2 className="h-4 w-4 mr-2" />
                  Try Again
                </Button>
              </EnhancedCard>
            )}

            {/* Bids List */}
            {!loading && !error && (
              <div className="space-y-4">
                {filteredBids.length === 0 ? (
                  <EnhancedCard className="p-12 text-center">
                    <DollarSign className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-900 mb-2">No {activeTab} bids</h3>
                    <p className="text-slate-500 mb-6">
                      {activeTab === "pending" ? "Submit bids on available projects to get started" : `You don't have any ${activeTab} bids yet`}
                    </p>
                    {activeTab === "pending" && (
                      <Link href="/pro/projects">
                        <Button className="bg-brand-secondary hover:bg-brand-secondary/90">
                          Browse Projects
                        </Button>
                      </Link>
                    )}
                  </EnhancedCard>
                ) : (
                  filteredBids.map((bid) => (
                    <EnhancedCard key={bid.id} className="p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            {getStatusIcon(bid.status)}
                            <h3 className="text-lg font-medium text-slate-900">
                              {bid.projects?.title || 'Project'}
                            </h3>
                            {getStatusBadge(bid.status)}
                            {bid.status === "pending" && bid.expires_at && isExpiringSoon(new Date(bid.expires_at)) && (
                              <Badge variant="outline" className="text-orange-600 border-orange-200 bg-orange-50">
                                Expires Soon
                              </Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-slate-500 mb-4">
                            <div className="flex items-center space-x-2">
                              <User className="h-4 w-4" />
                              <span>{bid.projects?.users?.name || 'Client'}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="h-4 w-4" />
                              <span>{bid.projects?.location?.address || 'Location'}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4" />
                              <span>{bid.timeline?.duration ? `${bid.timeline.duration} days` : 'Timeline TBD'}</span>
                            </div>
                          </div>
                          
                          <p className="text-slate-600 mb-4">{bid.description}</p>
                          
                          <div className="flex items-center justify-between text-sm text-slate-500">
                            <span>Submitted {formatDate(new Date(bid.submitted_at || bid.created_at))}</span>
                            {bid.status === "pending" && bid.expires_at && (
                              <span>
                                Expires {formatDate(new Date(bid.expires_at))}
                                {isExpired(new Date(bid.expires_at)) && " (Expired)"}
                              </span>
                            )}
                          </div>
                        </div>
                        
                        <div className="ml-6 text-right">
                          <div className="text-2xl font-semibold text-slate-900 mb-2">
                            {formatCurrency(bid.amount)}
                          </div>
                          <div className="flex space-x-2">
                            <Link href={`/pro/projects/${bid.project_id}`}>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4 mr-1" />
                                View
                              </Button>
                            </Link>
                            {bid.status === "pending" && (!bid.expires_at || !isExpired(new Date(bid.expires_at))) && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-red-600 hover:text-red-700"
                                onClick={() => handleWithdrawBid(bid.id)}
                              >
                                Withdraw
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </EnhancedCard>
                  ))
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      <RoleSwitcher />
    </div>
  )
}
