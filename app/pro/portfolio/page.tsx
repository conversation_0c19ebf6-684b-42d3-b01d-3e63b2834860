"use client"

import { UnifiedNavigation } from '@/components/unified-navigation'
import { PortfolioManager } from '@/components/portfolio/portfolio-manager'
import { ProRoute } from '@/components/auth/pro-route'

export default function ProPortfolioPage() {
  return (
    <ProRoute>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <UnifiedNavigation />
        
        <div className="container-premium section-premium">
          <PortfolioManager />
        </div>
      </div>
    </ProRoute>
  )
}
