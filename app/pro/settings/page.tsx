"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { useUser } from "@/contexts/user-context"
import {
  Edit3,
  Check,
  Camera,
  Star,
  MapPin,
  Phone,
  Mail,
  Globe,
  Shield,
  Bell,
  Eye,
  CreditCard,
  FileText,
  Settings as SettingsIcon,
  CheckCircle,
  AlertCircle,
  Download
} from "lucide-react"

interface ProProfile {
  name: string
  email: string
  phone: string
  company: string
  location: string
  website: string
  bio: string
  specialties: string[]
  yearsExperience: number
  licenseNumber: string
  insuranceProvider: string
  hourlyRate: string
  availability: string
}

export default function ProSettingsPage() {
  const { user } = useUser()
  const [isEditing, setIsEditing] = useState(false)
  const [activeTab, setActiveTab] = useState("profile")
  const [profile, setProfile] = useState<ProProfile>({
    name: user?.name || "<PERSON>",
    email: user?.email || "<EMAIL>",
    phone: "(*************",
    company: "Mike's Kitchen Experts",
    location: "San Francisco, CA",
    website: "www.mikeskitchenexperts.com",
    bio: "With over 15 years of experience in kitchen remodeling, I specialize in creating beautiful, functional kitchens that exceed expectations. I pride myself on quality craftsmanship, attention to detail, and exceptional customer service.",
    specialties: ["Kitchen Remodeling", "Cabinet Installation", "Countertop Installation", "Appliance Installation"],
    yearsExperience: 15,
    licenseNumber: "CA-*********",
    insuranceProvider: "State Farm Business Insurance",
    hourlyRate: "$85-120",
    availability: "Monday - Friday, 8 AM - 6 PM"
  })

  const [notifications, setNotifications] = useState({
    newProjects: true,
    messages: true,
    bidUpdates: true,
    marketing: false
  })

  const [privacy, setPrivacy] = useState({
    showPhone: true,
    showEmail: true,
    showRates: true,
    profileVisible: true
  })

  const handleSave = () => {
    // Save profile changes
    setIsEditing(false)
    // Here you would typically make an API call to save the changes
  }

  const tabs = [
    { id: "profile", label: "Profile", icon: Edit3 },
    { id: "business", label: "Business Info", icon: FileText },
    { id: "notifications", label: "Notifications", icon: Bell },
    { id: "privacy", label: "Privacy", icon: Eye },
    { id: "billing", label: "Billing", icon: CreditCard },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-12 gap-4">
          <div>
            <h1 className="text-3xl font-semibold text-slate-900 mb-2">Profile & Settings</h1>
            <p className="text-lg text-slate-600">Manage your professional profile and account preferences</p>
          </div>

          {activeTab === "profile" && (
            <Button
              onClick={() => (isEditing ? handleSave() : setIsEditing(true))}
              className={`${
                isEditing 
                  ? "bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg" 
                  : "bg-white border-2 border-slate-200 hover:border-slate-300 text-slate-700 hover:bg-slate-50"
              } font-medium px-6 py-3 rounded-xl transition-all duration-200`}
            >
              {isEditing ? (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Save Changes
                </>
              ) : (
                <>
                  <Edit3 className="h-4 w-4 mr-2" />
                  Edit Profile
                </>
              )}
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <div className="card-premium p-6">
              <nav className="space-y-2">
                {tabs.map((tab) => {
                  const Icon = tab.icon
                  return (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-all duration-200 ${
                        activeTab === tab.id
                          ? "bg-blue-50 text-blue-700 border-l-4 border-l-blue-500"
                          : "text-slate-600 hover:bg-slate-50 hover:text-slate-900"
                      }`}
                    >
                      <Icon className="h-5 w-5" />
                      <span className="font-medium">{tab.label}</span>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {activeTab === "profile" && (
              <div className="space-y-8">
                {/* Profile Information */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-8">Profile Information</h2>

                  {/* Avatar Section */}
                  <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-8 mb-10">
                    <div className="relative">
                      <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                        <span className="text-3xl font-semibold text-white">{profile.name.charAt(0)}</span>
                      </div>
                      {isEditing && (
                        <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-slate-900 text-white rounded-full flex items-center justify-center hover:bg-slate-800 transition-all duration-200 shadow-lg">
                          <Camera className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                    <div className="flex-1">
                      <h3 className="text-2xl font-semibold text-slate-900 mb-1">{profile.name}</h3>
                      <p className="text-slate-600 mb-3">{profile.company}</p>
                      <div className="flex flex-wrap items-center gap-4">
                        <div className="flex items-center space-x-2">
                          <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                          <span className="text-sm font-medium text-emerald-700">Verified Professional</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Star className="h-4 w-4 text-amber-500 fill-current" />
                          <span className="text-sm font-medium text-slate-700">4.9 Rating</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Shield className="h-4 w-4 text-blue-500" />
                          <span className="text-sm font-medium text-slate-700">Licensed & Insured</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Form Fields */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label htmlFor="name" className="text-sm font-semibold text-slate-700">
                        Full Name
                      </Label>
                      <Input
                        id="name"
                        value={profile.name}
                        onChange={(e) => setProfile((prev) => ({ ...prev, name: e.target.value }))}
                        disabled={!isEditing}
                        className={`h-12 ${
                          isEditing 
                            ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10" 
                            : "border-slate-200 bg-slate-50/50"
                        } transition-all duration-200`}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="email" className="text-sm font-semibold text-slate-700">
                        Email Address
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        value={profile.email}
                        onChange={(e) => setProfile((prev) => ({ ...prev, email: e.target.value }))}
                        disabled={!isEditing}
                        className={`h-12 ${
                          isEditing 
                            ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10" 
                            : "border-slate-200 bg-slate-50/50"
                        } transition-all duration-200`}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="phone" className="text-sm font-semibold text-slate-700">
                        Phone Number
                      </Label>
                      <Input
                        id="phone"
                        value={profile.phone}
                        onChange={(e) => setProfile((prev) => ({ ...prev, phone: e.target.value }))}
                        disabled={!isEditing}
                        className={`h-12 ${
                          isEditing 
                            ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10" 
                            : "border-slate-200 bg-slate-50/50"
                        } transition-all duration-200`}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="company" className="text-sm font-semibold text-slate-700">
                        Company Name
                      </Label>
                      <Input
                        id="company"
                        value={profile.company}
                        onChange={(e) => setProfile((prev) => ({ ...prev, company: e.target.value }))}
                        disabled={!isEditing}
                        className={`h-12 ${
                          isEditing 
                            ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10" 
                            : "border-slate-200 bg-slate-50/50"
                        } transition-all duration-200`}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="location" className="text-sm font-semibold text-slate-700">
                        Location
                      </Label>
                      <Input
                        id="location"
                        value={profile.location}
                        onChange={(e) => setProfile((prev) => ({ ...prev, location: e.target.value }))}
                        disabled={!isEditing}
                        className={`h-12 ${
                          isEditing 
                            ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10" 
                            : "border-slate-200 bg-slate-50/50"
                        } transition-all duration-200`}
                      />
                    </div>

                    <div className="space-y-3">
                      <Label htmlFor="website" className="text-sm font-semibold text-slate-700">
                        Website
                      </Label>
                      <Input
                        id="website"
                        value={profile.website}
                        onChange={(e) => setProfile((prev) => ({ ...prev, website: e.target.value }))}
                        disabled={!isEditing}
                        className={`h-12 ${
                          isEditing 
                            ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10" 
                            : "border-slate-200 bg-slate-50/50"
                        } transition-all duration-200`}
                      />
                    </div>
                  </div>

                  <div className="mt-6 space-y-3">
                    <Label htmlFor="bio" className="text-sm font-semibold text-slate-700">
                      Professional Bio
                    </Label>
                    <Textarea
                      id="bio"
                      value={profile.bio}
                      onChange={(e) => setProfile((prev) => ({ ...prev, bio: e.target.value }))}
                      disabled={!isEditing}
                      className={`min-h-[120px] ${
                        isEditing
                          ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10"
                          : "border-slate-200 bg-slate-50/50"
                      } transition-all duration-200`}
                      placeholder="Tell potential clients about your experience, specialties, and what makes you unique..."
                    />
                  </div>
                </div>

                {/* Specialties Section */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Specialties & Services</h2>
                  <div className="space-y-4">
                    <Label className="text-sm font-semibold text-slate-700">
                      Your Specialties
                    </Label>
                    <div className="flex flex-wrap gap-2">
                      {profile.specialties.map((specialty, index) => (
                        <div key={index} className="flex items-center space-x-2 bg-blue-50 text-blue-700 px-3 py-2 rounded-lg">
                          <span className="text-sm font-medium">{specialty}</span>
                          {isEditing && (
                            <button
                              onClick={() => {
                                const newSpecialties = profile.specialties.filter((_, i) => i !== index)
                                setProfile(prev => ({ ...prev, specialties: newSpecialties }))
                              }}
                              className="text-blue-500 hover:text-blue-700"
                            >
                              ×
                            </button>
                          )}
                        </div>
                      ))}
                      {isEditing && (
                        <button
                          onClick={() => {
                            const newSpecialty = prompt("Enter a new specialty:")
                            if (newSpecialty && !profile.specialties.includes(newSpecialty)) {
                              setProfile(prev => ({ ...prev, specialties: [...prev.specialties, newSpecialty] }))
                            }
                          }}
                          className="px-3 py-2 border-2 border-dashed border-slate-300 text-slate-500 rounded-lg hover:border-slate-400 hover:text-slate-600 transition-colors"
                        >
                          + Add Specialty
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "business" && (
              <div className="space-y-8">
                {/* Business Information */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-8">Business Information</h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-slate-700">Years of Experience</Label>
                        <Input
                          value={profile.yearsExperience}
                          onChange={(e) => setProfile(prev => ({ ...prev, yearsExperience: parseInt(e.target.value) || 0 }))}
                          className="h-12"
                          type="number"
                          min="0"
                          max="50"
                        />
                      </div>
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-slate-700">Hourly Rate Range</Label>
                        <Input
                          value={profile.hourlyRate}
                          onChange={(e) => setProfile(prev => ({ ...prev, hourlyRate: e.target.value }))}
                          className="h-12"
                          placeholder="e.g., $85-120"
                        />
                      </div>
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-slate-700">License Number</Label>
                        <Input
                          value={profile.licenseNumber}
                          onChange={(e) => setProfile(prev => ({ ...prev, licenseNumber: e.target.value }))}
                          className="h-12"
                          placeholder="e.g., CA-*********"
                        />
                      </div>
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-slate-700">Insurance Provider</Label>
                        <Input
                          value={profile.insuranceProvider}
                          onChange={(e) => setProfile(prev => ({ ...prev, insuranceProvider: e.target.value }))}
                          className="h-12"
                          placeholder="e.g., State Farm Business"
                        />
                      </div>
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-slate-700">Availability Schedule</Label>
                      <Input
                        value={profile.availability}
                        onChange={(e) => setProfile(prev => ({ ...prev, availability: e.target.value }))}
                        className="h-12"
                        placeholder="e.g., Monday - Friday, 8 AM - 6 PM"
                      />
                    </div>
                  </div>
                </div>

                {/* Service Areas */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Service Areas</h2>
                  <div className="space-y-4">
                    <Label className="text-sm font-semibold text-slate-700">
                      Areas You Serve
                    </Label>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-slate-600">Primary Service Area</Label>
                        <Input
                          placeholder="e.g., San Francisco, CA"
                          className="h-12"
                        />
                      </div>
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-slate-600">Service Radius (miles)</Label>
                        <Input
                          type="number"
                          placeholder="25"
                          className="h-12"
                          min="1"
                          max="100"
                        />
                      </div>
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-slate-600">Additional Service Areas</Label>
                      <Textarea
                        placeholder="List additional cities or areas you serve, separated by commas"
                        className="min-h-[80px]"
                      />
                    </div>
                  </div>
                </div>

                {/* Certifications */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Certifications & Credentials</h2>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-slate-700">Professional Certifications</Label>
                        <div className="space-y-2">
                          {["Licensed Contractor", "Insured", "BBB Accredited", "OSHA Certified"].map((cert) => (
                            <div key={cert} className="flex items-center space-x-3 p-3 bg-emerald-50 border border-emerald-200 rounded-lg">
                              <CheckCircle className="h-5 w-5 text-emerald-500" />
                              <span className="text-sm font-medium text-emerald-800">{cert}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      <div className="space-y-3">
                        <Label className="text-sm font-semibold text-slate-700">Add New Certification</Label>
                        <div className="space-y-3">
                          <Input placeholder="Certification Name" className="h-12" />
                          <Input placeholder="Issuing Organization" className="h-12" />
                          <Input type="date" placeholder="Issue Date" className="h-12" />
                          <Button variant="outline" className="w-full">
                            Add Certification
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "notifications" && (
              <div className="space-y-8">
                {/* Email Notifications */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-8">Email Notifications</h2>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">New Project Opportunities</h3>
                        <p className="text-sm text-slate-600">Get notified when new projects match your specialties</p>
                      </div>
                      <Switch
                        checked={notifications.newProjects}
                        onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, newProjects: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Messages from Clients</h3>
                        <p className="text-sm text-slate-600">Receive notifications for new messages and inquiries</p>
                      </div>
                      <Switch
                        checked={notifications.messages}
                        onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, messages: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Bid Status Updates</h3>
                        <p className="text-sm text-slate-600">Updates when your bids are accepted, rejected, or reviewed</p>
                      </div>
                      <Switch
                        checked={notifications.bidUpdates}
                        onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, bidUpdates: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Marketing & Promotions</h3>
                        <p className="text-sm text-slate-600">Tips, platform updates, and promotional offers</p>
                      </div>
                      <Switch
                        checked={notifications.marketing}
                        onCheckedChange={(checked) => setNotifications(prev => ({ ...prev, marketing: checked }))}
                      />
                    </div>
                  </div>
                </div>

                {/* Push Notifications */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Push Notifications</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Urgent Messages</h3>
                        <p className="text-sm text-slate-600">High-priority client communications</p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Project Deadlines</h3>
                        <p className="text-sm text-slate-600">Reminders for upcoming project milestones</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </div>

                {/* Notification Schedule */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Notification Schedule</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-slate-700">Quiet Hours Start</Label>
                      <Input type="time" defaultValue="22:00" className="h-12" />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-slate-700">Quiet Hours End</Label>
                      <Input type="time" defaultValue="08:00" className="h-12" />
                    </div>
                  </div>
                  <p className="text-sm text-slate-600 mt-4">
                    During quiet hours, you'll only receive urgent notifications.
                  </p>
                </div>
              </div>
            )}

            {activeTab === "privacy" && (
              <div className="space-y-8">
                {/* Profile Visibility */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-8">Profile Visibility</h2>
                  <div className="space-y-6">
                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Public Profile</h3>
                        <p className="text-sm text-slate-600">Make your profile visible in search results</p>
                      </div>
                      <Switch
                        checked={privacy.profileVisible}
                        onCheckedChange={(checked) => setPrivacy(prev => ({ ...prev, profileVisible: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Show Phone Number</h3>
                        <p className="text-sm text-slate-600">Display your phone number on your public profile</p>
                      </div>
                      <Switch
                        checked={privacy.showPhone}
                        onCheckedChange={(checked) => setPrivacy(prev => ({ ...prev, showPhone: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Show Email Address</h3>
                        <p className="text-sm text-slate-600">Display your email address on your public profile</p>
                      </div>
                      <Switch
                        checked={privacy.showEmail}
                        onCheckedChange={(checked) => setPrivacy(prev => ({ ...prev, showEmail: checked }))}
                      />
                    </div>

                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Show Hourly Rates</h3>
                        <p className="text-sm text-slate-600">Display your pricing information publicly</p>
                      </div>
                      <Switch
                        checked={privacy.showRates}
                        onCheckedChange={(checked) => setPrivacy(prev => ({ ...prev, showRates: checked }))}
                      />
                    </div>
                  </div>
                </div>

                {/* Communication Preferences */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Communication Preferences</h2>
                  <div className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-slate-700">Who can contact you directly?</Label>
                      <div className="space-y-2">
                        <label className="flex items-center space-x-3">
                          <input type="radio" name="contact" value="anyone" defaultChecked className="text-blue-600" />
                          <span className="text-sm text-slate-700">Anyone with a verified account</span>
                        </label>
                        <label className="flex items-center space-x-3">
                          <input type="radio" name="contact" value="clients" className="text-blue-600" />
                          <span className="text-sm text-slate-700">Only clients who have hired me</span>
                        </label>
                        <label className="flex items-center space-x-3">
                          <input type="radio" name="contact" value="none" className="text-blue-600" />
                          <span className="text-sm text-slate-700">No direct contact (platform messages only)</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Data & Analytics */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Data & Analytics</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Performance Analytics</h3>
                        <p className="text-sm text-slate-600">Allow RenovHub to track your profile performance</p>
                      </div>
                      <Switch defaultChecked />
                    </div>

                    <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
                      <div>
                        <h3 className="text-base font-medium text-slate-900">Usage Data</h3>
                        <p className="text-sm text-slate-600">Help improve the platform with anonymous usage data</p>
                      </div>
                      <Switch defaultChecked />
                    </div>
                  </div>
                </div>

                {/* Account Security */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Account Security</h2>
                  <div className="space-y-4">
                    <Button variant="outline" className="w-full justify-start">
                      <Shield className="h-4 w-4 mr-2" />
                      Change Password
                    </Button>
                    <Button variant="outline" className="w-full justify-start">
                      <Shield className="h-4 w-4 mr-2" />
                      Enable Two-Factor Authentication
                    </Button>
                    <Button variant="outline" className="w-full justify-start text-red-600 border-red-200 hover:bg-red-50">
                      <AlertCircle className="h-4 w-4 mr-2" />
                      Delete Account
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {activeTab === "billing" && (
              <div className="space-y-8">
                {/* Current Plan */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-8">Current Plan</h2>
                  <div className="bg-gradient-to-r from-emerald-50 to-blue-50 border border-emerald-200 rounded-xl p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h3 className="text-lg font-semibold text-slate-900">Professional Plan</h3>
                        <p className="text-sm text-slate-600">Full access to all contractor features</p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-slate-900">$29</div>
                        <div className="text-sm text-slate-600">per month</div>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-emerald-500" />
                        <span>Unlimited bids</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-emerald-500" />
                        <span>Priority support</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-emerald-500" />
                        <span>Analytics dashboard</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-emerald-500" />
                        <span>Featured listings</span>
                      </div>
                    </div>
                  </div>
                  <div className="mt-6 flex flex-col sm:flex-row gap-3">
                    <Button variant="outline" className="flex-1">
                      Change Plan
                    </Button>
                    <Button variant="outline" className="flex-1 text-red-600 border-red-200 hover:bg-red-50">
                      Cancel Subscription
                    </Button>
                  </div>
                </div>

                {/* Payment Method */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Payment Method</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-8 bg-slate-900 rounded flex items-center justify-center">
                          <CreditCard className="h-4 w-4 text-white" />
                        </div>
                        <div>
                          <p className="font-medium text-slate-900">•••• •••• •••• 4242</p>
                          <p className="text-sm text-slate-600">Expires 12/25</p>
                        </div>
                      </div>
                      <Button variant="outline" size="sm">
                        Edit
                      </Button>
                    </div>
                    <Button variant="outline" className="w-full">
                      Add New Payment Method
                    </Button>
                  </div>
                </div>

                {/* Billing History */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Billing History</h2>
                  <div className="space-y-4">
                    {[
                      { date: "Dec 1, 2024", amount: "$29.00", status: "Paid", invoice: "INV-001" },
                      { date: "Nov 1, 2024", amount: "$29.00", status: "Paid", invoice: "INV-002" },
                      { date: "Oct 1, 2024", amount: "$29.00", status: "Paid", invoice: "INV-003" },
                    ].map((bill, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border border-slate-200 rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div>
                            <p className="font-medium text-slate-900">{bill.date}</p>
                            <p className="text-sm text-slate-600">Invoice {bill.invoice}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <span className="font-medium text-slate-900">{bill.amount}</span>
                          <span className="px-2 py-1 bg-emerald-100 text-emerald-800 text-xs rounded-full">
                            {bill.status}
                          </span>
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Tax Information */}
                <div className="card-premium p-8">
                  <h2 className="text-2xl font-semibold text-slate-900 mb-6">Tax Information</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-slate-700">Business Tax ID</Label>
                      <Input placeholder="Enter your tax ID" className="h-12" />
                    </div>
                    <div className="space-y-3">
                      <Label className="text-sm font-semibold text-slate-700">Business Type</Label>
                      <Input placeholder="e.g., LLC, Corporation" className="h-12" />
                    </div>
                  </div>
                  <div className="mt-6">
                    <Button variant="outline">
                      <FileText className="h-4 w-4 mr-2" />
                      Download Tax Documents
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
