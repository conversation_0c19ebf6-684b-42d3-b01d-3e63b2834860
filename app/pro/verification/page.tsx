"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useUser } from "@/contexts/user-context"
import { 
  Shield, 
  CheckCircle, 
  Clock, 
  AlertCircle, 
  FileText, 
  Upload,
  Phone,
  Mail,
  ExternalLink,
  ArrowRight
} from "lucide-react"

export default function VerificationPage() {
  const [verificationStatus] = useState({
    identity: 'pending',
    license: 'pending', 
    insurance: 'verified',
    background: 'pending'
  })
  const router = useRouter()
  const { user } = useUser()

  const verificationSteps = [
    {
      id: 'identity',
      title: 'Identity Verification',
      description: 'Verify your identity with government-issued ID',
      status: verificationStatus.identity,
      icon: Shield,
      action: 'Upload ID',
      required: true
    },
    {
      id: 'license',
      title: 'Contractor License',
      description: 'Verify your contractor license with state authorities',
      status: verificationStatus.license,
      icon: FileText,
      action: 'Verify License',
      required: true
    },
    {
      id: 'insurance',
      title: 'Insurance Coverage',
      description: 'Verify your liability insurance coverage',
      status: verificationStatus.insurance,
      icon: Shield,
      action: 'Upload Certificate',
      required: true
    },
    {
      id: 'background',
      title: 'Background Check',
      description: 'Complete background check for customer safety',
      status: verificationStatus.background,
      icon: CheckCircle,
      action: 'Start Check',
      required: false
    }
  ]

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'verified':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />
      case 'failed':
        return <AlertCircle className="h-5 w-5 text-red-600" />
      default:
        return <Clock className="h-5 w-5 text-slate-400" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'verified':
        return <Badge className="bg-green-100 text-green-700">Verified</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-700">Pending</Badge>
      case 'failed':
        return <Badge className="bg-red-100 text-red-700">Failed</Badge>
      default:
        return <Badge variant="outline">Not Started</Badge>
    }
  }

  const completedSteps = verificationSteps.filter(step => step.status === 'verified').length
  const totalSteps = verificationSteps.length
  const progressPercentage = (completedSteps / totalSteps) * 100

  const canAccessDashboard = verificationSteps
    .filter(step => step.required)
    .every(step => step.status === 'verified')

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50/30">
      <div className="container mx-auto px-6 py-12">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Shield className="h-8 w-8 text-blue-600" />
            </div>
            <h1 className="text-3xl font-bold text-slate-900 mb-4">
              Account Verification
            </h1>
            <p className="text-slate-600 text-lg max-w-2xl mx-auto">
              Complete your verification to start bidding on projects and build trust with customers
            </p>
          </div>

          {/* Progress Overview */}
          <Card className="p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-slate-900">
                Verification Progress
              </h2>
              <span className="text-sm text-slate-600">
                {completedSteps} of {totalSteps} completed
              </span>
            </div>
            
            <div className="w-full bg-slate-200 rounded-full h-3 mb-4">
              <div
                className="bg-gradient-to-r from-blue-600 to-emerald-600 h-3 rounded-full transition-all duration-500"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>

            {canAccessDashboard ? (
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <div className="flex items-center">
                  <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                  <div className="flex-1">
                    <p className="font-medium text-green-900">
                      Verification Complete!
                    </p>
                    <p className="text-sm text-green-700">
                      You can now access your dashboard and start bidding on projects.
                    </p>
                  </div>
                  <Button onClick={() => router.push('/pro/dashboard')}>
                    Go to Dashboard
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                </div>
              </div>
            ) : (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-yellow-600 mr-3" />
                  <div>
                    <p className="font-medium text-yellow-900">
                      Verification in Progress
                    </p>
                    <p className="text-sm text-yellow-700">
                      Complete all required verifications to access your dashboard.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </Card>

          {/* Verification Steps */}
          <div className="space-y-6">
            {verificationSteps.map((step, index) => (
              <Card key={step.id} className="p-6">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-slate-100 rounded-full flex items-center justify-center">
                      <step.icon className="h-6 w-6 text-slate-600" />
                    </div>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-semibold text-slate-900">
                        {step.title}
                        {step.required && (
                          <span className="text-red-500 ml-1">*</span>
                        )}
                      </h3>
                      <div className="flex items-center space-x-3">
                        {getStatusIcon(step.status)}
                        {getStatusBadge(step.status)}
                      </div>
                    </div>
                    
                    <p className="text-slate-600 mb-4">
                      {step.description}
                    </p>

                    {step.status === 'verified' ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        <span className="text-sm font-medium">Verified</span>
                      </div>
                    ) : (
                      <Button 
                        variant={step.status === 'pending' ? 'default' : 'outline'}
                        size="sm"
                      >
                        <Upload className="h-4 w-4 mr-2" />
                        {step.action}
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Help Section */}
          <Card className="p-6 mt-8">
            <h3 className="text-lg font-semibold text-slate-900 mb-4">
              Need Help with Verification?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="flex items-start space-x-3">
                <Phone className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <p className="font-medium text-slate-900">Call Support</p>
                  <p className="text-sm text-slate-600">
                    Speak with our verification team
                  </p>
                  <p className="text-sm text-blue-600 font-medium">
                    (*************
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Mail className="h-5 w-5 text-blue-600 mt-1" />
                <div>
                  <p className="font-medium text-slate-900">Email Support</p>
                  <p className="text-sm text-slate-600">
                    Get help via email
                  </p>
                  <p className="text-sm text-blue-600 font-medium">
                    <EMAIL>
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-6 pt-6 border-t border-slate-200">
              <Link href="/pro/verification/guidelines">
                <Button variant="outline" className="w-full">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  View Verification Guidelines
                </Button>
              </Link>
            </div>
          </Card>

          {/* Skip Verification */}
          <Card className="p-6 mt-8 bg-yellow-50 border-yellow-200">
            <div className="text-center">
              <h3 className="text-lg font-semibold text-yellow-900 mb-2">
                Want to complete verification later?
              </h3>
              <p className="text-yellow-700 mb-6">
                You can skip verification for now and complete it later. However, you'll have limited access to features until verified.
              </p>
              <div className="flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  variant="outline"
                  onClick={() => router.push('/pro/dashboard?demo=true')}
                  className="border-yellow-300 text-yellow-700 hover:bg-yellow-100"
                >
                  View Demo Dashboard
                </Button>
                <Button
                  onClick={() => router.push('/pro/dashboard?skip_verification=true')}
                  className="bg-yellow-600 hover:bg-yellow-700 text-white"
                >
                  Skip for Now & Continue
                </Button>
              </div>
              <p className="text-xs text-yellow-600 mt-3">
                You can return to complete verification anytime from your dashboard
              </p>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
