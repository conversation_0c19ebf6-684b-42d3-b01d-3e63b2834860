"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { PageHeader } from "@/components/breadcrumb"
import { 
  Shield, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Camera,
  Upload,
  Clock,
  ArrowLeft,
  Download,
  ExternalLink
} from "lucide-react"
import Link from "next/link"

export default function VerificationGuidelinesPage() {
  const guidelines = [
    {
      title: "Identity Verification",
      icon: Shield,
      requirements: [
        "Government-issued photo ID (Driver's License, Passport, or State ID)",
        "Clear, high-resolution photo showing all details",
        "Document must be current and not expired",
        "Name on ID must match your account name"
      ],
      tips: [
        "Ensure good lighting when taking photos",
        "Avoid glare or shadows on the document",
        "Include all four corners of the ID in the photo"
      ]
    },
    {
      title: "Contractor License",
      icon: FileText,
      requirements: [
        "Valid contractor license for your state/region",
        "License must be in good standing",
        "Specialty licenses if applicable (electrical, plumbing, etc.)",
        "License number must be clearly visible"
      ],
      tips: [
        "Check your state's contractor licensing board website",
        "Ensure license is not suspended or expired",
        "Include any endorsements or specialty certifications"
      ]
    },
    {
      title: "Insurance Coverage",
      icon: Shield,
      requirements: [
        "General liability insurance certificate",
        "Minimum $1M coverage recommended",
        "Certificate must show current policy dates",
        "Your business name must match the policy"
      ],
      tips: [
        "Contact your insurance provider for a current certificate",
        "Ensure the certificate shows RenovHub as additional insured",
        "Workers' compensation may be required in some states"
      ]
    },
    {
      title: "Background Check",
      icon: CheckCircle,
      requirements: [
        "Clean criminal background check",
        "No felony convictions related to fraud or theft",
        "Verification of identity and address",
        "May include credit check for financial responsibility"
      ],
      tips: [
        "Process typically takes 3-5 business days",
        "You'll receive email updates on status",
        "Minor infractions may not disqualify you"
      ]
    }
  ]

  const documentSpecs = [
    {
      type: "Photos",
      formats: "JPG, PNG, WebP",
      maxSize: "10 MB per file",
      resolution: "Minimum 1200x800 pixels",
      quality: "Clear, well-lit, no blur"
    },
    {
      type: "Documents",
      formats: "PDF, JPG, PNG",
      maxSize: "25 MB per file",
      resolution: "High resolution scan or photo",
      quality: "All text must be clearly readable"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50/30">
      <UnifiedNavigation />
      
      <div className="container mx-auto px-6 py-8">
        <PageHeader
          title="Verification Guidelines"
          description="Complete guide to contractor verification requirements and best practices"
          actions={
            <Link href="/pro/verification">
              <Button variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Verification
              </Button>
            </Link>
          }
        />

        {/* Overview */}
        <Card className="p-6 mb-8">
          <div className="flex items-start space-x-4">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <Shield className="h-6 w-6 text-blue-600" />
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-semibold text-slate-900 mb-2">
                Why Verification Matters
              </h2>
              <p className="text-slate-600 mb-4">
                Our verification process ensures customer safety and builds trust in our marketplace. 
                Verified contractors receive more project invitations and higher customer confidence.
              </p>
              <div className="flex flex-wrap gap-4 text-sm">
                <div className="flex items-center text-green-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Increased project visibility
                </div>
                <div className="flex items-center text-green-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Higher customer trust
                </div>
                <div className="flex items-center text-green-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Premium marketplace features
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Verification Requirements */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          {guidelines.map((guideline, index) => (
            <Card key={index} className="p-6">
              <div className="flex items-center mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                  <guideline.icon className="h-5 w-5 text-blue-600" />
                </div>
                <h3 className="text-lg font-semibold text-slate-900">
                  {guideline.title}
                </h3>
              </div>

              <div className="space-y-4">
                <div>
                  <h4 className="font-medium text-slate-900 mb-2">Requirements:</h4>
                  <ul className="space-y-1">
                    {guideline.requirements.map((req, reqIndex) => (
                      <li key={reqIndex} className="flex items-start text-sm text-slate-600">
                        <CheckCircle className="h-3 w-3 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        {req}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="font-medium text-slate-900 mb-2">Tips:</h4>
                  <ul className="space-y-1">
                    {guideline.tips.map((tip, tipIndex) => (
                      <li key={tipIndex} className="flex items-start text-sm text-slate-600">
                        <AlertCircle className="h-3 w-3 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" />
                        {tip}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </Card>
          ))}
        </div>

        {/* Document Specifications */}
        <Card className="p-6 mb-8">
          <h2 className="text-xl font-semibold text-slate-900 mb-6">
            Document Upload Specifications
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {documentSpecs.map((spec, index) => (
              <div key={index} className="border border-slate-200 rounded-lg p-4">
                <h3 className="font-medium text-slate-900 mb-3">{spec.type}</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-slate-600">Formats:</span>
                    <span className="font-medium">{spec.formats}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Max Size:</span>
                    <span className="font-medium">{spec.maxSize}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Resolution:</span>
                    <span className="font-medium">{spec.resolution}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-slate-600">Quality:</span>
                    <span className="font-medium">{spec.quality}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>

        {/* Timeline & Process */}
        <Card className="p-6 mb-8">
          <h2 className="text-xl font-semibold text-slate-900 mb-6">
            Verification Timeline
          </h2>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <Upload className="h-4 w-4 text-blue-600" />
              </div>
              <div>
                <h3 className="font-medium text-slate-900">Submit Documents</h3>
                <p className="text-sm text-slate-600">Upload all required documents through your dashboard</p>
              </div>
              <div className="text-sm text-slate-500">Immediate</div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                <Clock className="h-4 w-4 text-yellow-600" />
              </div>
              <div>
                <h3 className="font-medium text-slate-900">Review Process</h3>
                <p className="text-sm text-slate-600">Our team reviews your documents for completeness and authenticity</p>
              </div>
              <div className="text-sm text-slate-500">1-3 business days</div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div>
                <h3 className="font-medium text-slate-900">Verification Complete</h3>
                <p className="text-sm text-slate-600">Receive confirmation and gain full access to platform features</p>
              </div>
              <div className="text-sm text-slate-500">Immediate</div>
            </div>
          </div>
        </Card>

        {/* Support */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold text-slate-900 mb-4">
            Need Help?
          </h2>
          <p className="text-slate-600 mb-6">
            Our verification team is here to help you through the process.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4">
            <Button variant="outline">
              <ExternalLink className="h-4 w-4 mr-2" />
              Contact Support
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Checklist
            </Button>
            <Link href="/pro/verification">
              <Button>
                Start Verification
              </Button>
            </Link>
          </div>
        </Card>
      </div>
    </div>
  )
}
