"use client"

import { useState, useRef } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useUser } from "@/contexts/user-context"
import {
  User,
  Briefcase,
  MapPin,
  FileText,
  CheckCircle,
  Upload,
  Star,
  Shield,
  Clock,
  ArrowRight,
  ArrowLeft,
  X,
  Image,
  File
} from "lucide-react"

export default function ProOnboardingPage() {
  const [step, setStep] = useState(1)
  const [contractorData, setContractorData] = useState({
    businessName: "",
    license: "",
    yearsExperience: "",
    specialties: [] as string[],
    serviceAreas: [] as string[],
    description: "",
    website: "",
    insurance: {
      provider: "",
      policyNumber: "",
      expiryDate: ""
    },
    portfolio: [] as File[],
    certifications: [] as File[]
  })
  const router = useRouter()
  const { user } = useUser()
  const portfolioInputRef = useRef<HTMLInputElement>(null)
  const certificationsInputRef = useRef<HTMLInputElement>(null)

  const totalSteps = 4

  const specialtyOptions = [
    "Kitchen Remodeling",
    "Bathroom Renovation", 
    "Flooring Installation",
    "Interior Painting",
    "Exterior Painting",
    "Electrical Work",
    "Plumbing",
    "HVAC",
    "Roofing",
    "Landscaping",
    "Carpentry",
    "Tile Work",
    "Drywall",
    "Windows & Doors",
    "General Contracting"
  ]

  const serviceAreaOptions = [
    "San Francisco",
    "Oakland",
    "San Jose",
    "Berkeley",
    "Fremont",
    "Hayward",
    "Sunnyvale",
    "Santa Clara",
    "Concord",
    "Richmond"
  ]

  const handleNext = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    } else {
      // Complete onboarding and redirect to verification
      router.push("/pro/verification")
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    }
  }

  const handleSpecialtyToggle = (specialty: string) => {
    setContractorData(prev => ({
      ...prev,
      specialties: prev.specialties.includes(specialty)
        ? prev.specialties.filter(s => s !== specialty)
        : [...prev.specialties, specialty]
    }))
  }

  const handleServiceAreaToggle = (area: string) => {
    setContractorData(prev => ({
      ...prev,
      serviceAreas: prev.serviceAreas.includes(area)
        ? prev.serviceAreas.filter(a => a !== area)
        : [...prev.serviceAreas, area]
    }))
  }

  const handlePortfolioUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    const imageFiles = files.filter(file => file.type.startsWith('image/'))

    if (imageFiles.length !== files.length) {
      alert('Please only upload image files for portfolio')
      return
    }

    setContractorData(prev => ({
      ...prev,
      portfolio: [...prev.portfolio, ...imageFiles]
    }))
  }

  const handleCertificationsUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setContractorData(prev => ({
      ...prev,
      certifications: [...prev.certifications, ...files]
    }))
  }

  const removePortfolioFile = (index: number) => {
    setContractorData(prev => ({
      ...prev,
      portfolio: prev.portfolio.filter((_, i) => i !== index)
    }))
  }

  const removeCertificationFile = (index: number) => {
    setContractorData(prev => ({
      ...prev,
      certifications: prev.certifications.filter((_, i) => i !== index)
    }))
  }

  const canProceed = () => {
    switch (step) {
      case 1:
        return contractorData.businessName && contractorData.license && contractorData.yearsExperience
      case 2:
        return contractorData.specialties.length > 0 && contractorData.serviceAreas.length > 0
      case 3:
        return contractorData.description && contractorData.insurance.provider
      case 4:
        return true // Portfolio is optional
      default:
        return false
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-emerald-50/30">
      <div className="container-premium page-padding">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Briefcase className="h-8 w-8 text-blue-600" />
            </div>
            <h1 className="text-3xl font-bold text-slate-900 mb-4">
              Welcome to RenovHub Pro
            </h1>
            <p className="text-slate-600 text-lg">
              Let's set up your professional profile to start connecting with customers
            </p>
          </div>

          {/* Progress */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <div className="text-sm text-slate-500">
                Step {step} of {totalSteps}
              </div>
              <div className="text-sm text-slate-500">
                {Math.round((step / totalSteps) * 100)}% Complete
              </div>
            </div>
            <div className="w-full bg-slate-200 rounded-full h-2">
              <div
                className="bg-gradient-to-r from-blue-600 to-emerald-600 h-2 rounded-full transition-all duration-500"
                style={{ width: `${(step / totalSteps) * 100}%` }}
              />
            </div>

            {/* Step Navigation */}
            <div className="flex justify-center mt-6">
              <div className="flex space-x-2">
                {Array.from({ length: totalSteps }, (_, i) => (
                  <button
                    key={i + 1}
                    onClick={() => setStep(i + 1)}
                    className={`w-8 h-8 rounded-full text-sm font-medium transition-all ${
                      i + 1 === step
                        ? 'bg-blue-600 text-white'
                        : i + 1 < step
                        ? 'bg-emerald-500 text-white hover:bg-emerald-600'
                        : 'bg-slate-200 text-slate-500 hover:bg-slate-300'
                    }`}
                  >
                    {i + 1 < step ? <CheckCircle className="h-4 w-4" /> : i + 1}
                  </button>
                ))}
              </div>
            </div>
          </div>

          <Card className="p-8">
            {/* Step 1: Business Information */}
            {step === 1 && (
              <div className="space-y-8">
                <div className="text-center">
                  <User className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-slate-900 mb-2">
                    Business Information
                  </h2>
                  <p className="text-slate-600">
                    Tell us about your contracting business
                  </p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="businessName">Business Name *</Label>
                    <Input
                      id="businessName"
                      value={contractorData.businessName}
                      onChange={(e) => setContractorData(prev => ({ ...prev, businessName: e.target.value }))}
                      placeholder="Your Business Name"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="license">License Number *</Label>
                    <Input
                      id="license"
                      value={contractorData.license}
                      onChange={(e) => setContractorData(prev => ({ ...prev, license: e.target.value }))}
                      placeholder="Contractor License #"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="experience">Years of Experience *</Label>
                    <Input
                      id="experience"
                      type="number"
                      value={contractorData.yearsExperience}
                      onChange={(e) => setContractorData(prev => ({ ...prev, yearsExperience: e.target.value }))}
                      placeholder="5"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="website">Website (Optional)</Label>
                    <Input
                      id="website"
                      value={contractorData.website}
                      onChange={(e) => setContractorData(prev => ({ ...prev, website: e.target.value }))}
                      placeholder="https://yourwebsite.com"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Services & Areas */}
            {step === 2 && (
              <div className="space-y-8">
                <div className="text-center">
                  <MapPin className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-slate-900 mb-2">
                    Services & Service Areas
                  </h2>
                  <p className="text-slate-600">
                    What services do you offer and where do you operate?
                  </p>
                </div>

                <div className="space-y-8">
                  <div>
                    <Label className="text-base font-medium mb-4 block">
                      Specialties * (Select all that apply)
                    </Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {specialtyOptions.map((specialty) => (
                        <button
                          key={specialty}
                          type="button"
                          onClick={() => handleSpecialtyToggle(specialty)}
                          className={`p-3 text-sm border rounded-lg transition-all ${
                            contractorData.specialties.includes(specialty)
                              ? 'border-blue-500 bg-blue-50 text-blue-700'
                              : 'border-slate-200 hover:border-slate-300'
                          }`}
                        >
                          {specialty}
                        </button>
                      ))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-base font-medium mb-4 block">
                      Service Areas * (Select all that apply)
                    </Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                      {serviceAreaOptions.map((area) => (
                        <button
                          key={area}
                          type="button"
                          onClick={() => handleServiceAreaToggle(area)}
                          className={`p-3 text-sm border rounded-lg transition-all ${
                            contractorData.serviceAreas.includes(area)
                              ? 'border-emerald-500 bg-emerald-50 text-emerald-700'
                              : 'border-slate-200 hover:border-slate-300'
                          }`}
                        >
                          {area}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Business Details */}
            {step === 3 && (
              <div className="space-y-8">
                <div className="text-center">
                  <Shield className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-slate-900 mb-2">
                    Business Details
                  </h2>
                  <p className="text-slate-600">
                    Help customers understand your business better
                  </p>
                </div>

                <div className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="description">Business Description *</Label>
                    <Textarea
                      id="description"
                      value={contractorData.description}
                      onChange={(e) => setContractorData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Describe your business, experience, and what sets you apart..."
                      rows={4}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="insuranceProvider">Insurance Provider *</Label>
                      <Input
                        id="insuranceProvider"
                        value={contractorData.insurance.provider}
                        onChange={(e) => setContractorData(prev => ({ 
                          ...prev, 
                          insurance: { ...prev.insurance, provider: e.target.value }
                        }))}
                        placeholder="Insurance Company"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="policyNumber">Policy Number</Label>
                      <Input
                        id="policyNumber"
                        value={contractorData.insurance.policyNumber}
                        onChange={(e) => setContractorData(prev => ({ 
                          ...prev, 
                          insurance: { ...prev.insurance, policyNumber: e.target.value }
                        }))}
                        placeholder="Policy #"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="expiryDate">Expiry Date</Label>
                      <Input
                        id="expiryDate"
                        type="date"
                        value={contractorData.insurance.expiryDate}
                        onChange={(e) => setContractorData(prev => ({ 
                          ...prev, 
                          insurance: { ...prev.insurance, expiryDate: e.target.value }
                        }))}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 4: Portfolio & Verification */}
            {step === 4 && (
              <div className="space-y-8">
                <div className="text-center">
                  <Star className="h-12 w-12 text-blue-600 mx-auto mb-4" />
                  <h2 className="text-2xl font-bold text-slate-900 mb-2">
                    Portfolio & Documents
                  </h2>
                  <p className="text-slate-600">
                    Showcase your work and upload verification documents (Optional)
                  </p>
                </div>

                <div className="space-y-6">
                  {/* Portfolio Upload */}
                  <div>
                    <Label className="text-base font-medium mb-4 block">
                      Portfolio Images (Optional)
                    </Label>
                    <div
                      className="border-2 border-dashed border-slate-300 rounded-lg p-8 text-center hover:border-slate-400 transition-colors cursor-pointer"
                      onClick={() => portfolioInputRef.current?.click()}
                    >
                      <Image className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-slate-900 mb-2">
                        Upload Portfolio Images
                      </h3>
                      <p className="text-slate-600 mb-4">
                        Show potential customers your best work (JPG, PNG, WebP)
                      </p>
                      <Button variant="outline" type="button">
                        <Upload className="h-4 w-4 mr-2" />
                        Choose Images
                      </Button>
                    </div>

                    <input
                      ref={portfolioInputRef}
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handlePortfolioUpload}
                      className="hidden"
                    />

                    {/* Portfolio Preview */}
                    {contractorData.portfolio.length > 0 && (
                      <div className="mt-4 grid grid-cols-2 md:grid-cols-3 gap-4">
                        {contractorData.portfolio.map((file, index) => (
                          <div key={index} className="relative group">
                            <div className="aspect-square bg-slate-100 rounded-lg flex items-center justify-center">
                              <Image className="h-8 w-8 text-slate-400" />
                            </div>
                            <button
                              onClick={() => removePortfolioFile(index)}
                              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity"
                            >
                              <X className="h-3 w-3" />
                            </button>
                            <p className="text-xs text-slate-600 mt-1 truncate">{file.name}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Certifications Upload */}
                  <div>
                    <Label className="text-base font-medium mb-4 block">
                      Certifications & Documents (Optional)
                    </Label>
                    <div
                      className="border-2 border-dashed border-slate-300 rounded-lg p-8 text-center hover:border-slate-400 transition-colors cursor-pointer"
                      onClick={() => certificationsInputRef.current?.click()}
                    >
                      <FileText className="h-12 w-12 text-slate-400 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-slate-900 mb-2">
                        Upload Certifications
                      </h3>
                      <p className="text-slate-600 mb-4">
                        License, insurance, and other certifications (PDF, JPG, PNG)
                      </p>
                      <Button variant="outline" type="button">
                        <Upload className="h-4 w-4 mr-2" />
                        Choose Files
                      </Button>
                    </div>

                    <input
                      ref={certificationsInputRef}
                      type="file"
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png,.webp"
                      onChange={handleCertificationsUpload}
                      className="hidden"
                    />

                    {/* Certifications Preview */}
                    {contractorData.certifications.length > 0 && (
                      <div className="mt-4 space-y-2">
                        {contractorData.certifications.map((file, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <File className="h-5 w-5 text-slate-400" />
                              <span className="text-sm text-slate-700">{file.name}</span>
                              <span className="text-xs text-slate-500">
                                ({(file.size / 1024 / 1024).toFixed(1)} MB)
                              </span>
                            </div>
                            <button
                              onClick={() => removeCertificationFile(index)}
                              className="text-red-500 hover:text-red-700 transition-colors"
                            >
                              <X className="h-4 w-4" />
                            </button>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Navigation */}
            <div className="flex items-center justify-between mt-12 pt-8 border-t border-slate-200">
              <Button
                variant="outline"
                onClick={handleBack}
                disabled={step === 1}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </Button>

              <div className="text-sm text-slate-500">
                {step} of {totalSteps}
              </div>

              <Button
                onClick={handleNext}
                disabled={!canProceed()}
              >
                {step === totalSteps ? 'Complete Setup' : 'Next'}
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
}
