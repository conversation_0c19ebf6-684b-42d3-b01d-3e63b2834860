"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { ArrowRight, Home, MapPin, User, CheckCircle } from "lucide-react"
import { useRouter } from "next/navigation"

export default function OnboardingPage() {
  const [step, setStep] = useState(1)
  const [userData, setUserData] = useState({
    name: "",
    email: "",
    location: "",
    propertyType: "",
    interests: [] as string[],
    goals: "",
  })
  const router = useRouter()

  const totalSteps = 4

  const propertyTypes = [
    { id: "house", label: "Single Family Home", icon: "🏠" },
    { id: "condo", label: "Condo/Apartment", icon: "🏢" },
    { id: "townhouse", label: "Townhouse", icon: "🏘️" },
    { id: "commercial", label: "Commercial Property", icon: "🏪" },
  ]

  const interestOptions = [
    "Kitchen Remodeling",
    "Bathroom Renovation",
    "Flooring",
    "Painting",
    "Landscaping",
    "Electrical Work",
    "Plumbing",
    "HVAC",
  ]

  const handleNext = () => {
    if (step < totalSteps) {
      setStep(step + 1)
    } else {
      // Complete onboarding
      router.push("/dashboard")
    }
  }

  const handleInterestToggle = (interest: string) => {
    setUserData((prev) => ({
      ...prev,
      interests: prev.interests.includes(interest)
        ? prev.interests.filter((i) => i !== interest)
        : [...prev.interests, interest],
    }))
  }

  const canProceed = () => {
    switch (step) {
      case 1:
        return userData.name && userData.email
      case 2:
        return userData.location && userData.propertyType
      case 3:
        return userData.interests.length > 0
      case 4:
        return userData.goals
      default:
        return false
    }
  }

  return (
    <div className="min-h-screen bg-white flex items-center justify-center px-6">
      <div className="w-full max-w-2xl">
        {/* Progress */}
        <div className="mb-12">
          <div className="flex items-center justify-between mb-6">
            <div className="text-sm text-slate-400">
              Step {step} of {totalSteps}
            </div>
            <div className="text-sm text-slate-400">{Math.round((step / totalSteps) * 100)}% Complete</div>
          </div>
          <div className="w-full bg-slate-100 rounded-full h-1">
            <div
              className="bg-slate-900 h-1 rounded-full transition-all duration-500"
              style={{ width: `${(step / totalSteps) * 100}%` }}
            />
          </div>
        </div>

        {/* Step Content */}
        <div className="space-y-8">
          {/* Step 1: Basic Info */}
          {step === 1 && (
            <div className="text-center animate-in">
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <User className="h-8 w-8 text-slate-600" />
              </div>
              <h1 className="text-3xl font-light text-slate-900 mb-4">Welcome to RenovHub</h1>
              <p className="text-slate-500 mb-8">Let's get to know you better so we can provide the best experience</p>

              <div className="space-y-4 max-w-md mx-auto">
                <Input
                  placeholder="Your full name"
                  value={userData.name}
                  onChange={(e) => setUserData((prev) => ({ ...prev, name: e.target.value }))}
                  className="text-center border-slate-200 focus:border-slate-300"
                />
                <Input
                  type="email"
                  placeholder="Your email address"
                  value={userData.email}
                  onChange={(e) => setUserData((prev) => ({ ...prev, email: e.target.value }))}
                  className="text-center border-slate-200 focus:border-slate-300"
                />
              </div>
            </div>
          )}

          {/* Step 2: Property Info */}
          {step === 2 && (
            <div className="text-center animate-in">
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Home className="h-8 w-8 text-slate-600" />
              </div>
              <h2 className="text-3xl font-light text-slate-900 mb-4">Tell us about your property</h2>
              <p className="text-slate-500 mb-8">This helps us match you with the right contractors</p>

              <div className="space-y-6 max-w-md mx-auto">
                <div className="relative">
                  <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                  <Input
                    placeholder="Your location (city, state)"
                    value={userData.location}
                    onChange={(e) => setUserData((prev) => ({ ...prev, location: e.target.value }))}
                    className="pl-10 border-slate-200 focus:border-slate-300"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  {propertyTypes.map((type) => (
                    <button
                      key={type.id}
                      onClick={() => setUserData((prev) => ({ ...prev, propertyType: type.id }))}
                      className={`p-4 rounded-xl border transition-all duration-200 ${
                        userData.propertyType === type.id
                          ? "border-slate-900 bg-slate-50"
                          : "border-slate-200 hover:border-slate-300"
                      }`}
                    >
                      <div className="text-2xl mb-2">{type.icon}</div>
                      <div className="text-sm font-medium text-slate-900">{type.label}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Step 3: Interests */}
          {step === 3 && (
            <div className="text-center animate-in">
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="h-8 w-8 text-slate-600" />
              </div>
              <h2 className="text-3xl font-light text-slate-900 mb-4">What are you interested in?</h2>
              <p className="text-slate-500 mb-8">Select all renovation types that interest you</p>

              <div className="grid grid-cols-2 md:grid-cols-3 gap-3 max-w-2xl mx-auto">
                {interestOptions.map((interest) => (
                  <button
                    key={interest}
                    onClick={() => handleInterestToggle(interest)}
                    className={`p-4 rounded-xl border transition-all duration-200 ${
                      userData.interests.includes(interest)
                        ? "border-slate-900 bg-slate-50"
                        : "border-slate-200 hover:border-slate-300"
                    }`}
                  >
                    <div className="text-sm font-medium text-slate-900">{interest}</div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Step 4: Goals */}
          {step === 4 && (
            <div className="text-center animate-in">
              <div className="w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Home className="h-8 w-8 text-slate-600" />
              </div>
              <h2 className="text-3xl font-light text-slate-900 mb-4">What are your renovation goals?</h2>
              <p className="text-slate-500 mb-8">Help us understand what you're hoping to achieve</p>

              <div className="max-w-md mx-auto">
                <Textarea
                  placeholder="Tell us about your renovation goals, timeline, or any specific requirements..."
                  value={userData.goals}
                  onChange={(e) => setUserData((prev) => ({ ...prev, goals: e.target.value }))}
                  className="border-slate-200 focus:border-slate-300 min-h-[120px]"
                />
              </div>
            </div>
          )}
        </div>

        {/* Action Button */}
        <div className="flex justify-center mt-12">
          <Button
            onClick={handleNext}
            disabled={!canProceed()}
            className="bg-slate-900 hover:bg-slate-800 text-white px-8 py-3 rounded-lg font-medium disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {step === totalSteps ? "Complete Setup" : "Continue"}
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  )
}
