import type { Metadata, Viewport } from 'next'
import './globals.css'
import { UserProvider } from '@/contexts/user-context'
import { PWAInstallPrompt, OfflineIndicator } from '@/components/pwa-install-prompt'
import { PageErrorBoundary } from '@/components/ui/error-boundary'
import { ToastProvider } from '@/components/ui/toast-system'
import { MinimalFooter } from '@/components/footer'

export const metadata: Metadata = {
  title: 'RenovHub - Home Renovation Marketplace',
  description: 'Connect with verified contractors for your home renovation projects. Get quotes, manage projects, and transform your home with trusted professionals.',
  keywords: ['home renovation', 'contractors', 'home improvement', 'construction', 'remodeling'],
  authors: [{ name: 'RenovHub Team' }],
  creator: 'RenovH<PERSON>',
  publisher: 'RenovHub',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://renovhub.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'RenovHub - Home Renovation Marketplace',
    description: 'Connect with verified contractors for your home renovation projects',
    url: 'https://renovhub.com',
    siteName: 'RenovHub',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'RenovHub - Home Renovation Marketplace',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'RenovHub - Home Renovation Marketplace',
    description: 'Connect with verified contractors for your home renovation projects',
    images: ['/twitter-image.png'],
    creator: '@renovhub',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  manifest: '/manifest.json',
  icons: {
    icon: [
      { url: '/favicon.svg', type: 'image/svg+xml' },
      { url: '/favicon.ico', sizes: 'any' },
      { url: '/icons/icon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/icons/icon-192x192.png', sizes: '192x192', type: 'image/png' },
      { url: '/icons/icon.svg', type: 'image/svg+xml' },
    ],
    apple: [
      { url: '/icons/apple-touch-icon.png', sizes: '180x180', type: 'image/png' },
    ],
    shortcut: '/favicon.svg',
  },
  appleWebApp: {
    capable: true,
    statusBarStyle: 'default',
    title: 'RenovHub',
  },
}

export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#018445' },
    { media: '(prefers-color-scheme: dark)', color: '#018445' },
  ],
  width: 'device-width',
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700;800;900&family=Roboto+Condensed:wght@400;700&display=swap" rel="stylesheet" />
        <link rel="manifest" href="/manifest.json" />
        <link rel="icon" href="/favicon.ico" sizes="any" />
        <link rel="icon" href="/icons/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/icons/apple-touch-icon.png" />
        <meta name="theme-color" content="#ff5a5f" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="RenovHub" />
      </head>
      <body>
        <PageErrorBoundary>
          <ToastProvider>
            <UserProvider>


              <OfflineIndicator />
              <div className="flex flex-col min-h-screen">
                <div className="flex-1">
                  <main id="main-content" className="focus:outline-none" tabIndex={-1}>
                    {children}
                  </main>
                </div>
                <MinimalFooter />
              </div>
              <PWAInstallPrompt />
            </UserProvider>
          </ToastProvider>
        </PageErrorBoundary>
      </body>
    </html>
  )
}
