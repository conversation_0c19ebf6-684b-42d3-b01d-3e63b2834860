"use client"

import { useState, useEffect } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { ImageUpload } from "@/components/ui/image-upload"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { useProjects } from "@/hooks/use-projects"
import { useToast } from "@/components/ui/toast-system"
import { ArrowLeft, ArrowRight, Camera } from "lucide-react"

interface ProjectData {
  description: string
  category: string
  budget: string
  timeline: string
  location: string
  photos: File[]
}

export default function CreateProjectPage() {
  const { user } = useUser()
  const searchParams = useSearchParams()
  const router = useRouter()
  const { createProject } = useProjects({ autoFetch: false })
  const { success, error } = useToast()
  const [step, setStep] = useState(1)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [projectData, setProjectData] = useState<ProjectData>({
    description: searchParams?.get("description") || "",
    category: "",
    budget: "",
    timeline: "",
    location: searchParams?.get("location") || "",
    photos: [],
  })

  const totalSteps = 5

  const handleNext = async () => {
    if (step < totalSteps) {
      setStep(step + 1)
    } else {
      // Final step - create the project
      if (!user) {
        // Store project data in localStorage for after login
        localStorage.setItem('pendingProject', JSON.stringify(projectData))
        router.push("/login?redirect=/project/create")
        return
      }

      setIsSubmitting(true)
      try {
        // Map category to database enum
        const categoryMap: Record<string, string> = {
          'Kitchen Remodel': 'kitchen',
          'Bathroom Renovation': 'bathroom',
          'Flooring Installation': 'flooring',
          'Interior/Exterior Painting': 'painting',
          'Roofing': 'roofing',
          'Other': 'general'
        }

        const dbProjectData = {
          title: projectData.description.split('.')[0] || 'New Project',
          description: projectData.description,
          category: categoryMap[projectData.category] || 'general',
          budget: {
            range: projectData.budget,
            currency: 'USD'
          },
          timeline: {
            preferred: projectData.timeline,
            flexible: true
          },
          location: {
            address: projectData.location,
            coordinates: null
          },
          photos: projectData.photos.map(file => ({
            name: file.name,
            size: file.size,
            type: file.type
          })),
          requirements: [],
          tags: [projectData.category.toLowerCase()],
          status: 'active' as const
        }

        const newProject = await createProject(dbProjectData)

        if (newProject) {
          success('Project created successfully!')
          router.push(`/project/${newProject.id}`)
        } else {
          error('Failed to create project. Please try again.')
        }
      } catch (err) {
        console.error('Error creating project:', err)
        error('An unexpected error occurred. Please try again.')
      } finally {
        setIsSubmitting(false)
      }
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    } else {
      router.push("/")
    }
  }

  const budgetOptions = ["$1,000 - $5,000", "$5,000 - $15,000", "$15,000 - $30,000", "$30,000 - $50,000", "$50,000+"]
  const timelineOptions = ["Within 2 weeks", "Within 1 month", "1-3 months", "3-6 months", "Flexible"]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <UnifiedNavigation />
      
      <div className="container-native section-native">
        <div className="mb-8 sm:mb-12">
          <div className="text-center mb-6 sm:mb-8">
            <h1 className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-900 mb-3 sm:mb-4">Post Your Project</h1>
            <p className="text-sm sm:text-base lg:text-lg text-slate-600 max-w-2xl mx-auto leading-relaxed">
              Let's gather some details about your renovation project to connect you with the perfect contractors
            </p>
          </div>

          <div className="max-w-3xl mx-auto">
            <div className="flex items-center justify-between mb-3 sm:mb-4">
              <span className="text-xs sm:text-sm font-semibold text-slate-600">Step {step} of {totalSteps}</span>
              <span className="text-xs sm:text-sm font-semibold text-slate-600">{Math.round(((step - 1) / totalSteps) * 100)}% Complete</span>
            </div>

            <div className="w-full bg-slate-200 rounded-full h-1.5 sm:h-2 mb-6 sm:mb-8 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-blue-600 to-emerald-600 rounded-full transition-all duration-500 ease-out"
                style={{ width: `${((step - 1) / totalSteps) * 100}%` }}
              />
            </div>

            <div className="flex justify-between">
              {Array.from({ length: totalSteps }, (_, i) => i + 1).map((stepNumber) => (
                <div key={stepNumber} className="flex flex-col items-center">
                  <div className={`w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center text-xs sm:text-sm font-semibold transition-all duration-300 ${
                    stepNumber <= step
                      ? 'bg-gradient-to-r from-blue-600 to-emerald-600 text-white shadow-md'
                      : 'bg-slate-200 text-slate-500'
                  }`}>
                    {stepNumber <= step ? '✓' : stepNumber}
                  </div>
                  <span className={`text-[10px] sm:text-xs mt-1 sm:mt-2 font-medium transition-colors duration-300 text-center leading-tight ${
                    stepNumber <= step ? 'text-slate-900' : 'text-slate-500'
                  }`}>
                    {stepNumber === 1 && 'Details'}
                    {stepNumber === 2 && 'Budget'}
                    {stepNumber === 3 && 'Timeline'}
                    {stepNumber === 4 && 'Images'}
                    {stepNumber === 5 && 'Review'}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="max-w-2xl mx-auto">
          <div className="bg-white rounded-xl sm:rounded-2xl p-6 sm:p-8 shadow-sm sm:shadow-lg border border-slate-100">
            {/* Step Content */}
            {step === 1 && (
              <div className="space-y-6 sm:space-y-8">
                <div className="text-center mb-6 sm:mb-8">
                  <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-900 mb-2">Project Details</h2>
                  <p className="text-sm sm:text-base text-slate-600">Tell us about your renovation project</p>
                </div>

                {/* Project Description */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Project Description *
                  </label>
                  <Textarea
                    value={projectData.description}
                    onChange={(e) => setProjectData(prev => ({ ...prev, description: e.target.value }))}
                    placeholder="Describe your renovation project in detail..."
                    className="min-h-[100px] sm:min-h-[120px] text-sm sm:text-base"
                  />
                </div>

                {/* Project Category */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3 sm:mb-4">
                    Project Category *
                  </label>
                  <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                    {[
                      { id: 'kitchen', name: 'Kitchen Remodel', icon: '🏠' },
                      { id: 'bathroom', name: 'Bathroom Renovation', icon: '🛁' },
                      { id: 'flooring', name: 'Flooring Installation', icon: '🪵' },
                      { id: 'painting', name: 'Interior/Exterior Painting', icon: '🎨' },
                      { id: 'roofing', name: 'Roofing', icon: '🏠' },
                      { id: 'other', name: 'Other', icon: '🔧' }
                    ].map((category) => (
                      <button
                        key={category.id}
                        onClick={() => setProjectData(prev => ({ ...prev, category: category.name }))}
                        className={`p-3 sm:p-4 border-2 rounded-xl text-center sm:text-left transition-all duration-200 min-h-[80px] sm:min-h-[100px] flex flex-col justify-center items-center sm:items-start ${
                          projectData.category === category.name
                            ? 'border-blue-500 bg-blue-50'
                            : 'border-slate-200 hover:border-slate-300'
                        }`}
                      >
                        <div className="text-xl sm:text-2xl mb-1 sm:mb-2">{category.icon}</div>
                        <div className="font-medium text-slate-900 text-xs sm:text-sm leading-tight">{category.name}</div>
                      </button>
                    ))}
                  </div>
                </div>

                {/* Project Location */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    Project Location
                  </label>
                  <input
                    type="text"
                    value={projectData.location}
                    onChange={(e) => setProjectData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="Enter your city, state"
                    className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 text-sm sm:text-base transition-all duration-200"
                  />
                </div>
              </div>
            )}

            {step === 2 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-900 mb-2">Budget Range</h2>
                  <p className="text-sm sm:text-base text-slate-600">What's your estimated budget for this project?</p>
                </div>

                <div className="space-y-3">
                  {budgetOptions.map((option) => (
                    <button
                      key={option}
                      onClick={() => setProjectData(prev => ({ ...prev, budget: option }))}
                      className={`w-full p-3 sm:p-4 border-2 rounded-xl text-left transition-all duration-200 ${
                        projectData.budget === option
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-slate-200 hover:border-slate-300'
                      }`}
                    >
                      <div className="font-semibold text-slate-900 text-sm sm:text-base">{option}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {step === 3 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-900 mb-2">Timeline</h2>
                  <p className="text-sm sm:text-base text-slate-600">When would you like to start this project?</p>
                </div>

                <div className="space-y-3">
                  {timelineOptions.map((option) => (
                    <button
                      key={option}
                      onClick={() => setProjectData(prev => ({ ...prev, timeline: option }))}
                      className={`w-full p-3 sm:p-4 border-2 rounded-xl text-left transition-all duration-200 ${
                        projectData.timeline === option
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-slate-200 hover:border-slate-300'
                      }`}
                    >
                      <div className="font-semibold text-slate-900 text-sm sm:text-base">{option}</div>
                    </button>
                  ))}
                </div>
              </div>
            )}

            {step === 4 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-lg sm:text-xl lg:text-2xl font-bold text-slate-900 mb-2">Project Images</h2>
                  <p className="text-sm sm:text-base text-slate-600">Upload images to help contractors understand your project better (optional)</p>
                </div>

                <ImageUpload
                  images={projectData.photos}
                  onImagesChange={(images) => setProjectData(prev => ({ ...prev, photos: images }))}
                  maxImages={8}
                  maxSizePerImage={5}
                />

                <div className="bg-blue-50 border border-blue-200 rounded-xl p-3 sm:p-4">
                  <h3 className="font-semibold text-blue-900 mb-2 text-sm sm:text-base">💡 Tips for better project images:</h3>
                  <ul className="text-xs sm:text-sm text-blue-800 space-y-1">
                    <li>• Include current state of the area to be renovated</li>
                    <li>• Show any specific issues or challenges</li>
                    <li>• Add inspiration photos if you have them</li>
                    <li>• Include measurements or floor plans if available</li>
                  </ul>
                </div>
              </div>
            )}

            {step === 5 && (
              <div className="space-y-6">
                <div className="text-center mb-6">
                  <h2 className="text-base sm:text-lg font-bold text-slate-900 mb-2">Review Your Project</h2>
                  <p className="text-sm sm:text-base text-slate-600">Please review your project details before submitting</p>
                </div>

                <div className="space-y-3 sm:space-y-4">
                  <div className="p-3 sm:p-4 bg-slate-50 rounded-xl">
                    <h3 className="font-semibold text-slate-900 mb-2 text-sm sm:text-base">Project Description</h3>
                    <p className="text-slate-600 text-sm sm:text-base leading-relaxed">{projectData.description}</p>
                  </div>

                  {projectData.category && (
                    <div className="p-3 sm:p-4 bg-slate-50 rounded-xl">
                      <h3 className="font-semibold text-slate-900 mb-2 text-sm sm:text-base">Category</h3>
                      <p className="text-slate-600 text-sm sm:text-base">{projectData.category}</p>
                    </div>
                  )}

                  {projectData.budget && (
                    <div className="p-3 sm:p-4 bg-slate-50 rounded-xl">
                      <h3 className="font-semibold text-slate-900 mb-2 text-sm sm:text-base">Budget</h3>
                      <p className="text-slate-600 text-sm sm:text-base">{projectData.budget}</p>
                    </div>
                  )}

                  {projectData.timeline && (
                    <div className="p-3 sm:p-4 bg-slate-50 rounded-xl">
                      <h3 className="font-semibold text-slate-900 mb-2 text-sm sm:text-base">Timeline</h3>
                      <p className="text-slate-600 text-sm sm:text-base">{projectData.timeline}</p>
                    </div>
                  )}

                  {projectData.location && (
                    <div className="p-4 bg-slate-50 rounded-xl">
                      <h3 className="font-medium text-slate-900 mb-2">Location</h3>
                      <p className="text-slate-600">{projectData.location}</p>
                    </div>
                  )}

                  {projectData.photos.length > 0 && (
                    <div className="p-4 bg-slate-50 rounded-xl">
                      <h3 className="font-medium text-slate-900 mb-4">Project Images ({projectData.photos.length})</h3>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3">
                        {projectData.photos.map((photo, index) => (
                          <ProjectImageThumbnail key={index} file={photo} index={index} />
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Enhanced Navigation Buttons */}
            <div className="flex justify-between pt-6 sm:pt-8 border-t border-slate-100 mt-6 sm:mt-8">
              <Button
                variant="outline"
                onClick={handleBack}
                className="flex items-center space-x-2 px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Back</span>
              </Button>

              <Button
                onClick={handleNext}
                disabled={
                  isSubmitting ||
                  (step === 1 && (!projectData.description.trim() || !projectData.category)) ||
                  (step === 2 && !projectData.budget) ||
                  (step === 3 && !projectData.timeline)
                  // Step 4 (images) is optional, no validation needed
                }
                className="flex items-center space-x-2 px-4 py-2 sm:px-6 sm:py-3 text-sm sm:text-base bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
              >
                <span>
                  {isSubmitting ? 'Creating Project...' :
                   step === 5 ? 'Submit Project' : 'Continue'}
                </span>
                {!isSubmitting && <ArrowRight className="h-4 w-4" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      <RoleSwitcher />
    </div>
  )
}

// Component for displaying image thumbnails in the review step
function ProjectImageThumbnail({ file, index }: { file: File; index: number }) {
  const [preview, setPreview] = useState<string | null>(null)

  useEffect(() => {
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
    }
    reader.readAsDataURL(file)
  })

  return (
    <div className="relative aspect-square bg-slate-100 rounded-lg overflow-hidden">
      {preview ? (
        <img
          src={preview}
          alt={`Project image ${index + 1}`}
          className="w-full h-full object-cover"
        />
      ) : (
        <div className="w-full h-full flex items-center justify-center">
          <Camera className="h-6 w-6 text-slate-400" />
        </div>
      )}
      <div className="absolute bottom-1 left-1 bg-black/70 text-white text-xs px-1.5 py-0.5 rounded">
        {index + 1}
      </div>
    </div>
  )
}
