"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { CustomerRoute } from "@/components/route-guard"

import { Star, MessageCircle, Check, Phone, Mail, ArrowLeft, ArrowRight, X, CheckCircle, Plus } from "lucide-react"
import { RatingDisplay } from "@/components/ui/interactive-elements"

interface Bid {
  id: string
  contractorName: string
  rating: number
  reviewCount: number
  price: string
  timeline: string
  description: string
  verified: boolean
  responseTime: string
  phone?: string
  email?: string
}

export default function CompareContractorsPage() {
  const router = useRouter()
  const [selectedBids, setSelectedBids] = useState<Bid[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedForContract, setSelectedForContract] = useState<string[]>([])
  const [showSelectionMode, setShowSelectionMode] = useState(false)

  useEffect(() => {
    // Get selected bids from localStorage
    const storedBids = localStorage.getItem('selectedBidsData')
    if (storedBids) {
      setSelectedBids(JSON.parse(storedBids))
    } else {
      // If no selected bids, redirect back to bids page
      router.push('/project/bids')
      return
    }
    setLoading(false)
  }, [router])

  const handleRemoveContractor = (bidId: string) => {
    const updatedBids = selectedBids.filter(bid => bid.id !== bidId)
    setSelectedBids(updatedBids)

    // Update localStorage
    localStorage.setItem('selectedBidsData', JSON.stringify(updatedBids))

    // If no contractors left, redirect back to bids
    if (updatedBids.length === 0) {
      localStorage.removeItem('selectedBids')
      localStorage.removeItem('selectedBidsData')
      router.push('/project/bids')
    }
  }

  const handleMessage = (bid: Bid) => {
    // Create or navigate to conversation with contractor
    // For now, we'll simulate opening a message interface
    alert(`Opening message interface with ${bid.contractorName}`)
    // In a real app, this would navigate to a messaging page or open a modal
    // router.push(`/messages/new?contractor=${bid.id}`)
  }

  const handleCall = (bid: Bid) => {
    // In a real app, this might open the phone app or show contact details
    const phoneNumber = bid.phone
    if (phoneNumber && confirm(`Call ${bid.contractorName} at ${phoneNumber}?`)) {
      window.open(`tel:${phoneNumber}`, '_self')
    } else {
      alert('Phone number not available for this contractor.')
    }
  }

  const handleToggleContractorSelection = (bidId: string) => {
    setSelectedForContract(prev =>
      prev.includes(bidId)
        ? prev.filter(id => id !== bidId)
        : [...prev, bidId]
    )
  }

  const handleSelectAllForContract = () => {
    setSelectedForContract(selectedBids.map(bid => bid.id))
  }

  const handleDeselectAllForContract = () => {
    setSelectedForContract([])
  }

  const handleProceedToContract = () => {
    if (selectedForContract.length === 0) {
      alert('Please select at least one contractor to proceed with the contract.')
      return
    }
    // Store selected contractors for contract
    const contractorData = selectedBids.filter(bid => selectedForContract.includes(bid.id))
    localStorage.setItem('contractContractors', JSON.stringify(contractorData))
    // Navigate to contract/agreement page
    router.push('/project/contract')
  }

  const handleGoBack = () => {
    router.push('/project/bids')
  }

  if (loading) {
    return (
      <CustomerRoute>
        <div className="min-h-screen bg-white">
          <UnifiedNavigation />
          <div className="container-premium section-premium">
            <div className="text-center py-20">
              <div className="animate-pulse text-slate-500 text-lg">Loading comparison...</div>
            </div>
          </div>
        </div>
      </CustomerRoute>
    )
  }

  if (selectedBids.length === 0) {
    return (
      <CustomerRoute>
        <div className="min-h-screen bg-white">
          <UnifiedNavigation />
          <div className="container-premium section-premium">
            <div className="text-center py-20">
              <h1 className="text-2xl font-semibold text-slate-900 mb-4">No Contractors Selected</h1>
              <p className="text-slate-600 mb-8">Please go back and select contractors to compare.</p>
              <Button onClick={handleGoBack} variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Bids
              </Button>
            </div>
          </div>
        </div>
      </CustomerRoute>
    )
  }

  return (
    <CustomerRoute>
      <div className="min-h-screen bg-white">
        <UnifiedNavigation />

        <div className="container-premium section-premium">
          {/* Header */}
          <div className="mb-12">
            <Button onClick={handleGoBack} variant="ghost" className="mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Bids
            </Button>
            <h1 className="text-3xl font-semibold text-slate-900 mb-3">Compare Selected Contractors</h1>
            <p className="text-lg text-slate-600">
              Review and compare your selected contractors before proceeding
            </p>
          </div>

          {/* Selection Controls */}
          <div className="mb-8 p-6 bg-slate-50 rounded-xl border border-slate-200">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-slate-900">Select Contractors for Contract</h2>
              <div className="flex items-center space-x-3">
                <Button
                  onClick={handleSelectAllForContract}
                  variant="outline"
                  size="sm"
                  className="text-sm"
                >
                  Select All
                </Button>
                <Button
                  onClick={handleDeselectAllForContract}
                  variant="outline"
                  size="sm"
                  className="text-sm"
                >
                  Deselect All
                </Button>
              </div>
            </div>
            <p className="text-slate-600 text-sm">
              Choose which contractors you'd like to proceed with for the contract phase.
              You can select multiple contractors to compare their final offers.
            </p>
            {selectedForContract.length > 0 && (
              <div className="mt-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <p className="text-blue-800 text-sm font-medium">
                  {selectedForContract.length} contractor{selectedForContract.length > 1 ? 's' : ''} selected for contract
                </p>
              </div>
            )}
          </div>

          {/* Mobile-Optimized Comparison Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 mb-8 sm:mb-12">
          {selectedBids.map((bid) => (
            <div key={bid.id} className={`card-premium p-4 sm:p-6 relative transition-all duration-200 ${
              selectedForContract.includes(bid.id)
                ? 'ring-2 ring-blue-500 bg-blue-50/50'
                : 'hover:shadow-lg'
            }`}>
              {/* Selection Checkbox */}
              <div className="absolute top-4 left-4 z-10">
                <button
                  onClick={() => handleToggleContractorSelection(bid.id)}
                  className={`w-6 h-6 rounded-md border-2 flex items-center justify-center transition-all duration-200 ${
                    selectedForContract.includes(bid.id)
                      ? 'bg-blue-500 border-blue-500 text-white'
                      : 'border-slate-300 hover:border-blue-400 bg-white'
                  }`}
                  title={selectedForContract.includes(bid.id) ? 'Deselect for contract' : 'Select for contract'}
                >
                  {selectedForContract.includes(bid.id) && (
                    <Check className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Remove Button */}
              <button
                onClick={() => handleRemoveContractor(bid.id)}
                className="absolute top-4 right-4 w-8 h-8 bg-red-50 hover:bg-red-100 rounded-full flex items-center justify-center transition-colors group"
                title="Remove from comparison"
              >
                <X className="h-4 w-4 text-red-500 group-hover:text-red-600" />
              </button>

              <div className="text-center mb-6">
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <h3 className="text-xl font-semibold text-slate-900 pr-8">{bid.contractorName}</h3>
                  {bid.verified && (
                    <div className="w-5 h-5 bg-emerald-500 rounded-full flex items-center justify-center">
                      <Check className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>
                <div className="flex justify-center mb-2">
                  <RatingDisplay
                    rating={bid.rating}
                    reviewCount={bid.reviewCount}
                    size="sm"
                  />
                </div>
              </div>

              <div className="space-y-4 mb-6">
                <div className="text-center">
                  <div className="text-3xl font-bold text-slate-900 mb-1">{bid.price}</div>
                  <div className="text-sm text-slate-600">{bid.timeline}</div>
                </div>
                
                <div className="text-sm text-slate-600 text-center">
                  {bid.description}
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full hover:bg-blue-50 hover:border-blue-200"
                  onClick={() => handleMessage(bid)}
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  Message
                </Button>
                <Button
                  variant="outline"
                  className="w-full hover:bg-green-50 hover:border-green-200"
                  onClick={() => handleCall(bid)}
                >
                  <Phone className="h-4 w-4 mr-2" />
                  Call {bid.phone || 'Contact'}
                </Button>

                {/* Additional Info */}
                <div className="pt-2 text-xs text-slate-500 space-y-1">
                  <div className="flex items-center justify-between">
                    <span>Response time:</span>
                    <span className="font-medium">{bid.responseTime}</span>
                  </div>
                  {bid.email && (
                    <div className="flex items-center justify-between">
                      <span>Email:</span>
                      <a
                        href={`mailto:${bid.email}`}
                        className="text-blue-600 hover:text-blue-700 font-medium"
                      >
                        <Mail className="h-3 w-3 inline mr-1" />
                        Contact
                      </a>
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Action Buttons */}
        <div className="text-center">
          <div className="bg-slate-50 rounded-xl p-8">
            <h2 className="text-xl font-semibold text-slate-900 mb-4">
              Comparing {selectedBids.length} Contractor{selectedBids.length !== 1 ? 's' : ''}
            </h2>
            <p className="text-slate-600 mb-6">
              You can contact these contractors directly, remove any from your comparison, or select contractors above and proceed to create contracts with your preferred choices.
            </p>

            {/* Quick Actions */}
            <div className="flex flex-wrap gap-3 justify-center mb-6">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  selectedBids.forEach(bid => handleMessage(bid))
                }}
                className="hover:bg-blue-50"
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                Message All
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleGoBack}
                className="hover:bg-slate-100"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Add More Contractors
              </Button>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button onClick={handleGoBack} variant="outline" size="lg">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Bids
              </Button>
              <Button
                onClick={handleProceedToContract}
                size="lg"
                className={`${selectedForContract.length > 0
                  ? 'bg-blue-600 hover:bg-blue-700'
                  : 'bg-slate-400 cursor-not-allowed'
                } transition-colors`}
                disabled={selectedForContract.length === 0}
              >
                <span>
                  {selectedForContract.length > 0
                    ? `Proceed to Contract (${selectedForContract.length} selected)`
                    : 'Select contractors to proceed'
                  }
                </span>
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            </div>
          </div>
        </div>
        </div>
      </div>
    </CustomerRoute>
  )
}
