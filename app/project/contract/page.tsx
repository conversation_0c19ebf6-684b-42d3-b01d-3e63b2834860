"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { useUser } from "@/contexts/user-context"
import { 
  ArrowLeft, 
  ArrowRight, 
  FileText, 
  Calendar, 
  DollarSign, 
  CheckCircle, 
  AlertCircle,
  Download,
  Send,
  Edit3,
  Shield
} from "lucide-react"

interface PaymentMilestone {
  id: string
  name: string
  percentage: number
  amount: string
  description: string
  dueDate: string
}

interface ContractData {
  projectTitle: string
  contractorName: string
  projectDescription: string
  startDate: string
  endDate: string
  totalCost: string
  paymentSchedule: string
  paymentMilestones: PaymentMilestone[]
  materials: string
  laborCost: string
  permits: string
  warranty: string
  cancellationPolicy: string
}

export default function ContractPage() {
  const { user } = useUser()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [step, setStep] = useState(1)
  const [isEditing, setIsEditing] = useState(false)
  const [isSigning, setIsSigning] = useState(false)
  const [isDownloading, setIsDownloading] = useState(false)
  const [contractData, setContractData] = useState<ContractData>({
    projectTitle: "Modern Kitchen Renovation",
    contractorName: "Mike's Kitchen Experts",
    projectDescription: "Complete kitchen remodel including new cabinets, countertops, appliances, and flooring.",
    startDate: "",
    endDate: "",
    totalCost: "$28,500",
    paymentSchedule: "30% upfront, 40% at midpoint, 30% upon completion",
    paymentMilestones: [
      {
        id: "1",
        name: "Upfront Payment",
        percentage: 30,
        amount: "$8,550",
        description: "Due upon signing",
        dueDate: "Upon contract signing"
      },
      {
        id: "2",
        name: "Midpoint Payment",
        percentage: 40,
        amount: "$11,400",
        description: "Due at project midpoint",
        dueDate: "50% project completion"
      },
      {
        id: "3",
        name: "Final Payment",
        percentage: 30,
        amount: "$8,550",
        description: "Due upon completion",
        dueDate: "Project completion"
      }
    ],
    materials: "Quartz countertops, custom cabinets, stainless steel appliances",
    laborCost: "$15,000",
    permits: "Building permits included",
    warranty: "2-year warranty on workmanship, manufacturer warranty on materials",
    cancellationPolicy: "48-hour cancellation notice required"
  })

  useEffect(() => {
    // Simulate loading contract data
    setTimeout(() => {
      setLoading(false)
    }, 1000)
  }, [])

  const handleNext = () => {
    if (step < 3) {
      setStep(step + 1)
    }
  }

  const handleBack = () => {
    if (step > 1) {
      setStep(step - 1)
    } else {
      router.push('/project/compare-contractors')
    }
  }

  const handleSignContract = async () => {
    setIsSigning(true)
    try {
      // Simulate contract signing process
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Save contract data to localStorage (in real app, this would be sent to backend)
      localStorage.setItem('signedContract', JSON.stringify({
        ...contractData,
        signedAt: new Date().toISOString(),
        status: 'signed'
      }))

      // Navigate to dashboard with success message
      router.push('/dashboard?contractSigned=true')
    } catch (error) {
      console.error('Error signing contract:', error)
      alert('Error signing contract. Please try again.')
    } finally {
      setIsSigning(false)
    }
  }

  const handleDownloadPDF = async () => {
    setIsDownloading(true)
    try {
      // Simulate PDF generation and download
      await new Promise(resolve => setTimeout(resolve, 1500))

      // Create a simple text representation of the contract
      const contractText = `
CONTRACT AGREEMENT

Project: ${contractData.projectTitle}
Contractor: ${contractData.contractorName}
Description: ${contractData.projectDescription}

Start Date: ${contractData.startDate}
End Date: ${contractData.endDate}
Total Cost: ${contractData.totalCost}

Payment Schedule:
${contractData.paymentMilestones.map(milestone =>
  `- ${milestone.name}: ${milestone.percentage}% (${milestone.amount}) - ${milestone.description}`
).join('\n')}

Terms & Conditions:
Materials: ${contractData.materials}
Warranty: ${contractData.warranty}
Permits: ${contractData.permits}
Cancellation Policy: ${contractData.cancellationPolicy}

Generated on: ${new Date().toLocaleDateString()}
      `

      // Create and download the file
      const blob = new Blob([contractText], { type: 'text/plain' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `${contractData.projectTitle.replace(/\s+/g, '_')}_Contract.txt`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      window.URL.revokeObjectURL(url)

    } catch (error) {
      console.error('Error downloading PDF:', error)
      alert('Error downloading contract. Please try again.')
    } finally {
      setIsDownloading(false)
    }
  }

  const handleEditToggle = () => {
    setIsEditing(!isEditing)
  }

  const handleSaveChanges = () => {
    setIsEditing(false)
    // Here you would typically save to backend
    console.log('Contract data saved:', contractData)
  }

  const handleInputChange = (field: keyof ContractData, value: string) => {
    setContractData(prev => ({ ...prev, [field]: value }))
  }

  const handlePaymentMilestoneChange = (milestoneId: string, field: keyof PaymentMilestone, value: string | number) => {
    setContractData(prev => ({
      ...prev,
      paymentMilestones: prev.paymentMilestones.map(milestone =>
        milestone.id === milestoneId
          ? { ...milestone, [field]: value }
          : milestone
      )
    }))
  }

  const addPaymentMilestone = () => {
    const newMilestone: PaymentMilestone = {
      id: Date.now().toString(),
      name: "New Milestone",
      percentage: 0,
      amount: "$0",
      description: "Payment description",
      dueDate: "Due date"
    }
    setContractData(prev => ({
      ...prev,
      paymentMilestones: [...prev.paymentMilestones, newMilestone]
    }))
  }

  const removePaymentMilestone = (milestoneId: string) => {
    setContractData(prev => ({
      ...prev,
      paymentMilestones: prev.paymentMilestones.filter(milestone => milestone.id !== milestoneId)
    }))
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-white">
        <UnifiedNavigation />
        <div className="container-premium section-premium">
          <div className="text-center py-20">
            <div className="animate-pulse text-slate-500 text-lg">Preparing contract...</div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-white">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Header */}
        <div className="mb-12">
          <Button onClick={handleBack} variant="ghost" className="mb-4">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          
          <h1 className="text-3xl font-semibold text-slate-900 mb-3">Project Contract</h1>
          <p className="text-lg text-slate-600">Review and finalize your project agreement</p>
        </div>

        {/* Progress Steps */}
        <div className="mb-12">
          <div className="flex items-center justify-center space-x-8">
            {[
              { step: 1, label: "Review Terms", icon: FileText },
              { step: 2, label: "Payment Details", icon: DollarSign },
              { step: 3, label: "Sign Contract", icon: CheckCircle }
            ].map((item) => {
              const Icon = item.icon
              return (
                <div key={item.step} className="flex items-center space-x-2">
                  <div className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    step >= item.step 
                      ? "bg-emerald-500 text-white" 
                      : "bg-slate-200 text-slate-500"
                  }`}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <span className={`text-sm font-medium ${
                    step >= item.step ? "text-slate-900" : "text-slate-500"
                  }`}>
                    {item.label}
                  </span>
                </div>
              )
            })}
          </div>
        </div>

        <div className="max-w-4xl mx-auto">
          {step === 1 && (
            <div className="space-y-8">
              {/* Contract Overview */}
              <div className="card-premium p-8">
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-semibold text-slate-900">Contract Overview</h2>
                  <Button
                    onClick={isEditing ? handleSaveChanges : handleEditToggle}
                    variant={isEditing ? "default" : "outline"}
                    size="sm"
                    className={isEditing ? "bg-emerald-600 hover:bg-emerald-700" : ""}
                  >
                    <Edit3 className="h-4 w-4 mr-2" />
                    {isEditing ? "Save Changes" : "Edit Contract"}
                  </Button>
                </div>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-semibold text-slate-700 mb-2 block">Project Title</label>
                      {isEditing ? (
                        <Input
                          value={contractData.projectTitle}
                          onChange={(e) => handleInputChange('projectTitle', e.target.value)}
                          className="h-12"
                        />
                      ) : (
                        <p className="text-slate-900">{contractData.projectTitle}</p>
                      )}
                    </div>

                    <div>
                      <label className="text-sm font-semibold text-slate-700 mb-2 block">Contractor</label>
                      {isEditing ? (
                        <Input
                          value={contractData.contractorName}
                          onChange={(e) => handleInputChange('contractorName', e.target.value)}
                          className="h-12"
                        />
                      ) : (
                        <p className="text-slate-900">{contractData.contractorName}</p>
                      )}
                    </div>

                    <div>
                      <label className="text-sm font-semibold text-slate-700 mb-2 block">Project Description</label>
                      {isEditing ? (
                        <Textarea
                          value={contractData.projectDescription}
                          onChange={(e) => handleInputChange('projectDescription', e.target.value)}
                          className="min-h-[100px]"
                        />
                      ) : (
                        <p className="text-slate-700">{contractData.projectDescription}</p>
                      )}
                    </div>
                  </div>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-semibold text-slate-700 mb-2 block">Start Date</label>
                      <Input 
                        type="date" 
                        value={contractData.startDate}
                        onChange={(e) => setContractData(prev => ({ ...prev, startDate: e.target.value }))}
                        className="h-12"
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm font-semibold text-slate-700 mb-2 block">End Date</label>
                      <Input 
                        type="date" 
                        value={contractData.endDate}
                        onChange={(e) => setContractData(prev => ({ ...prev, endDate: e.target.value }))}
                        className="h-12"
                      />
                    </div>
                    
                    <div>
                      <label className="text-sm font-semibold text-slate-700 mb-2 block">Total Cost</label>
                      {isEditing ? (
                        <Input
                          value={contractData.totalCost}
                          onChange={(e) => handleInputChange('totalCost', e.target.value)}
                          className="h-12 text-2xl font-semibold"
                          placeholder="$0.00"
                        />
                      ) : (
                        <p className="text-2xl font-semibold text-slate-900">{contractData.totalCost}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Terms and Conditions */}
              <div className="card-premium p-8">
                <h2 className="text-xl font-semibold text-slate-900 mb-6">Terms & Conditions</h2>

                <div className="space-y-6">
                  <div>
                    <h3 className="font-semibold text-slate-900 mb-2">Materials</h3>
                    {isEditing ? (
                      <Textarea
                        value={contractData.materials}
                        onChange={(e) => handleInputChange('materials', e.target.value)}
                        className="min-h-[80px]"
                      />
                    ) : (
                      <p className="text-slate-700">{contractData.materials}</p>
                    )}
                  </div>

                  <div>
                    <h3 className="font-semibold text-slate-900 mb-2">Warranty</h3>
                    {isEditing ? (
                      <Textarea
                        value={contractData.warranty}
                        onChange={(e) => handleInputChange('warranty', e.target.value)}
                        className="min-h-[80px]"
                      />
                    ) : (
                      <p className="text-slate-700">{contractData.warranty}</p>
                    )}
                  </div>

                  <div>
                    <h3 className="font-semibold text-slate-900 mb-2">Permits</h3>
                    {isEditing ? (
                      <Textarea
                        value={contractData.permits}
                        onChange={(e) => handleInputChange('permits', e.target.value)}
                        className="min-h-[60px]"
                      />
                    ) : (
                      <p className="text-slate-700">{contractData.permits}</p>
                    )}
                  </div>

                  <div>
                    <h3 className="font-semibold text-slate-900 mb-2">Cancellation Policy</h3>
                    {isEditing ? (
                      <Textarea
                        value={contractData.cancellationPolicy}
                        onChange={(e) => handleInputChange('cancellationPolicy', e.target.value)}
                        className="min-h-[60px]"
                      />
                    ) : (
                      <p className="text-slate-700">{contractData.cancellationPolicy}</p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {step === 2 && (
            <div className="space-y-8">
              {/* Payment Details */}
              <div className="card-premium p-8">
                <h2 className="text-xl font-semibold text-slate-900 mb-6">Payment Details</h2>
                
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div className="space-y-6">
                    <div>
                      <h3 className="font-semibold text-slate-900 mb-4">Cost Breakdown</h3>
                      <div className="space-y-3">
                        <div className="flex justify-between">
                          <span className="text-slate-600">Labor</span>
                          <span className="font-medium">{contractData.laborCost}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-slate-600">Materials</span>
                          <span className="font-medium">$13,500</span>
                        </div>
                        <div className="border-t pt-3 flex justify-between">
                          <span className="font-semibold text-slate-900">Total</span>
                          <span className="font-semibold text-slate-900">{contractData.totalCost}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-6">
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="font-semibold text-slate-900">Payment Schedule</h3>
                        {isEditing && (
                          <Button
                            onClick={addPaymentMilestone}
                            variant="outline"
                            size="sm"
                            className="text-sm"
                          >
                            <Plus className="h-4 w-4 mr-1" />
                            Add Milestone
                          </Button>
                        )}
                      </div>
                      <div className="space-y-3">
                        {contractData.paymentMilestones.map((milestone, index) => (
                          <div key={milestone.id} className={`p-3 rounded-lg border ${
                            isEditing ? 'border-slate-300 bg-white' : 'bg-slate-50 border-slate-200'
                          }`}>
                            {isEditing ? (
                              <div className="space-y-3">
                                <div className="flex items-center justify-between">
                                  <Input
                                    value={milestone.name}
                                    onChange={(e) => handlePaymentMilestoneChange(milestone.id, 'name', e.target.value)}
                                    className="font-medium"
                                    placeholder="Milestone name"
                                  />
                                  {contractData.paymentMilestones.length > 1 && (
                                    <Button
                                      onClick={() => removePaymentMilestone(milestone.id)}
                                      variant="ghost"
                                      size="sm"
                                      className="text-red-500 hover:text-red-700 ml-2"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  )}
                                </div>
                                <div className="grid grid-cols-2 gap-3">
                                  <div>
                                    <label className="text-xs text-slate-600 mb-1 block">Percentage</label>
                                    <Input
                                      type="number"
                                      value={milestone.percentage}
                                      onChange={(e) => handlePaymentMilestoneChange(milestone.id, 'percentage', parseInt(e.target.value) || 0)}
                                      placeholder="0"
                                      className="text-sm"
                                    />
                                  </div>
                                  <div>
                                    <label className="text-xs text-slate-600 mb-1 block">Amount</label>
                                    <Input
                                      value={milestone.amount}
                                      onChange={(e) => handlePaymentMilestoneChange(milestone.id, 'amount', e.target.value)}
                                      placeholder="$0"
                                      className="text-sm"
                                    />
                                  </div>
                                </div>
                                <div>
                                  <label className="text-xs text-slate-600 mb-1 block">Description</label>
                                  <Input
                                    value={milestone.description}
                                    onChange={(e) => handlePaymentMilestoneChange(milestone.id, 'description', e.target.value)}
                                    placeholder="Payment description"
                                    className="text-sm"
                                  />
                                </div>
                                <div>
                                  <label className="text-xs text-slate-600 mb-1 block">Due Date</label>
                                  <Input
                                    value={milestone.dueDate}
                                    onChange={(e) => handlePaymentMilestoneChange(milestone.id, 'dueDate', e.target.value)}
                                    placeholder="Due date"
                                    className="text-sm"
                                  />
                                </div>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-3">
                                {index === 0 ? (
                                  <CheckCircle className="h-5 w-5 text-emerald-500" />
                                ) : (
                                  <div className="w-5 h-5 border-2 border-slate-300 rounded-full"></div>
                                )}
                                <div>
                                  <p className="font-medium text-slate-900">
                                    {milestone.percentage}% {milestone.name}
                                  </p>
                                  <p className="text-sm text-slate-600">
                                    {milestone.amount} - {milestone.description}
                                  </p>
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {step === 3 && (
            <div className="space-y-8">
              {/* Contract Signing */}
              <div className="card-premium p-8 text-center">
                <div className="max-w-md mx-auto">
                  <Shield className="h-16 w-16 text-emerald-500 mx-auto mb-6" />
                  <h2 className="text-2xl font-semibold text-slate-900 mb-4">Ready to Sign</h2>
                  <p className="text-slate-600 mb-8">
                    By signing this contract, you agree to the terms and conditions outlined above. 
                    Your project will begin on the specified start date.
                  </p>
                  
                  <div className="space-y-4">
                    <Button
                      onClick={handleSignContract}
                      size="lg"
                      className="w-full bg-emerald-600 hover:bg-emerald-700"
                      disabled={isSigning}
                    >
                      {isSigning ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                          Signing Contract...
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-5 w-5 mr-2" />
                          Sign Contract & Start Project
                        </>
                      )}
                    </Button>

                    <Button
                      onClick={handleDownloadPDF}
                      variant="outline"
                      size="lg"
                      className="w-full"
                      disabled={isDownloading}
                    >
                      {isDownloading ? (
                        <>
                          <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-slate-600 mr-2"></div>
                          Generating PDF...
                        </>
                      ) : (
                        <>
                          <Download className="h-5 w-5 mr-2" />
                          Download Contract PDF
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Navigation */}
          <div className="flex justify-between mt-12">
            <Button onClick={handleBack} variant="outline" size="lg">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {step === 1 ? "Back to Comparison" : "Previous"}
            </Button>
            
            {step < 3 && (
              <Button onClick={handleNext} size="lg" className="bg-slate-900 hover:bg-slate-800">
                Next Step
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
