"use client"

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation'
import { UnifiedNavigation } from '@/components/unified-navigation'
import { useUser } from '@/contexts/user-context'
import { useMilestones } from '@/hooks/use-milestones'
import { useReviews } from '@/hooks/use-reviews'
import { useMessaging } from '@/hooks/use-messaging'
import { useBidding } from '@/hooks/use-bidding'
import { usePayments } from '@/hooks/use-payments'
import { MilestoneTracker } from '@/components/project/milestone-tracker'
import { ReviewCard } from '@/components/reviews/review-card'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { useToast } from '@/hooks/use-toast'
import {
  MessageCircle,
  Star,
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  User,
  Phone,
  Mail,
  CheckCircle,
  AlertTriangle,
  FileText,
  Camera,
  Share2,
  Edit,
  MoreHorizontal,
  TrendingUp,
  Award,
  Shield,
  Bookmark,
  Heart,
  Download,
  Upload,
  Eye,
  ArrowLeft,
  ExternalLink
} from 'lucide-react'
import { format, differenceInDays, isAfter, isBefore } from 'date-fns'
import Link from 'next/link'

export default function ProjectDetailsPage() {
  const params = useParams()
  const router = useRouter()
  const projectId = params?.id as string
  const { user } = useUser()
  const { toast } = useToast()

  const {
    project,
    milestones,
    loading: milestonesLoading,
    updateProjectStatus,
    updateMilestone,
    addMilestone,
    deleteMilestone
  } = useMilestones({ projectId, autoFetch: true })

  const {
    reviews,
    loading: reviewsLoading,
    createReview,
    addResponse,
    markHelpful
  } = useReviews({ projectId, autoFetch: true })

  const {
    conversations,
    createConversation
  } = useMessaging({ autoFetch: true })

  const {
    bids,
    loading: bidsLoading
  } = useBidding({ projectId, autoFetch: true })

  const {
    payments,
    loading: paymentsLoading
  } = usePayments({ projectId, autoFetch: true })

  const [activeTab, setActiveTab] = useState('overview')
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [isLiked, setIsLiked] = useState(false)

  // Enhanced project data with more details
  const projectData = {
    id: projectId,
    title: 'Kitchen Remodel',
    description: 'Complete kitchen renovation including new cabinets, countertops, and appliances. This project involves removing existing fixtures, installing new plumbing and electrical work, and creating a modern, functional kitchen space.',
    category: 'kitchen',
    status: 'in-progress',
    priority: 'high',
    customer: {
      id: 'customer-1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      avatar_url: '/placeholder.svg',
      verified: true,
      member_since: '2023-06-15'
    },
    contractor: {
      id: 'contractor-1',
      user_id: 'user-contractor-1',
      business_name: "Mike's Kitchen Experts",
      rating: 4.9,
      total_reviews: 127,
      verified: true,
      license_number: 'CA-123456',
      insurance_verified: true,
      users: {
        id: 'user-contractor-1',
        name: 'Mike Johnson',
        email: '<EMAIL>',
        phone: '+****************',
        avatar_url: '/placeholder.svg'
      }
    },
    budget: {
      range: '$15,000 - $25,000',
      approved: '$20,000',
      spent: '$12,500',
      remaining: '$7,500'
    },
    timeline: {
      start_date: '2024-01-15',
      end_date: '2024-02-15',
      estimated_duration: '4 weeks',
      days_remaining: differenceInDays(new Date('2024-02-15'), new Date()),
      is_on_schedule: true
    },
    location: {
      address: '123 Main St, San Francisco, CA 94102',
      city: 'San Francisco',
      state: 'CA',
      zip: '94102'
    },
    images: [
      '/placeholder.svg',
      '/placeholder.svg',
      '/placeholder.svg'
    ],
    documents: [
      { name: 'Contract.pdf', size: '2.4 MB', uploaded: '2024-01-10' },
      { name: 'Permits.pdf', size: '1.8 MB', uploaded: '2024-01-12' }
    ],
    created_at: '2024-01-10T10:00:00Z',
    updated_at: '2024-01-20T14:30:00Z'
  }

  // Calculate project progress
  const completedMilestones = milestones.filter(m => m.status === 'completed').length
  const totalMilestones = milestones.length
  const progressPercentage = totalMilestones > 0 ? Math.round((completedMilestones / totalMilestones) * 100) : 0

  // Calculate budget progress
  const budgetUsed = parseFloat(projectData.budget.spent.replace(/[$,]/g, ''))
  const budgetTotal = parseFloat(projectData.budget.approved.replace(/[$,]/g, ''))
  const budgetPercentage = Math.round((budgetUsed / budgetTotal) * 100)

  const handleStartConversation = async () => {
    // Enhanced validation and error handling
    if (!user) {
      console.error('User not authenticated')
      toast({
        title: "Authentication Required",
        description: "Please log in to start a conversation",
        variant: "destructive"
      })
      return
    }



    if (!projectData.contractor) {
      console.error('No contractor data available')
      toast({
        title: "Error",
        description: "Contractor information not available",
        variant: "destructive"
      })
      return
    }

    // Use the contractor's user_id for the conversation, not the contractor id
    const contractorUserId = projectData.contractor.user_id || projectData.contractor.users?.id

    if (!contractorUserId) {
      console.error('Contractor user ID not found', projectData.contractor)
      toast({
        title: "Error",
        description: "Unable to identify contractor for messaging",
        variant: "destructive"
      })
      return
    }

    console.log('Starting conversation between:', {
      currentUser: user.id,
      contractorUser: contractorUserId,
      projectId: projectId
    })

    try {
      const conversationId = await createConversation(
        projectId,
        [contractorUserId] // Pass the contractor's user ID, not contractor ID
      )

      if (conversationId) {
        // Navigate to messages page with this conversation
        window.location.href = `/messages?conversation=${conversationId}`
      } else {
        console.error('Failed to create conversation - no conversation ID returned')
        toast({
          title: "Error",
          description: "Failed to create conversation. Please try again.",
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Error in handleStartConversation:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred while starting the conversation",
        variant: "destructive"
      })
    }
  }

  const canEdit = user?.role === 'customer' || user?.role === 'contractor'
  const isCustomer = user?.role === 'customer'
  const isContractor = user?.role === 'contractor'

  if (!projectId) {
    return (
      <div className="min-h-screen bg-slate-50">
        <UnifiedNavigation />
        <div className="flex items-center justify-center h-96">
          <p className="text-slate-500">Project not found</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="container-native section-native">
        {/* Mobile-First Breadcrumb */}
        <div className="flex items-center space-x-2 text-sm text-slate-600 mb-4 sm:mb-6">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="p-0 h-auto font-normal hover:text-slate-900"
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            Back
          </Button>
          <span className="hidden sm:inline">/</span>
          <Link href="/projects" className="hover:text-slate-900 hidden sm:inline">Projects</Link>
          <span className="hidden sm:inline">/</span>
          <span className="text-slate-900 font-medium truncate">{projectData.title}</span>
        </div>

        {/* Streamlined Project Header */}
        <div className="bg-white rounded-lg shadow-sm border border-slate-200 overflow-hidden mb-6">
          {/* Compact Header */}
          <div className="p-4 sm:p-6 border-b border-slate-100">
            <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
              <div className="flex-1 min-w-0">
                <div className="flex items-start gap-3">
                  <div className="w-12 h-12 sm:w-14 sm:h-14 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <FileText className="h-6 w-6 sm:h-7 sm:w-7 text-blue-600" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h1 className="text-xl sm:text-2xl font-bold text-slate-900 mb-2 leading-tight">
                      {projectData.title}
                    </h1>
                    <div className="flex flex-wrap items-center gap-2 mb-3">
                      <Badge
                        className={
                          projectData.status === 'completed' ? 'bg-green-100 text-green-800' :
                          projectData.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                          'bg-yellow-100 text-yellow-800'
                        }
                      >
                        {projectData.status.replace('-', ' ').toUpperCase()}
                      </Badge>
                      {projectData.priority === 'high' && (
                        <Badge className="bg-red-100 text-red-800">
                          HIGH PRIORITY
                        </Badge>
                      )}
                    </div>
                    <p className="text-slate-600 text-sm sm:text-base line-clamp-2">
                      {projectData.description}
                    </p>
                  </div>
                </div>
              </div>

              {/* Mobile-Optimized Action Buttons */}
              <div className="flex items-center gap-2 sm:flex-col sm:gap-3">
                <Button
                  onClick={handleStartConversation}
                  disabled={!user}
                  className="flex-1 sm:flex-none bg-blue-600 hover:bg-blue-700 text-white disabled:opacity-50"
                  size="sm"
                >
                  <MessageCircle className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Message {isCustomer ? 'Contractor' : 'Customer'}</span>
                  <span className="sm:hidden">Message</span>
                </Button>

                {/* Mobile Quick Actions */}
                {isCustomer && projectData.status === 'completed' && (
                  <Button variant="outline" className="hidden sm:flex" size="sm">
                    <Star className="h-4 w-4 mr-2" />
                    Leave Review
                  </Button>
                )}

                {isContractor && (
                  <Button variant="outline" className="hidden sm:flex" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Photos
                  </Button>
                )}

                <div className="flex items-center gap-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsBookmarked(!isBookmarked)}
                    className="p-2"
                  >
                    <Bookmark className={`h-4 w-4 ${isBookmarked ? 'fill-current text-blue-600' : 'text-slate-400'}`} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsLiked(!isLiked)}
                    className="p-2"
                  >
                    <Heart className={`h-4 w-4 ${isLiked ? 'fill-current text-red-500' : 'text-slate-400'}`} />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="p-2 text-slate-400 hover:text-slate-600"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Compact Key Metrics */}
          <div className="p-4 sm:p-6">
            <div className="grid grid-cols-2 sm:grid-cols-4 gap-3 sm:gap-4 mb-6">
              <div className="text-center p-3 sm:p-4 bg-slate-50 rounded-lg">
                <div className="text-lg sm:text-2xl font-bold text-slate-900">{progressPercentage}%</div>
                <div className="text-xs sm:text-sm text-slate-600">Complete</div>
              </div>
              <div className="text-center p-3 sm:p-4 bg-slate-50 rounded-lg">
                <div className="text-lg sm:text-2xl font-bold text-slate-900">{projectData.timeline.days_remaining}</div>
                <div className="text-xs sm:text-sm text-slate-600">Days Left</div>
              </div>
              <div className="text-center p-3 sm:p-4 bg-slate-50 rounded-lg">
                <div className="text-lg sm:text-2xl font-bold text-slate-900">{budgetPercentage}%</div>
                <div className="text-xs sm:text-sm text-slate-600">Budget Used</div>
              </div>
              <div className="text-center p-3 sm:p-4 bg-slate-50 rounded-lg">
                <div className="text-lg sm:text-2xl font-bold text-slate-900">{reviews.length}</div>
                <div className="text-xs sm:text-sm text-slate-600">Reviews</div>
              </div>
            </div>

            {/* Essential Info Grid */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
              <div className="flex items-center space-x-2">
                <DollarSign className="h-4 w-4 text-slate-400 flex-shrink-0" />
                <div>
                  <span className="text-slate-600">Budget:</span>
                  <span className="font-medium ml-1">{projectData.budget.approved}</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-slate-400 flex-shrink-0" />
                <div>
                  <span className="text-slate-600">Timeline:</span>
                  <span className="font-medium ml-1">{projectData.timeline.estimated_duration}</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="h-4 w-4 text-slate-400 flex-shrink-0" />
                <div>
                  <span className="text-slate-600">Location:</span>
                  <span className="font-medium ml-1">{projectData.location.city}, {projectData.location.state}</span>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-slate-400 flex-shrink-0" />
                <div>
                  <span className="text-slate-600">Created:</span>
                  <span className="font-medium ml-1">{format(new Date(projectData.created_at), 'MMM d')}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Streamlined Participants Section */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 mb-6">
          {/* Customer Card */}
          <Card className="border-slate-200 shadow-sm">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="relative flex-shrink-0">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-lg">
                      {projectData.customer.name.charAt(0)}
                    </span>
                  </div>
                  {projectData.customer.verified && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-3 w-3 text-white" />
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <h3 className="font-medium text-slate-900 truncate">{projectData.customer.name}</h3>
                    <User className="h-4 w-4 text-slate-400 flex-shrink-0" />
                  </div>
                  <p className="text-sm text-slate-500">Customer</p>
                  <div className="flex items-center space-x-2 text-xs text-slate-400 mt-1">
                    <span>Member since {format(new Date(projectData.customer.member_since), 'MMM yyyy')}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contractor Card */}
          {projectData.contractor && (
            <Card className="border-slate-200 shadow-sm">
              <CardContent className="p-4">
                <div className="flex items-center space-x-3">
                  <div className="relative flex-shrink-0">
                    <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                      <span className="text-green-600 font-semibold text-lg">
                        {projectData.contractor.business_name.charAt(0)}
                      </span>
                    </div>
                    {projectData.contractor.verified && (
                      <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                        <CheckCircle className="h-3 w-3 text-white" />
                      </div>
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-medium text-slate-900 truncate">{projectData.contractor.business_name}</h3>
                      <Award className="h-4 w-4 text-slate-400 flex-shrink-0" />
                    </div>
                    <div className="flex items-center space-x-2 mb-1">
                      <div className="flex items-center space-x-1">
                        {Array.from({ length: 5 }, (_, i) => (
                          <Star
                            key={i}
                            className={`h-3 w-3 ${
                              i < Math.floor(projectData.contractor.rating)
                                ? 'text-yellow-400 fill-current'
                                : 'text-slate-300'
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-xs text-slate-600">
                        {projectData.contractor.rating} ({projectData.contractor.total_reviews})
                      </span>
                    </div>
                    <div className="flex flex-wrap gap-1">
                      {projectData.contractor.verified && (
                        <Badge variant="outline" className="text-xs text-green-600 border-green-200 px-1 py-0">
                          Verified
                        </Badge>
                      )}
                      {projectData.contractor.insurance_verified && (
                        <Badge variant="outline" className="text-xs text-blue-600 border-blue-200 px-1 py-0">
                          Insured
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Mobile-Optimized Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          {/* Mobile: Horizontal Scroll Tabs */}
          <div className="sm:hidden">
            <div className="flex space-x-1 overflow-x-auto pb-2 mb-4">
              {[
                { value: 'overview', label: 'Overview', icon: Eye },
                { value: 'milestones', label: 'Progress', icon: TrendingUp },
                { value: 'bids', label: `Bids (${bids.length})`, icon: FileText },
                { value: 'payments', label: 'Payments', icon: DollarSign },
                { value: 'documents', label: 'Files', icon: FileText },
                { value: 'reviews', label: `Reviews (${reviews.length})`, icon: Star }
              ].map((tab) => (
                <button
                  key={tab.value}
                  onClick={() => setActiveTab(tab.value)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-sm font-medium whitespace-nowrap transition-colors ${
                    activeTab === tab.value
                      ? 'bg-blue-600 text-white'
                      : 'bg-white text-slate-600 border border-slate-200 hover:bg-slate-50'
                  }`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Desktop: Grid Tabs */}
          <TabsList className="hidden sm:grid w-full grid-cols-3 lg:grid-cols-6 bg-white border border-slate-200 rounded-lg p-1">
            <TabsTrigger value="overview" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
              <Eye className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">Overview</span>
            </TabsTrigger>
            <TabsTrigger value="milestones" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
              <TrendingUp className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">Progress</span>
            </TabsTrigger>
            <TabsTrigger value="bids" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
              <FileText className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">Bids ({bids.length})</span>
            </TabsTrigger>
            <TabsTrigger value="payments" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
              <DollarSign className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">Payments</span>
            </TabsTrigger>
            <TabsTrigger value="documents" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
              <FileText className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">Files</span>
            </TabsTrigger>
            <TabsTrigger value="reviews" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
              <Star className="h-4 w-4 mr-2" />
              <span className="hidden lg:inline">Reviews ({reviews.length})</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="mt-4 sm:mt-6">
            <div className="space-y-4 sm:space-y-6">
              {/* Progress Overview */}
              <Card className="border-slate-200 shadow-sm">
                <CardContent className="p-4 sm:p-6">
                  <div className="space-y-4">
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-slate-700">Project Progress</span>
                        <span className="text-sm font-bold text-slate-900">{progressPercentage}%</span>
                      </div>
                      <Progress value={progressPercentage} className="h-2" />
                    </div>
                    <div>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-slate-700">Budget Used</span>
                        <span className="text-sm font-bold text-slate-900">{budgetPercentage}%</span>
                      </div>
                      <Progress value={budgetPercentage} className="h-2" />
                      <div className="flex justify-between text-xs text-slate-500 mt-1">
                        <span>{projectData.budget.spent}</span>
                        <span>{projectData.budget.approved}</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Project Details */}
              <Card className="border-slate-200 shadow-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">Project Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h4 className="font-medium text-slate-900 mb-2">Description</h4>
                    <p className="text-slate-600 text-sm leading-relaxed">{projectData.description}</p>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 pt-2">
                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Category</span>
                        <Badge variant="outline" className="capitalize text-xs">
                          {projectData.category}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Priority</span>
                        <Badge
                          className={`text-xs ${
                            projectData.priority === 'high' ? 'bg-red-100 text-red-800' :
                            projectData.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-green-100 text-green-800'
                          }`}
                        >
                          {projectData.priority.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Duration</span>
                        <span className="text-sm font-medium">{projectData.timeline.estimated_duration}</span>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Start Date</span>
                        <span className="text-sm font-medium">
                          {format(new Date(projectData.timeline.start_date), 'MMM d, yyyy')}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">End Date</span>
                        <span className="text-sm font-medium">
                          {format(new Date(projectData.timeline.end_date), 'MMM d, yyyy')}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-slate-600">Days Left</span>
                        <span className={`text-sm font-medium ${
                          projectData.timeline.days_remaining < 7 ? 'text-red-600' :
                          projectData.timeline.days_remaining < 14 ? 'text-yellow-600' :
                          'text-green-600'
                        }`}>
                          {projectData.timeline.days_remaining} days
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="pt-2">
                    <h4 className="font-medium text-slate-900 mb-2">Location</h4>
                    <div className="flex items-start space-x-2 p-3 bg-slate-50 rounded-lg">
                      <MapPin className="h-4 w-4 text-slate-400 mt-0.5 flex-shrink-0" />
                      <div>
                        <p className="text-sm font-medium text-slate-900">{projectData.location.address}</p>
                        <p className="text-xs text-slate-600">
                          {projectData.location.city}, {projectData.location.state} {projectData.location.zip}
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Project Photos */}
              <Card className="border-slate-200 shadow-sm">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center space-x-2">
                    <Camera className="h-5 w-5 text-blue-600" />
                    <span>Project Photos</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-3">
                    {projectData.images.map((image, index) => (
                      <div key={index} className="aspect-square bg-slate-100 rounded-lg overflow-hidden">
                        <img
                          src={image}
                          alt={`Project photo ${index + 1}`}
                          className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                        />
                      </div>
                    ))}
                    <div className="aspect-square bg-slate-50 rounded-lg border-2 border-dashed border-slate-300 flex items-center justify-center cursor-pointer hover:border-slate-400 transition-colors">
                      <div className="text-center">
                        <Camera className="h-6 w-6 text-slate-400 mx-auto mb-1" />
                        <p className="text-xs text-slate-500">Add Photo</p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="milestones" className="mt-6">
            {project && (
              <MilestoneTracker
                project={project}
                milestones={milestones}
                onUpdateMilestone={updateMilestone}
                onAddMilestone={() => {
                  // This would open a modal to add milestone
                  console.log('Add milestone')
                }}
                onEditMilestone={(milestone) => {
                  // This would open a modal to edit milestone
                  console.log('Edit milestone', milestone)
                }}
                onDeleteMilestone={deleteMilestone}
                onUpdateProjectStatus={updateProjectStatus}
                loading={milestonesLoading}
                canEdit={canEdit}
              />
            )}
          </TabsContent>

          {/* Bids Tab */}
          <TabsContent value="bids" className="mt-6">
            <Card className="border-slate-200/60 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <span>Project Bids</span>
                  </div>
                  {isCustomer && (
                    <Link href={`/project/bids?projectId=${projectId}`}>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-2" />
                        View All Bids
                      </Button>
                    </Link>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {bidsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p className="text-slate-600 mt-2">Loading bids...</p>
                  </div>
                ) : bids.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-900 mb-2">No bids yet</h3>
                    <p className="text-slate-600 mb-4">
                      {isCustomer ? 'No contractors have submitted bids for this project yet.' : 'You haven\'t submitted a bid for this project.'}
                    </p>
                    {isContractor && (
                      <Link href={`/pro/projects/${projectId}/bid`}>
                        <Button>
                          <FileText className="h-4 w-4 mr-2" />
                          Submit Bid
                        </Button>
                      </Link>
                    )}
                  </div>
                ) : (
                  <div className="space-y-4">
                    {bids.slice(0, 3).map((bid) => (
                      <div key={bid.id} className="border border-slate-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center space-x-3">
                            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                              <User className="h-5 w-5 text-blue-600" />
                            </div>
                            <div>
                              <h4 className="font-medium text-slate-900">
                                {bid.contractor?.business_name || 'Contractor'}
                              </h4>
                              <p className="text-sm text-slate-500">
                                Submitted {format(new Date(bid.created_at), 'MMM d, yyyy')}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-slate-900">
                              ${bid.amount.toLocaleString()}
                            </p>
                            <Badge
                              className={
                                bid.status === 'accepted' ? 'bg-green-100 text-green-800' :
                                bid.status === 'rejected' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }
                            >
                              {bid.status.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                        <p className="text-slate-600 text-sm mb-3">{bid.description}</p>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-slate-500">
                            Timeline: {bid.timeline?.duration || 'N/A'} days
                          </span>
                          {isCustomer && bid.status === 'submitted' && (
                            <div className="space-x-2">
                              <Button size="sm" variant="outline">
                                View Details
                              </Button>
                              <Button size="sm">
                                Accept Bid
                              </Button>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    {bids.length > 3 && (
                      <div className="text-center pt-4">
                        <Link href={`/project/bids?projectId=${projectId}`}>
                          <Button variant="outline">
                            View All {bids.length} Bids
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Payments Tab */}
          <TabsContent value="payments" className="mt-6">
            <Card className="border-slate-200/60 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    <span>Project Payments</span>
                  </div>
                  <Link href={`/payments?projectId=${projectId}`}>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      View All Payments
                    </Button>
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {paymentsLoading ? (
                  <div className="text-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
                    <p className="text-slate-600 mt-2">Loading payments...</p>
                  </div>
                ) : payments.length === 0 ? (
                  <div className="text-center py-8">
                    <DollarSign className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-900 mb-2">No payments yet</h3>
                    <p className="text-slate-600">Payments will appear here once the project begins.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {payments.slice(0, 3).map((payment) => (
                      <div key={payment.id} className="border border-slate-200 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <h4 className="font-medium text-slate-900">
                              {payment.description || 'Project Payment'}
                            </h4>
                            <p className="text-sm text-slate-500">
                              {format(new Date(payment.created_at), 'MMM d, yyyy')}
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-lg font-bold text-slate-900">
                              ${payment.amount.toLocaleString()}
                            </p>
                            <Badge
                              className={
                                payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                                payment.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                                payment.status === 'failed' ? 'bg-red-100 text-red-800' :
                                'bg-yellow-100 text-yellow-800'
                              }
                            >
                              {payment.status.toUpperCase()}
                            </Badge>
                          </div>
                        </div>
                      </div>
                    ))}
                    {payments.length > 3 && (
                      <div className="text-center pt-4">
                        <Link href={`/payments?projectId=${projectId}`}>
                          <Button variant="outline">
                            View All {payments.length} Payments
                          </Button>
                        </Link>
                      </div>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* Documents Tab */}
          <TabsContent value="documents" className="mt-6">
            <Card className="border-slate-200/60 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-purple-600" />
                    <span>Project Documents</span>
                  </div>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Upload Document
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {projectData.documents.length === 0 ? (
                  <div className="text-center py-8">
                    <FileText className="h-12 w-12 text-slate-300 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-slate-900 mb-2">No documents yet</h3>
                    <p className="text-slate-600 mb-4">Upload contracts, permits, and other project documents.</p>
                    <Button>
                      <Upload className="h-4 w-4 mr-2" />
                      Upload First Document
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {projectData.documents.map((doc, index) => (
                      <div key={index} className="flex items-center justify-between p-3 border border-slate-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                            <FileText className="h-5 w-5 text-purple-600" />
                          </div>
                          <div>
                            <h4 className="font-medium text-slate-900">{doc.name}</h4>
                            <p className="text-sm text-slate-500">
                              {doc.size} • Uploaded {format(new Date(doc.uploaded), 'MMM d, yyyy')}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Button variant="ghost" size="sm">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reviews" className="mt-6">
            <div className="space-y-6">
              {reviews.length === 0 ? (
                <Card>
                  <CardContent className="text-center py-8">
                    <Star className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                    <p className="text-slate-500">No reviews yet</p>
                    {isCustomer && projectData.status === 'completed' && (
                      <Button className="mt-4">
                        Be the first to leave a review
                      </Button>
                    )}
                  </CardContent>
                </Card>
              ) : (
                reviews.map((review) => (
                  <ReviewCard
                    key={review.id}
                    review={review}
                    onMarkHelpful={markHelpful}
                    onAddResponse={isContractor ? addResponse : undefined}
                    canRespond={isContractor}
                    showProject={false}
                  />
                ))
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
