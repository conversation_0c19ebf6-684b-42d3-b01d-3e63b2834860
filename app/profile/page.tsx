"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Switch } from "@/components/ui/switch"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { RoleSwitcher } from "@/components/role-switcher"
import { useUser } from "@/contexts/user-context"
import { Camera, Bell, CreditCard, Shield, Phone, Mail, Edit3, Check, User, Settings, Star } from "lucide-react"

export default function ProfilePage() {
  const { user } = useUser()
  const [isEditing, setIsEditing] = useState(false)
  const [profile, setProfile] = useState({
    name: user?.name || "",
    email: user?.email || "",
    phone: user?.phone || "",
    location: user?.location || "",
    bio: user?.bio || "",
  })

  const [notifications, setNotifications] = useState({
    email: true,
    push: true,
    sms: false,
    marketing: false,
  })

  // Update profile when user data changes
  useEffect(() => {
    if (user) {
      setProfile({
        name: user.name || "",
        email: user.email || "",
        phone: user.phone || "",
        location: user.location || "",
        bio: user.bio || "",
      })
    }
  }, [user])

  const handleSave = () => {
    setIsEditing(false)
    // Save logic here - would typically update user context and database
    console.log("Saving profile:", profile)
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
      <UnifiedNavigation />

      <div className="container-premium section-premium">
        {/* Enhanced Header */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-12 gap-4">
          <div>
            <h1 className="text-3xl font-semibold text-slate-900 mb-2">Profile Settings</h1>
            <p className="text-lg text-slate-600">Manage your account information and preferences</p>
          </div>

          <Button
            onClick={() => (isEditing ? handleSave() : setIsEditing(true))}
            className={`${
              isEditing
                ? "bg-emerald-600 hover:bg-emerald-700 text-white shadow-lg"
                : "bg-white border-2 border-slate-200 hover:border-slate-300 text-slate-700 hover:bg-slate-50"
            } font-medium px-6 py-3 rounded-xl transition-all duration-200`}
          >
            {isEditing ? (
              <>
                <Check className="h-4 w-4 mr-2" />
                Save Changes
              </>
            ) : (
              <>
                <Edit3 className="h-4 w-4 mr-2" />
                Edit Profile
              </>
            )}
          </Button>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          {/* Main Profile */}
          <div className="xl:col-span-2 space-y-8">
            {/* Personal Information */}
            <div className="card-premium p-8">
              <h2 className="text-xl font-semibold text-slate-900 mb-8">Personal Information</h2>

              {/* Enhanced Avatar Section */}
              <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-8 mb-10">
                <div className="relative">
                  <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-full flex items-center justify-center shadow-lg">
                    <span className="text-3xl font-semibold text-white">{profile.name.charAt(0)}</span>
                  </div>
                  {isEditing && (
                    <button className="absolute -bottom-2 -right-2 w-8 h-8 bg-slate-900 text-white rounded-full flex items-center justify-center hover:bg-slate-800 transition-all duration-200 shadow-lg">
                      <Camera className="h-4 w-4" />
                    </button>
                  )}
                </div>
                <div className="flex-1">
                  <h3 className="text-2xl font-semibold text-slate-900 mb-1">{profile.name || "User"}</h3>
                  <p className="text-slate-600 mb-3">
                    Member since {user?.createdAt ? new Date(user.createdAt).toLocaleDateString('en-US', { month: 'long', year: 'numeric' }) : 'Recently'}
                  </p>
                  <div className="flex flex-wrap items-center gap-4">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${user?.verified ? 'bg-emerald-500' : 'bg-amber-500'}`}></div>
                      <span className={`text-sm font-medium ${user?.verified ? 'text-emerald-700' : 'text-amber-700'}`}>
                        {user?.verified ? 'Verified Account' : 'Verification Pending'}
                      </span>
                    </div>
                    {user?.rating && (
                      <div className="flex items-center space-x-1">
                        <Star className="h-4 w-4 text-amber-500 fill-current" />
                        <span className="text-sm font-medium text-slate-700">{user.rating} Rating</span>
                      </div>
                    )}
                    <div className="flex items-center space-x-1">
                      <span className="text-sm font-medium text-slate-600 capitalize">{user?.role || 'User'}</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Form Fields */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <Label htmlFor="name" className="text-sm font-semibold text-slate-700">
                    Full Name
                  </Label>
                  <Input
                    id="name"
                    value={profile.name}
                    onChange={(e) => setProfile((prev) => ({ ...prev, name: e.target.value }))}
                    disabled={!isEditing}
                    className={`h-12 ${
                      isEditing
                        ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10"
                        : "border-slate-200 bg-slate-50/50"
                    } transition-all duration-200`}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="email" className="text-sm font-semibold text-slate-700">
                    Email Address
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={profile.email}
                    onChange={(e) => setProfile((prev) => ({ ...prev, email: e.target.value }))}
                    disabled={!isEditing}
                    className={`h-12 ${
                      isEditing
                        ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10"
                        : "border-slate-200 bg-slate-50/50"
                    } transition-all duration-200`}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="phone" className="text-sm font-semibold text-slate-700">
                    Phone Number
                  </Label>
                  <Input
                    id="phone"
                    value={profile.phone}
                    onChange={(e) => setProfile((prev) => ({ ...prev, phone: e.target.value }))}
                    disabled={!isEditing}
                    className={`h-12 ${
                      isEditing
                        ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10"
                        : "border-slate-200 bg-slate-50/50"
                    } transition-all duration-200`}
                  />
                </div>

                <div className="space-y-3">
                  <Label htmlFor="location" className="text-sm font-semibold text-slate-700">
                    Location
                  </Label>
                  <Input
                    id="location"
                    value={profile.location}
                    onChange={(e) => setProfile((prev) => ({ ...prev, location: e.target.value }))}
                    disabled={!isEditing}
                    className={`h-12 ${
                      isEditing
                        ? "border-2 border-slate-200 focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10"
                        : "border-slate-200 bg-slate-50/50"
                    } transition-all duration-200`}
                  />
                </div>
              </div>

              <div className="mt-6 space-y-2">
                <Label htmlFor="bio" className="text-sm font-medium text-slate-700">
                  Bio
                </Label>
                <Textarea
                  id="bio"
                  value={profile.bio}
                  onChange={(e) => setProfile((prev) => ({ ...prev, bio: e.target.value }))}
                  disabled={!isEditing}
                  className="border-slate-200 focus:border-slate-300 disabled:bg-slate-50 min-h-[80px]"
                  placeholder="Tell us a bit about yourself..."
                />
              </div>
            </div>

            {/* Notification Preferences */}
            <div className="bg-white border border-slate-100 rounded-xl p-6">
              <h2 className="text-lg font-medium text-slate-900 mb-6">Notification Preferences</h2>

              <div className="space-y-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-slate-400" />
                    <div>
                      <p className="font-medium text-slate-900">Email Notifications</p>
                      <p className="text-sm text-slate-500">Receive updates about your projects</p>
                    </div>
                  </div>
                  <Switch
                    checked={notifications.email}
                    onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, email: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Bell className="h-5 w-5 text-slate-400" />
                    <div>
                      <p className="font-medium text-slate-900">Push Notifications</p>
                      <p className="text-sm text-slate-500">Get notified about new bids and messages</p>
                    </div>
                  </div>
                  <Switch
                    checked={notifications.push}
                    onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, push: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Phone className="h-5 w-5 text-slate-400" />
                    <div>
                      <p className="font-medium text-slate-900">SMS Notifications</p>
                      <p className="text-sm text-slate-500">Important updates via text message</p>
                    </div>
                  </div>
                  <Switch
                    checked={notifications.sms}
                    onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, sms: checked }))}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Mail className="h-5 w-5 text-slate-400" />
                    <div>
                      <p className="font-medium text-slate-900">Marketing Emails</p>
                      <p className="text-sm text-slate-500">Tips, trends, and special offers</p>
                    </div>
                  </div>
                  <Switch
                    checked={notifications.marketing}
                    onCheckedChange={(checked) => setNotifications((prev) => ({ ...prev, marketing: checked }))}
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Account Stats */}
            <div className="bg-white border border-slate-100 rounded-xl p-6">
              <h3 className="font-medium text-slate-900 mb-4">Account Overview</h3>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-slate-600">Projects Posted</span>
                  <span className="font-medium text-slate-900">3</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600">Projects Completed</span>
                  <span className="font-medium text-slate-900">1</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600">Total Spent</span>
                  <span className="font-medium text-slate-900">$12,500</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-slate-600">Average Rating</span>
                  <span className="font-medium text-slate-900">4.8 ★</span>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white border border-slate-100 rounded-xl p-6">
              <h3 className="font-medium text-slate-900 mb-4">Account Settings</h3>

              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent border-slate-200 hover:border-slate-300"
                >
                  <CreditCard className="h-4 w-4 mr-3" />
                  Payment Methods
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent border-slate-200 hover:border-slate-300"
                >
                  <Shield className="h-4 w-4 mr-3" />
                  Privacy & Security
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent border-slate-200 hover:border-slate-300"
                >
                  <Bell className="h-4 w-4 mr-3" />
                  Notification Settings
                </Button>
              </div>
            </div>

            {/* Support */}
            <div className="bg-white border border-slate-100 rounded-xl p-6">
              <h3 className="font-medium text-slate-900 mb-4">Need Help?</h3>

              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent border-slate-200 hover:border-slate-300"
                >
                  <Mail className="h-4 w-4 mr-3" />
                  Contact Support
                </Button>

                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent border-slate-200 hover:border-slate-300"
                >
                  <Phone className="h-4 w-4 mr-3" />
                  Schedule Call
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <RoleSwitcher />
    </div>
  )
}
