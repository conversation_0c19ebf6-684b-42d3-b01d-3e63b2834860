"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useUser } from "@/contexts/user-context"
import { authService } from "@/services/auth"
import { Logo } from "@/components/logo"
import { Loader2, CheckCircle, AlertCircle } from "lucide-react"

export default function AuthCallbackPage() {
  const router = useRouter()
  const { setUser } = useUser()
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading')
  const [message, setMessage] = useState('')

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const result = await authService.handleOAuthCallback()
        
        if (result.success && result.user) {
          setUser(result.user)
          setStatus('success')
          setMessage('Successfully signed in with Google!')
          
          // Redirect based on user role after a short delay
          setTimeout(() => {
            if (result.user?.role === 'pro') {
              router.push('/pro/dashboard')
            } else {
              router.push('/dashboard')
            }
          }, 2000)
        } else {
          setStatus('error')
          setMessage(result.error || 'Authentication failed')
          
          // Redirect to login after error
          setTimeout(() => {
            router.push('/login')
          }, 3000)
        }
      } catch (error) {
        console.error('Callback error:', error)
        setStatus('error')
        setMessage('An unexpected error occurred')
        
        setTimeout(() => {
          router.push('/login')
        }, 3000)
      }
    }

    handleCallback()
  }, [router, setUser])

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 flex items-center justify-center">
      <div className="text-center max-w-md mx-auto px-6">
        <Logo size="xl" className="justify-center mb-8" />
        
        <div className="bg-white/95 backdrop-blur-xl border border-slate-200/60 rounded-3xl p-8 shadow-xl">
          {status === 'loading' && (
            <>
              <Loader2 className="h-12 w-12 animate-spin text-philippine-green mx-auto mb-6" />
              <h1 className="text-xl font-bold text-raisin-black mb-3">Signing you in...</h1>
              <p className="text-raisin-black/70">Please wait while we complete your Google sign-in.</p>
            </>
          )}
          
          {status === 'success' && (
            <>
              <CheckCircle className="h-12 w-12 text-philippine-green mx-auto mb-6" />
              <h1 className="text-xl font-bold text-raisin-black mb-3">Welcome to RenovHub!</h1>
              <p className="text-raisin-black/70">{message}</p>
              <p className="text-sm text-raisin-black/60 mt-3">Redirecting you to your dashboard...</p>
            </>
          )}
          
          {status === 'error' && (
            <>
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-6" />
              <h1 className="text-xl font-bold text-raisin-black mb-3">Sign-in Failed</h1>
              <p className="text-raisin-black/70 mb-4">{message}</p>
              <p className="text-sm text-raisin-black/60">Redirecting you back to login...</p>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
