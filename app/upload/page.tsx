"use client"

import { useState, useRef, type ChangeEvent, type DragEvent } from "react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { UnifiedNavigation } from "@/components/unified-navigation"
import { Upload, X, ImageIcon, File, Check, ArrowLeft, Camera } from "lucide-react"
import Link from "next/link"

interface UploadedFile {
  id: string
  file: File
  preview: string
  progress: number
  status: "uploading" | "complete" | "error"
  type: "image" | "document"
}

export default function UploadPage() {
  const [files, setFiles] = useState<UploadedFile[]>([])
  const [isDragging, setIsDragging] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      addFiles(Array.from(e.target.files))
    }
  }

  const handleDragOver = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(true)
  }

  const handleDragLeave = () => {
    setIsDragging(false)
  }

  const handleDrop = (e: DragEvent<HTMLDivElement>) => {
    e.preventDefault()
    setIsDragging(false)

    if (e.dataTransfer.files) {
      addFiles(Array.from(e.dataTransfer.files))
    }
  }

  const addFiles = (newFiles: File[]) => {
    const updatedFiles: UploadedFile[] = newFiles.map((file) => {
      const isImage = file.type.startsWith("image/")

      return {
        id: Math.random().toString(36).substring(2, 9),
        file,
        preview: isImage ? URL.createObjectURL(file) : "",
        progress: 0,
        status: "uploading",
        type: isImage ? "image" : "document",
      }
    })

    setFiles((prev) => [...prev, ...updatedFiles])

    // Simulate upload progress
    updatedFiles.forEach((file) => {
      simulateUpload(file.id)
    })
  }

  const simulateUpload = (fileId: string) => {
    let progress = 0
    const interval = setInterval(() => {
      progress += Math.floor(Math.random() * 10) + 5

      if (progress >= 100) {
        clearInterval(interval)
        progress = 100

        setFiles((prev) => prev.map((f) => (f.id === fileId ? { ...f, progress, status: "complete" } : f)))
      } else {
        setFiles((prev) => prev.map((f) => (f.id === fileId ? { ...f, progress } : f)))
      }
    }, 300)
  }

  const removeFile = (fileId: string) => {
    setFiles((prev) => {
      const updatedFiles = prev.filter((f) => f.id !== fileId)

      // Revoke object URL to avoid memory leaks
      const fileToRemove = prev.find((f) => f.id === fileId)
      if (fileToRemove && fileToRemove.preview) {
        URL.revokeObjectURL(fileToRemove.preview)
      }

      return updatedFiles
    })
  }

  const triggerFileInput = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const getFileIcon = (file: UploadedFile) => {
    if (file.type === "image") {
      return <ImageIcon className="h-5 w-5 text-slate-400 mx-auto mb-4" />
    }

    return <File className="h-5 w-5 text-slate-400" />
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return "0 Bytes"

    const k = 1024
    const sizes = ["Bytes", "KB", "MB", "GB"]
    const i = Math.floor(Math.log(bytes) / Math.log(k))

    return Number.parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i]
  }

  return (
    <div className="min-h-screen bg-slate-50">
      <UnifiedNavigation />

      <div className="max-w-4xl mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-8">
          <Link href="/project/1">
            <Button variant="ghost" className="text-slate-500 hover:text-slate-700 p-2 mb-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Project
            </Button>
          </Link>

          <h1 className="text-3xl font-light text-slate-900 mb-2">Upload Files</h1>
          <p className="text-slate-500">Add photos and documents to your Kitchen Remodel project</p>
        </div>

        {/* Upload Area */}
        <Card className="bg-white border-0 shadow-sm mb-8">
          <div
            className={`p-8 border-2 border-dashed rounded-xl transition-colors ${
              isDragging ? "border-slate-400 bg-slate-50" : "border-slate-200"
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <div className="text-center">
              <Upload className="h-12 w-12 text-slate-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-slate-900 mb-2">Drag and drop files here</h3>
              <p className="text-slate-500 mb-6">Support for images, PDFs, and documents up to 25MB each</p>

              <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                multiple
                className="hidden"
                accept="image/*,.pdf,.doc,.docx,.xls,.xlsx"
              />

              <div className="flex flex-wrap justify-center gap-4">
                <Button onClick={triggerFileInput} className="bg-slate-900 hover:bg-slate-800 text-white">
                  <Upload className="h-4 w-4 mr-2" />
                  Browse Files
                </Button>

                <Button
                  variant="outline"
                  onClick={() => {
                    if (navigator.mediaDevices) {
                      // This would trigger the device camera in a real implementation
                      alert("Camera functionality would open here")
                    }
                  }}
                  className="bg-transparent border-slate-200 hover:border-slate-300"
                >
                  <Camera className="h-4 w-4 mr-2" />
                  Take Photo
                </Button>
              </div>
            </div>
          </div>
        </Card>

        {/* Uploaded Files */}
        {files.length > 0 && (
          <div className="space-y-6">
            <h2 className="text-lg font-medium text-slate-900">Uploaded Files ({files.length})</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {files.map((file) => (
                <Card key={file.id} className="p-4 bg-white border-0 shadow-sm">
                  <div className="flex items-start space-x-4">
                    {file.type === "image" && file.preview ? (
                      <div className="w-16 h-16 bg-slate-100 rounded-lg overflow-hidden flex-shrink-0">
                        <img
                          src={file.preview || "/placeholder.svg"}
                          alt={file.file.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    ) : (
                      <div className="w-16 h-16 bg-slate-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <File className="h-8 w-8 text-slate-400" />
                      </div>
                    )}

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="pr-2">
                          <p className="font-medium text-slate-900 truncate">{file.file.name}</p>
                          <p className="text-xs text-slate-500">
                            {formatFileSize(file.file.size)} • {file.file.type.split("/")[1].toUpperCase()}
                          </p>
                        </div>

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFile(file.id)}
                          className="text-slate-400 hover:text-red-600 p-1"
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </div>

                      <div className="mt-2">
                        {file.status === "uploading" ? (
                          <div className="space-y-1">
                            <div className="w-full bg-slate-100 rounded-full h-1.5">
                              <div
                                className="bg-slate-900 h-1.5 rounded-full transition-all duration-300"
                                style={{ width: `${file.progress}%` }}
                              />
                            </div>
                            <p className="text-xs text-slate-500">Uploading... {file.progress}%</p>
                          </div>
                        ) : file.status === "complete" ? (
                          <div className="flex items-center text-green-600 text-sm">
                            <Check className="h-4 w-4 mr-1" />
                            <span>Upload complete</span>
                          </div>
                        ) : (
                          <div className="text-red-600 text-sm">Upload failed</div>
                        )}
                      </div>
                    </div>
                  </div>
                </Card>
              ))}
            </div>

            {files.some((f) => f.status === "complete") && (
              <div className="flex justify-end">
                <Button className="bg-slate-900 hover:bg-slate-800 text-white">Done</Button>
              </div>
            )}
          </div>
        )}

        {/* Tips */}
        <div className="mt-12 bg-blue-50 border border-blue-100 rounded-xl p-6">
          <h3 className="font-medium text-slate-900 mb-3">Tips for Great Project Photos</h3>
          <ul className="space-y-2 text-slate-700">
            <li className="flex items-start">
              <Check className="h-4 w-4 text-blue-600 mt-1 mr-2" />
              <span>Take photos in good lighting to show accurate colors and details</span>
            </li>
            <li className="flex items-start">
              <Check className="h-4 w-4 text-blue-600 mt-1 mr-2" />
              <span>Include before, during, and after photos to document progress</span>
            </li>
            <li className="flex items-start">
              <Check className="h-4 w-4 text-blue-600 mt-1 mr-2" />
              <span>Capture wide shots of the space and close-ups of important details</span>
            </li>
            <li className="flex items-start">
              <Check className="h-4 w-4 text-blue-600 mt-1 mr-2" />
              <span>Add captions to your photos to provide context</span>
            </li>
          </ul>
        </div>
      </div>
    </div>
  )
}
