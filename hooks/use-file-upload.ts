"use client"

import { useState, useCallback, useRef } from 'react'
import { storageService, UploadOptions, UploadResult } from '@/services/storage'
import { useToast } from '@/hooks/use-toast'

export interface FileUploadState {
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'error'
  result?: UploadResult
  error?: string
}

export interface UseFileUploadOptions {
  maxFiles?: number
  maxFileSize?: number
  acceptedFileTypes?: string[]
  uploadOptions?: Partial<UploadOptions>
  onUploadComplete?: (results: UploadResult[]) => void
  onUploadError?: (error: string) => void
  onProgress?: (progress: number) => void
  autoUpload?: boolean
}

export interface UseFileUploadReturn {
  files: FileUploadState[]
  isUploading: boolean
  progress: number
  addFiles: (newFiles: File[]) => void
  removeFile: (index: number) => void
  uploadFiles: () => Promise<UploadResult[]>
  uploadSingleFile: (file: File, options?: Partial<UploadOptions>) => Promise<UploadResult | null>
  clearFiles: () => void
  retryUpload: (index: number) => Promise<void>
  cancelUpload: () => void
}

export function useFileUpload({
  maxFiles = 5,
  maxFileSize = 10 * 1024 * 1024, // 10MB
  acceptedFileTypes = ['image/*', 'application/pdf'],
  uploadOptions = {},
  onUploadComplete,
  onUploadError,
  onProgress,
  autoUpload = false
}: UseFileUploadOptions = {}): UseFileUploadReturn {
  const [files, setFiles] = useState<FileUploadState[]>([])
  const [isUploading, setIsUploading] = useState(false)
  const abortControllerRef = useRef<AbortController | null>(null)
  const { toast } = useToast()

  // Calculate overall progress
  const progress = files.length > 0 
    ? files.reduce((sum, file) => sum + file.progress, 0) / files.length 
    : 0

  // Validate file
  const validateFile = useCallback((file: File): string | null => {
    if (file.size > maxFileSize) {
      return `File size must be less than ${formatFileSize(maxFileSize)}`
    }

    const fileType = file.type
    const fileName = file.name.toLowerCase()
    const isValidType = acceptedFileTypes.some(type => {
      if (type.startsWith('.')) {
        return fileName.endsWith(type)
      }
      if (type.includes('*')) {
        const baseType = type.split('/')[0]
        return fileType.startsWith(baseType)
      }
      return fileType === type
    })

    if (!isValidType) {
      return `File type not supported. Accepted types: ${acceptedFileTypes.join(', ')}`
    }

    return null
  }, [maxFileSize, acceptedFileTypes])

  // Add files to upload queue
  const addFiles = useCallback((newFiles: File[]) => {
    const validFiles: File[] = []
    const errors: string[] = []

    // Check total file count
    if (files.length + newFiles.length > maxFiles) {
      const error = `Maximum ${maxFiles} files allowed`
      errors.push(error)
      if (onUploadError) onUploadError(error)
      toast({
        title: "Upload Error",
        description: error,
        variant: "destructive"
      })
      return
    }

    // Validate each file
    newFiles.forEach(file => {
      const error = validateFile(file)
      if (error) {
        errors.push(`${file.name}: ${error}`)
      } else {
        validFiles.push(file)
      }
    })

    if (errors.length > 0) {
      const errorMessage = errors.join(', ')
      if (onUploadError) onUploadError(errorMessage)
      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive"
      })
    }

    if (validFiles.length > 0) {
      const newFileStates = validFiles.map(file => ({
        file,
        progress: 0,
        status: 'pending' as const
      }))

      setFiles(prev => [...prev, ...newFileStates])

      // Auto upload if enabled
      if (autoUpload) {
        setTimeout(() => uploadFiles(), 100)
      }
    }
  }, [files.length, maxFiles, validateFile, onUploadError, autoUpload, toast])

  // Remove file from queue
  const removeFile = useCallback((index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index))
  }, [])

  // Upload single file with progress tracking
  const uploadSingleFile = useCallback(async (
    file: File, 
    options: Partial<UploadOptions> = {}
  ): Promise<UploadResult | null> => {
    const mergedOptions = {
      bucket: 'documents',
      folder: 'uploads',
      ...uploadOptions,
      ...options
    }

    try {
      // Create a mock progress for now (Supabase doesn't support upload progress)
      // In a real implementation, you might use XMLHttpRequest for progress tracking
      const result = await storageService.uploadFile(file, mergedOptions as UploadOptions)
      
      if (result.success && result.data) {
        return result.data
      } else {
        throw new Error(result.error || 'Upload failed')
      }
    } catch (error) {
      console.error('Upload error:', error)
      throw error
    }
  }, [uploadOptions])

  // Upload all files
  const uploadFiles = useCallback(async (): Promise<UploadResult[]> => {
    if (files.length === 0 || isUploading) return []

    setIsUploading(true)
    abortControllerRef.current = new AbortController()

    const results: UploadResult[] = []
    const errors: string[] = []

    try {
      for (let i = 0; i < files.length; i++) {
        const fileState = files[i]
        
        if (fileState.status === 'completed') {
          if (fileState.result) results.push(fileState.result)
          continue
        }

        // Update status to uploading
        setFiles(prev => prev.map((f, index) => 
          index === i ? { ...f, status: 'uploading' as const, progress: 0 } : f
        ))

        try {
          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setFiles(prev => prev.map((f, index) => 
              index === i && f.status === 'uploading' 
                ? { ...f, progress: Math.min(f.progress + 10, 90) } 
                : f
            ))
          }, 100)

          const result = await uploadSingleFile(fileState.file)

          clearInterval(progressInterval)

          if (result) {
            // Update to completed
            setFiles(prev => prev.map((f, index) => 
              index === i ? { 
                ...f, 
                status: 'completed' as const, 
                progress: 100, 
                result 
              } : f
            ))
            results.push(result)
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Upload failed'
          errors.push(`${fileState.file.name}: ${errorMessage}`)
          
          // Update to error
          setFiles(prev => prev.map((f, index) => 
            index === i ? { 
              ...f, 
              status: 'error' as const, 
              error: errorMessage 
            } : f
          ))
        }

        // Update overall progress
        if (onProgress) {
          onProgress((i + 1) / files.length * 100)
        }
      }

      if (results.length > 0 && onUploadComplete) {
        onUploadComplete(results)
      }

      if (errors.length > 0 && onUploadError) {
        onUploadError(errors.join(', '))
      }

      if (results.length > 0) {
        toast({
          title: "Upload Complete",
          description: `Successfully uploaded ${results.length} file(s)`
        })
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed'
      if (onUploadError) onUploadError(errorMessage)
      toast({
        title: "Upload Error",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsUploading(false)
      abortControllerRef.current = null
    }

    return results
  }, [files, isUploading, uploadSingleFile, onProgress, onUploadComplete, onUploadError, toast])

  // Retry failed upload
  const retryUpload = useCallback(async (index: number) => {
    const fileState = files[index]
    if (!fileState || fileState.status !== 'error') return

    setFiles(prev => prev.map((f, i) => 
      i === index ? { ...f, status: 'pending' as const, error: undefined } : f
    ))

    // Re-upload just this file
    await uploadFiles()
  }, [files, uploadFiles])

  // Cancel ongoing upload
  const cancelUpload = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    setIsUploading(false)
    setFiles(prev => prev.map(f => 
      f.status === 'uploading' ? { ...f, status: 'pending' as const } : f
    ))
  }, [])

  // Clear all files
  const clearFiles = useCallback(() => {
    if (isUploading) {
      cancelUpload()
    }
    setFiles([])
  }, [isUploading, cancelUpload])

  return {
    files,
    isUploading,
    progress,
    addFiles,
    removeFile,
    uploadFiles,
    uploadSingleFile,
    clearFiles,
    retryUpload,
    cancelUpload
  }
}

// Utility function
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
