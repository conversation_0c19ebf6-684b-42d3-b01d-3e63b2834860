"use client"

import { useState, useEffect, useCallback } from 'react'
import { useUser } from '@/contexts/user-context'
import { useToastActions } from '@/components/ui/toast-system'
import { reviewService, contractorService } from '@/services/database'
import type { Tables } from '@/lib/supabase'

export interface ReviewPhoto {
  id: string
  url: string
  caption?: string
  type: 'before' | 'after' | 'progress'
}

export interface Review {
  id: string
  project_id: string
  contractor_id: string
  customer_id: string
  rating: number
  title: string
  content: string
  photos: ReviewPhoto[]
  helpful_count: number
  response?: {
    content: string
    created_at: string
  }
  created_at: string
  updated_at: string
  verified: boolean
  project?: {
    id: string
    title: string
    category: string
  }
  customer?: {
    id: string
    name: string
    avatar_url?: string
  }
  contractor?: {
    id: string
    business_name: string
    users: {
      name: string
      avatar_url?: string
    }
  }
}

export interface UseReviewsOptions {
  contractorId?: string
  projectId?: string
  customerId?: string
  autoFetch?: boolean
}

export interface UseReviewsReturn {
  reviews: Review[]
  loading: boolean
  error: string | null
  averageRating: number
  totalReviews: number
  ratingDistribution: Record<number, number>
  createReview: (reviewData: Omit<Review, 'id' | 'created_at' | 'updated_at' | 'helpful_count' | 'verified'>) => Promise<boolean>
  updateReview: (reviewId: string, updates: Partial<Review>) => Promise<boolean>
  deleteReview: (reviewId: string) => Promise<boolean>
  addResponse: (reviewId: string, response: string) => Promise<boolean>
  markHelpful: (reviewId: string) => Promise<boolean>
  uploadPhotos: (files: File[]) => Promise<ReviewPhoto[]>
  refetch: () => Promise<void>
}

export function useReviews(options: UseReviewsOptions = {}): UseReviewsReturn {
  const { contractorId, projectId, customerId, autoFetch = true } = options
  const { user } = useUser()
  const { showError, showSuccess } = useToastActions()
  
  const [reviews, setReviews] = useState<Review[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch reviews
  const fetchReviews = useCallback(async () => {
    console.log('useReviews.fetchReviews - Starting fetch with:', { contractorId, projectId, customerId })
    setLoading(true)
    setError(null)

    try {
      let response
      if (contractorId) {
        console.log('useReviews.fetchReviews - Fetching by contractor ID:', contractorId)
        response = await reviewService.findByContractorId(contractorId)
      } else if (projectId) {
        console.log('useReviews.fetchReviews - Fetching by project ID:', projectId)
        response = await reviewService.findByProjectId(projectId)
      } else if (customerId) {
        console.log('useReviews.fetchReviews - Fetching by customer ID:', customerId)
        response = await reviewService.findByCustomerId(customerId)
      } else {
        console.log('useReviews.fetchReviews - Fetching all reviews')
        response = await reviewService.findMany()
      }

      console.log('useReviews.fetchReviews - Response:', response)

      if (response.success && response.data) {
        console.log('useReviews.fetchReviews - Success, setting reviews:', response.data.length)
        setReviews(response.data)
      } else {
        const errorMessage = response.error || 'Failed to fetch reviews'
        setError(errorMessage)
        console.error('Error in fetchReviews:', errorMessage)
      }
    } catch (err) {
      const errorMessage = 'An unexpected error occurred while fetching reviews'
      setError(errorMessage)
      console.error('Unexpected error in fetchReviews:', err)
    } finally {
      setLoading(false)
    }
  }, [contractorId, projectId, customerId])

  // Create review
  const createReview = useCallback(async (reviewData: Omit<Review, 'id' | 'created_at' | 'updated_at' | 'helpful_count' | 'verified'>): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to create a review')
      return false
    }

    try {
      const newReviewData = {
        ...reviewData,
        customer_id: user.id,
        helpful_count: 0,
        verified: false
      }

      const response = await reviewService.create(newReviewData)
      if (response.success && response.data) {
        setReviews(prev => [response.data!, ...prev])
        
        // Update contractor rating
        if (contractorId) {
          await contractorService.updateRating(contractorId, reviewData.rating)
        }
        
        showSuccess('Review created successfully')
        return true
      } else {
        showError(response.error || 'Failed to create review')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while creating review')
      return false
    }
  }, [user, contractorId, showError, showSuccess])

  // Update review
  const updateReview = useCallback(async (reviewId: string, updates: Partial<Review>): Promise<boolean> => {
    try {
      const response = await reviewService.update(reviewId, updates)
      if (response.success && response.data) {
        setReviews(prev => prev.map(r => r.id === reviewId ? { ...r, ...updates } : r))
        showSuccess('Review updated successfully')
        return true
      } else {
        showError(response.error || 'Failed to update review')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while updating review')
      return false
    }
  }, [showError, showSuccess])

  // Delete review
  const deleteReview = useCallback(async (reviewId: string): Promise<boolean> => {
    try {
      const response = await reviewService.delete(reviewId)
      if (response.success) {
        setReviews(prev => prev.filter(r => r.id !== reviewId))
        showSuccess('Review deleted successfully')
        return true
      } else {
        showError(response.error || 'Failed to delete review')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while deleting review')
      return false
    }
  }, [showError, showSuccess])

  // Add response to review
  const addResponse = useCallback(async (reviewId: string, responseContent: string): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to respond to reviews')
      return false
    }

    try {
      const response = {
        content: responseContent,
        created_at: new Date().toISOString()
      }

      const updateResponse = await reviewService.update(reviewId, { response })
      if (updateResponse.success) {
        setReviews(prev => prev.map(r => 
          r.id === reviewId ? { ...r, response } : r
        ))
        showSuccess('Response added successfully')
        return true
      } else {
        showError(updateResponse.error || 'Failed to add response')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while adding response')
      return false
    }
  }, [user, showError, showSuccess])

  // Mark review as helpful
  const markHelpful = useCallback(async (reviewId: string): Promise<boolean> => {
    try {
      const review = reviews.find(r => r.id === reviewId)
      if (!review) return false

      const updates = { helpful_count: review.helpful_count + 1 }
      const response = await reviewService.update(reviewId, updates)
      
      if (response.success) {
        setReviews(prev => prev.map(r => 
          r.id === reviewId ? { ...r, helpful_count: r.helpful_count + 1 } : r
        ))
        showSuccess('Marked as helpful')
        return true
      } else {
        showError(response.error || 'Failed to mark as helpful')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred')
      return false
    }
  }, [reviews, showError, showSuccess])

  // Upload photos
  const uploadPhotos = useCallback(async (files: File[]): Promise<ReviewPhoto[]> => {
    if (!user) {
      showError('You must be logged in to upload photos')
      return []
    }

    try {
      const uploadedPhotos: ReviewPhoto[] = []
      
      for (const file of files) {
        // In a real implementation, this would upload to Supabase Storage
        const photoId = `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        const photoUrl = URL.createObjectURL(file) // Temporary URL for demo
        
        uploadedPhotos.push({
          id: photoId,
          url: photoUrl,
          type: 'after' // Default type, can be changed by user
        })
      }
      
      return uploadedPhotos
    } catch (err) {
      showError('Failed to upload photos')
      return []
    }
  }, [user, showError])

  // Calculate statistics
  const averageRating = reviews.length > 0 
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length 
    : 0

  const totalReviews = reviews.length

  const ratingDistribution = reviews.reduce((dist, review) => {
    dist[review.rating] = (dist[review.rating] || 0) + 1
    return dist
  }, {} as Record<number, number>)

  // Refetch data
  const refetch = useCallback(async () => {
    await fetchReviews()
  }, [fetchReviews])

  // Initialize
  useEffect(() => {
    if (autoFetch) {
      fetchReviews()
    }
  }, [autoFetch, fetchReviews])

  return {
    reviews,
    loading,
    error,
    averageRating,
    totalReviews,
    ratingDistribution,
    createReview,
    updateReview,
    deleteReview,
    addResponse,
    markHelpful,
    uploadPhotos,
    refetch
  }
}
