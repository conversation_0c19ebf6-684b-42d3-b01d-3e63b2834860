"use client"

import { useEffect, useRef, useCallback } from 'react'
import { useUser } from '@/contexts/user-context'
import { messagingService } from '@/services/messaging'
import type { MessageData, ConversationData } from '@/services/messaging'

export interface UseRealtimeMessagingOptions {
  conversationId?: string
  onNewMessage?: (message: MessageData) => void
  onConversationUpdate?: (conversation: ConversationData) => void
  onTypingStart?: (userId: string) => void
  onTypingStop?: (userId: string) => void
  onUserOnline?: (userId: string) => void
  onUserOffline?: (userId: string) => void
}

export interface UseRealtimeMessagingReturn {
  isConnected: boolean
  sendTypingIndicator: (isTyping: boolean) => void
  updateOnlineStatus: (isOnline: boolean) => void
  reconnect: () => void
  disconnect: () => void
}

export function useRealtimeMessaging({
  conversationId,
  onNewMessage,
  onConversationUpdate,
  onTypingStart,
  onTypingStop,
  onUserOnline,
  onUserOffline
}: UseRealtimeMessagingOptions = {}): UseRealtimeMessagingReturn {
  const { user } = useUser()
  const subscriptionsRef = useRef<Array<() => void>>([])
  const isConnectedRef = useRef(false)
  const typingTimeoutRef = useRef<NodeJS.Timeout>()

  // Clean up subscriptions
  const cleanup = useCallback(() => {
    subscriptionsRef.current.forEach(unsubscribe => {
      try {
        unsubscribe()
      } catch (error) {
        console.error('Error unsubscribing:', error)
      }
    })
    subscriptionsRef.current = []
    isConnectedRef.current = false
  }, [])

  // Set up conversation-specific subscriptions
  const setupConversationSubscriptions = useCallback(() => {
    if (!conversationId || !user) return

    try {
      // Subscribe to new messages in this conversation
      const messageUnsubscribe = messagingService.subscribeToConversation(
        conversationId,
        (message: MessageData) => {
          // Don't notify for own messages
          if (message.sender_id !== user.id && onNewMessage) {
            onNewMessage(message)
          }
        }
      )

      subscriptionsRef.current.push(messageUnsubscribe)
      isConnectedRef.current = true
    } catch (error) {
      console.error('Error setting up conversation subscriptions:', error)
      isConnectedRef.current = false
    }
  }, [conversationId, user, onNewMessage])

  // Set up user-level subscriptions
  const setupUserSubscriptions = useCallback(() => {
    if (!user) return

    try {
      // Subscribe to conversation updates for this user
      const conversationUnsubscribe = messagingService.subscribeToUserConversations(
        user.id,
        (conversation: ConversationData) => {
          if (onConversationUpdate) {
            onConversationUpdate(conversation)
          }
        }
      )

      subscriptionsRef.current.push(conversationUnsubscribe)
    } catch (error) {
      console.error('Error setting up user subscriptions:', error)
    }
  }, [user, onConversationUpdate])

  // Send typing indicator
  const sendTypingIndicator = useCallback((isTyping: boolean) => {
    if (!conversationId || !user) return

    try {
      // Clear existing timeout
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }

      if (isTyping) {
        // Broadcast typing start
        messagingService.broadcastTyping(conversationId, user.id, true)
        
        // Auto-stop typing after 3 seconds
        typingTimeoutRef.current = setTimeout(() => {
          messagingService.broadcastTyping(conversationId, user.id, false)
        }, 3000)
      } else {
        // Broadcast typing stop
        messagingService.broadcastTyping(conversationId, user.id, false)
      }
    } catch (error) {
      console.error('Error sending typing indicator:', error)
    }
  }, [conversationId, user])

  // Update online status
  const updateOnlineStatus = useCallback((isOnline: boolean) => {
    if (!user) return

    try {
      messagingService.updateUserPresence(user.id, isOnline)
    } catch (error) {
      console.error('Error updating online status:', error)
    }
  }, [user])

  // Reconnect to real-time subscriptions
  const reconnect = useCallback(() => {
    cleanup()
    setupUserSubscriptions()
    setupConversationSubscriptions()
  }, [cleanup, setupUserSubscriptions, setupConversationSubscriptions])

  // Disconnect from real-time subscriptions
  const disconnect = useCallback(() => {
    cleanup()
  }, [cleanup])

  // Set up subscriptions when dependencies change
  useEffect(() => {
    if (!user) return

    setupUserSubscriptions()
    setupConversationSubscriptions()

    // Set user as online when component mounts
    updateOnlineStatus(true)

    return () => {
      // Set user as offline when component unmounts
      updateOnlineStatus(false)
      cleanup()
    }
  }, [user, conversationId, setupUserSubscriptions, setupConversationSubscriptions, updateOnlineStatus, cleanup])

  // Handle page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.hidden) {
        updateOnlineStatus(false)
      } else {
        updateOnlineStatus(true)
        // Reconnect when page becomes visible again
        reconnect()
      }
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [updateOnlineStatus, reconnect])

  // Handle beforeunload to set user offline
  useEffect(() => {
    const handleBeforeUnload = () => {
      updateOnlineStatus(false)
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [updateOnlineStatus])

  // Clean up typing timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current)
      }
    }
  }, [])

  return {
    isConnected: isConnectedRef.current,
    sendTypingIndicator,
    updateOnlineStatus,
    reconnect,
    disconnect
  }
}
