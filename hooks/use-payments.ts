"use client"

import { useState, useEffect, useCallback } from 'react'
import { paymentService } from '@/services/database'
import { useUser } from '@/contexts/user-context'
import { useToastActions } from '@/components/ui/toast-system'
import type { Tables } from '@/lib/supabase'

export interface UsePaymentsOptions {
  autoFetch?: boolean
  projectId?: string
  status?: string
}

export interface UsePaymentsReturn {
  payments: Tables<'payments'>[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createPayment: (paymentData: any) => Promise<Tables<'payments'> | null>
  updatePaymentStatus: (paymentId: string, status: string) => Promise<boolean>
  processPayment: (paymentId: string) => Promise<boolean>
  downloadReceipt: (paymentId: string) => Promise<string | null>
}

export function usePayments(options: UsePaymentsOptions = {}): UsePaymentsReturn {
  const { autoFetch = true, projectId, status } = options
  const { user } = useUser()
  const { showError, showSuccess } = useToastActions()
  
  const [payments, setPayments] = useState<Tables<'payments'>[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const refetch = useCallback(async () => {
    if (!user) return

    setLoading(true)
    setError(null)
    
    try {
      // For now, we'll use the existing findByUserId method
      // In a real implementation, you might want more specific filtering
      const userPayments = await paymentService.findByUserId(user.id)
      
      let filteredPayments = userPayments || []
      
      // Apply filters
      if (projectId) {
        filteredPayments = filteredPayments.filter(p => p.project_id === projectId)
      }
      
      if (status && status !== 'all') {
        filteredPayments = filteredPayments.filter(p => p.status === status)
      }
      
      setPayments(filteredPayments)
    } catch (err) {
      console.error('Error fetching payments:', err)
      setError('Failed to fetch payments')
      setPayments([])
    } finally {
      setLoading(false)
    }
  }, [user, projectId, status])

  useEffect(() => {
    if (autoFetch && user) {
      refetch()
    }
  }, [autoFetch, user, refetch])

  const createPayment = useCallback(async (paymentData: any): Promise<Tables<'payments'> | null> => {
    if (!user) {
      showError('You must be logged in to create payments')
      return null
    }

    setLoading(true)
    try {
      const response = await paymentService.create({
        ...paymentData,
        payer_id: user.id,
        status: 'pending',
        created_at: new Date().toISOString()
      })

      if (response.success && response.data) {
        setPayments(prev => [response.data!, ...prev])
        showSuccess('Payment created successfully')
        return response.data
      } else {
        showError(response.error || 'Failed to create payment')
        return null
      }
    } catch (err) {
      console.error('Error creating payment:', err)
      showError('An unexpected error occurred while creating the payment')
      return null
    } finally {
      setLoading(false)
    }
  }, [user, showError, showSuccess])

  const updatePaymentStatus = useCallback(async (paymentId: string, status: string): Promise<boolean> => {
    setLoading(true)
    try {
      const response = await paymentService.update(paymentId, { 
        status,
        updated_at: new Date().toISOString()
      })

      if (response.success) {
        setPayments(prev => prev.map(p => 
          p.id === paymentId ? { ...p, status } : p
        ))
        showSuccess(`Payment ${status} successfully`)
        return true
      } else {
        showError(response.error || 'Failed to update payment status')
        return false
      }
    } catch (err) {
      console.error('Error updating payment status:', err)
      showError('An unexpected error occurred while updating payment status')
      return false
    } finally {
      setLoading(false)
    }
  }, [showError, showSuccess])

  const processPayment = useCallback(async (paymentId: string): Promise<boolean> => {
    setLoading(true)
    try {
      // In a real implementation, this would integrate with a payment processor
      // For now, we'll just update the status to processing then completed
      
      // First set to processing
      await updatePaymentStatus(paymentId, 'processing')
      
      // Simulate payment processing delay
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Then set to completed
      const success = await updatePaymentStatus(paymentId, 'completed')
      
      if (success) {
        showSuccess('Payment processed successfully')
      }
      
      return success
    } catch (err) {
      console.error('Error processing payment:', err)
      showError('Payment processing failed')
      return false
    } finally {
      setLoading(false)
    }
  }, [updatePaymentStatus, showError, showSuccess])

  const downloadReceipt = useCallback(async (paymentId: string): Promise<string | null> => {
    try {
      // In a real implementation, this would generate and download a receipt
      // For now, we'll just return a mock URL
      const payment = payments.find(p => p.id === paymentId)
      if (!payment) {
        showError('Payment not found')
        return null
      }

      // Mock receipt generation
      const receiptUrl = `/api/payments/${paymentId}/receipt`
      showSuccess('Receipt downloaded successfully')
      return receiptUrl
    } catch (err) {
      console.error('Error downloading receipt:', err)
      showError('Failed to download receipt')
      return null
    }
  }, [payments, showError, showSuccess])

  return {
    payments,
    loading,
    error,
    refetch,
    createPayment,
    updatePaymentStatus,
    processPayment,
    downloadReceipt
  }
}
