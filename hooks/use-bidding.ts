"use client"

import { useState, useEffect, useCallback } from 'react'
import { biddingService, type BidData } from '@/services/bidding'
import { bidService } from '@/services/database'
import { useUser } from '@/contexts/user-context'
import { useToastActions } from '@/components/ui/toast-system'
import type { Tables } from '@/lib/supabase'

export interface UseBiddingOptions {
  autoFetch?: boolean
  projectId?: string
  contractorId?: string
}

export interface UseBiddingReturn {
  bids: Tables<'bids'>[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
  createBid: (bidData: Omit<BidData, 'id' | 'submitted_at' | 'status'>) => Promise<Tables<'bids'> | null>
  updateBid: (bidId: string, updates: Partial<BidData>) => Promise<Tables<'bids'> | null>
  withdrawBid: (bidId: string) => Promise<boolean>
  acceptBid: (bidId: string) => Promise<boolean>
  rejectBid: (bidId: string) => Promise<boolean>
  getProjectBids: (projectId: string) => Promise<Tables<'bids'>[]>
  getContractorBids: (contractorId: string) => Promise<Tables<'bids'>[]>
}

export function useBidding(options: UseBiddingOptions = {}): UseBiddingReturn {
  const { autoFetch = true, projectId, contractorId } = options
  const { user } = useUser()
  const { showError, showSuccess } = useToastActions()
  
  const [bids, setBids] = useState<Tables<'bids'>[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const refetch = useCallback(async () => {
    if (!user) return

    setLoading(true)
    setError(null)
    
    try {
      let response
      
      if (projectId) {
        // Fetch bids for a specific project
        response = await bidService.findByProjectId(projectId)
      } else if (contractorId) {
        // Fetch bids for a specific contractor
        response = await bidService.findByContractorId(contractorId)
      } else if (user.role === 'contractor') {
        // Fetch all bids for the current contractor
        response = await bidService.findByContractorId(user.id)
      } else {
        // For customers, we might want to fetch bids for their projects
        setBids([])
        return
      }

      if (response.success && response.data) {
        setBids(Array.isArray(response.data) ? response.data : [response.data])
      } else {
        setError(response.error || 'Failed to fetch bids')
        setBids([])
      }
    } catch (err) {
      console.error('Error fetching bids:', err)
      setError('An unexpected error occurred while fetching bids')
      setBids([])
    } finally {
      setLoading(false)
    }
  }, [user, projectId, contractorId])

  useEffect(() => {
    if (autoFetch && user) {
      refetch()
    }
  }, [autoFetch, user, refetch])

  const createBid = useCallback(async (bidData: Omit<BidData, 'id' | 'submitted_at' | 'status'>): Promise<Tables<'bids'> | null> => {
    if (!user || user.role !== 'contractor') {
      showError('You must be logged in as a contractor to submit bids')
      return null
    }

    setLoading(true)
    try {
      const response = await biddingService.createBid({
        ...bidData,
        contractor_id: user.id
      })

      if (response.success && response.data) {
        setBids(prev => [response.data!, ...prev])
        showSuccess('Bid submitted successfully!')
        return response.data as Tables<'bids'>
      } else {
        showError(response.error || 'Failed to submit bid')
        return null
      }
    } catch (err) {
      console.error('Error creating bid:', err)
      showError('An unexpected error occurred while submitting the bid')
      return null
    } finally {
      setLoading(false)
    }
  }, [user, showError, showSuccess])

  const updateBid = useCallback(async (bidId: string, updates: Partial<BidData>): Promise<Tables<'bids'> | null> => {
    setLoading(true)
    try {
      const response = await biddingService.updateBid(bidId, updates)

      if (response.success && response.data) {
        setBids(prev => prev.map(b => b.id === bidId ? response.data! : b))
        showSuccess('Bid updated successfully')
        return response.data as Tables<'bids'>
      } else {
        showError(response.error || 'Failed to update bid')
        return null
      }
    } catch (err) {
      console.error('Error updating bid:', err)
      showError('An unexpected error occurred while updating the bid')
      return null
    } finally {
      setLoading(false)
    }
  }, [showError, showSuccess])

  const withdrawBid = useCallback(async (bidId: string): Promise<boolean> => {
    setLoading(true)
    try {
      const response = await biddingService.withdrawBid(bidId)

      if (response.success) {
        setBids(prev => prev.map(b => b.id === bidId ? { ...b, status: 'withdrawn' } : b))
        showSuccess('Bid withdrawn successfully')
        return true
      } else {
        showError(response.error || 'Failed to withdraw bid')
        return false
      }
    } catch (err) {
      console.error('Error withdrawing bid:', err)
      showError('An unexpected error occurred while withdrawing the bid')
      return false
    } finally {
      setLoading(false)
    }
  }, [showError, showSuccess])

  const acceptBid = useCallback(async (bidId: string): Promise<boolean> => {
    setLoading(true)
    try {
      const response = await biddingService.acceptBid(bidId)

      if (response.success) {
        setBids(prev => prev.map(b => b.id === bidId ? { ...b, status: 'accepted' } : b))
        showSuccess('Bid accepted successfully')
        return true
      } else {
        showError(response.error || 'Failed to accept bid')
        return false
      }
    } catch (err) {
      console.error('Error accepting bid:', err)
      showError('An unexpected error occurred while accepting the bid')
      return false
    } finally {
      setLoading(false)
    }
  }, [showError, showSuccess])

  const rejectBid = useCallback(async (bidId: string): Promise<boolean> => {
    setLoading(true)
    try {
      const response = await biddingService.rejectBid(bidId)

      if (response.success) {
        setBids(prev => prev.map(b => b.id === bidId ? { ...b, status: 'rejected' } : b))
        showSuccess('Bid rejected')
        return true
      } else {
        showError(response.error || 'Failed to reject bid')
        return false
      }
    } catch (err) {
      console.error('Error rejecting bid:', err)
      showError('An unexpected error occurred while rejecting the bid')
      return false
    } finally {
      setLoading(false)
    }
  }, [showError, showSuccess])

  const getProjectBids = useCallback(async (projectId: string): Promise<Tables<'bids'>[]> => {
    try {
      const response = await bidService.findByProjectId(projectId)
      if (response.success && response.data) {
        return Array.isArray(response.data) ? response.data : [response.data]
      }
      return []
    } catch (err) {
      console.error('Error fetching project bids:', err)
      return []
    }
  }, [])

  const getContractorBids = useCallback(async (contractorId: string): Promise<Tables<'bids'>[]> => {
    try {
      const response = await bidService.findByContractorId(contractorId)
      if (response.success && response.data) {
        return Array.isArray(response.data) ? response.data : [response.data]
      }
      return []
    } catch (err) {
      console.error('Error fetching contractor bids:', err)
      return []
    }
  }, [])

  return {
    bids,
    loading,
    error,
    refetch,
    createBid,
    updateBid,
    withdrawBid,
    acceptBid,
    rejectBid,
    getProjectBids,
    getContractorBids
  }
}
