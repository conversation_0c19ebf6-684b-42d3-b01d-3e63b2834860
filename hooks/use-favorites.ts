import { useState, useEffect, useCallback } from 'react'
import { favoritesService } from '@/services/favorites'
import { useUser } from '@/contexts/user-context'

export interface UseFavoritesOptions {
  itemId: string
  itemType: 'contractor' | 'project'
  enableOffline?: boolean
}

export interface UseLikesOptions {
  itemId: string
  itemType: 'contractor' | 'project' | 'review'
  enableOffline?: boolean
}

export function useFavorites({ itemId, itemType, enableOffline = true }: UseFavoritesOptions) {
  const { user, isAuthenticated } = useUser()
  const [isFavorite, setIsFavorite] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load initial favorite status
  useEffect(() => {
    const loadFavoriteStatus = async () => {
      if (!itemId) return

      setIsLoading(true)
      setError(null)

      try {
        if (isAuthenticated) {
          const response = await favoritesService.isFavorite(itemId, itemType)
          if (response.success) {
            setIsFavorite(response.data)
          } else {
            throw new Error(response.error || 'Failed to load favorite status')
          }
        } else if (enableOffline) {
          // Use local storage for non-authenticated users
          setIsFavorite(favoritesService.isFavoriteLocal(itemId))
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        // Fallback to local storage on error
        if (enableOffline) {
          setIsFavorite(favoritesService.isFavoriteLocal(itemId))
        }
      } finally {
        setIsLoading(false)
      }
    }

    loadFavoriteStatus()
  }, [itemId, itemType, isAuthenticated, enableOffline])

  const toggleFavorite = useCallback(async () => {
    if (!itemId) return

    setIsLoading(true)
    setError(null)

    try {
      if (isAuthenticated) {
        if (isFavorite) {
          const response = await favoritesService.removeFavorite(itemId, itemType)
          if (response.success) {
            setIsFavorite(false)
          } else {
            throw new Error(response.error || 'Failed to remove favorite')
          }
        } else {
          const response = await favoritesService.addFavorite(itemId, itemType)
          if (response.success) {
            setIsFavorite(true)
          } else {
            throw new Error(response.error || 'Failed to add favorite')
          }
        }
      } else if (enableOffline) {
        // Use local storage for non-authenticated users
        const newStatus = favoritesService.toggleFavoriteLocal(itemId)
        setIsFavorite(newStatus)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      // Fallback to local storage on error
      if (enableOffline) {
        const newStatus = favoritesService.toggleFavoriteLocal(itemId)
        setIsFavorite(newStatus)
      }
    } finally {
      setIsLoading(false)
    }
  }, [itemId, itemType, isFavorite, isAuthenticated, enableOffline])

  return {
    isFavorite,
    isLoading,
    error,
    toggleFavorite
  }
}

export function useLikes({ itemId, itemType, enableOffline = true }: UseLikesOptions) {
  const { user, isAuthenticated } = useUser()
  const [isLiked, setIsLiked] = useState(false)
  const [likeCount, setLikeCount] = useState(0)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Load initial like status and count
  useEffect(() => {
    const loadLikeStatus = async () => {
      if (!itemId) return

      setIsLoading(true)
      setError(null)

      try {
        if (isAuthenticated) {
          const [statusResponse, countResponse] = await Promise.all([
            favoritesService.isLiked(itemId, itemType),
            favoritesService.getLikeCount(itemId, itemType)
          ])

          if (statusResponse.success && countResponse.success) {
            setIsLiked(statusResponse.data)
            setLikeCount(countResponse.data)
          } else {
            throw new Error('Failed to load like status')
          }
        } else if (enableOffline) {
          // Use local storage for non-authenticated users
          setIsLiked(favoritesService.isLikedLocal(itemId))
          // For offline mode, we can't get accurate count, so we'll show 0
          setLikeCount(0)
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        // Fallback to local storage on error
        if (enableOffline) {
          setIsLiked(favoritesService.isLikedLocal(itemId))
          setLikeCount(0)
        }
      } finally {
        setIsLoading(false)
      }
    }

    loadLikeStatus()
  }, [itemId, itemType, isAuthenticated, enableOffline])

  const toggleLike = useCallback(async () => {
    if (!itemId) return

    setIsLoading(true)
    setError(null)

    try {
      if (isAuthenticated) {
        if (isLiked) {
          const response = await favoritesService.removeLike(itemId, itemType)
          if (response.success) {
            setIsLiked(false)
            setLikeCount(prev => Math.max(0, prev - 1))
          } else {
            throw new Error(response.error || 'Failed to remove like')
          }
        } else {
          const response = await favoritesService.addLike(itemId, itemType)
          if (response.success) {
            setIsLiked(true)
            setLikeCount(prev => prev + 1)
          } else {
            throw new Error(response.error || 'Failed to add like')
          }
        }
      } else if (enableOffline) {
        // Use local storage for non-authenticated users
        const newStatus = favoritesService.toggleLikeLocal(itemId)
        setIsLiked(newStatus)
        // For offline mode, we'll just toggle the visual state
        setLikeCount(prev => newStatus ? prev + 1 : Math.max(0, prev - 1))
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error')
      // Fallback to local storage on error
      if (enableOffline) {
        const newStatus = favoritesService.toggleLikeLocal(itemId)
        setIsLiked(newStatus)
        setLikeCount(prev => newStatus ? prev + 1 : Math.max(0, prev - 1))
      }
    } finally {
      setIsLoading(false)
    }
  }, [itemId, itemType, isLiked, isAuthenticated, enableOffline])

  return {
    isLiked,
    likeCount,
    isLoading,
    error,
    toggleLike
  }
}

// Hook for managing multiple favorites (e.g., in a list)
export function useBulkFavorites(items: Array<{id: string, type: 'contractor' | 'project'}>) {
  const { isAuthenticated } = useUser()
  const [favoriteStatus, setFavoriteStatus] = useState<Record<string, boolean>>({})
  const [isLoading, setIsLoading] = useState(false)

  useEffect(() => {
    const loadBulkStatus = async () => {
      if (!items.length) return

      setIsLoading(true)
      try {
        if (isAuthenticated) {
          const response = await favoritesService.getFavoriteStatus(items)
          if (response.success) {
            setFavoriteStatus(response.data)
          }
        } else {
          // Use local storage for non-authenticated users
          const status: Record<string, boolean> = {}
          items.forEach(item => {
            status[item.id] = favoritesService.isFavoriteLocal(item.id)
          })
          setFavoriteStatus(status)
        }
      } catch (error) {
        console.error('Failed to load bulk favorite status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadBulkStatus()
  }, [items, isAuthenticated])

  const toggleFavorite = useCallback(async (itemId: string, itemType: 'contractor' | 'project') => {
    const currentStatus = favoriteStatus[itemId] || false
    
    // Optimistically update UI
    setFavoriteStatus(prev => ({
      ...prev,
      [itemId]: !currentStatus
    }))

    try {
      if (isAuthenticated) {
        if (currentStatus) {
          await favoritesService.removeFavorite(itemId, itemType)
        } else {
          await favoritesService.addFavorite(itemId, itemType)
        }
      } else {
        favoritesService.toggleFavoriteLocal(itemId)
      }
    } catch (error) {
      // Revert on error
      setFavoriteStatus(prev => ({
        ...prev,
        [itemId]: currentStatus
      }))
      console.error('Failed to toggle favorite:', error)
    }
  }, [favoriteStatus, isAuthenticated])

  return {
    favoriteStatus,
    isLoading,
    toggleFavorite
  }
}
