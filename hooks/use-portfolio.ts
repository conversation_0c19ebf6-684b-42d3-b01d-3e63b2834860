"use client"

import { useState, useEffect, useCallback } from 'react'
import { useUser } from '@/contexts/user-context'
import { useToast } from '@/components/ui/toast-system'
import { contractorService } from '@/services/database'
import type { Tables } from '@/lib/supabase'

export interface PortfolioItem {
  id: string
  title: string
  description: string
  category: string
  images: Array<{
    id: string
    url: string
    caption?: string
    type: 'before' | 'after' | 'progress'
  }>
  project_details: {
    duration: string
    budget_range: string
    client_location: string
    completion_date: string
  }
  tags: string[]
  featured: boolean
  visible: boolean
  created_at: string
  updated_at: string
}

export interface Schedule {
  id: string
  title: string
  type: 'project' | 'consultation' | 'estimate' | 'personal'
  start_date: string
  end_date: string
  all_day: boolean
  location?: string
  client_name?: string
  project_id?: string
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled'
  notes?: string
  reminders: Array<{
    type: 'email' | 'sms' | 'push'
    minutes_before: number
  }>
}

export interface UsePortfolioOptions {
  contractorId?: string
  autoFetch?: boolean
}

export interface UsePortfolioReturn {
  portfolioItems: PortfolioItem[]
  schedule: Schedule[]
  loading: boolean
  error: string | null
  addPortfolioItem: (item: Omit<PortfolioItem, 'id' | 'created_at' | 'updated_at'>) => Promise<boolean>
  updatePortfolioItem: (itemId: string, updates: Partial<PortfolioItem>) => Promise<boolean>
  deletePortfolioItem: (itemId: string) => Promise<boolean>
  reorderPortfolio: (itemIds: string[]) => Promise<boolean>
  uploadImages: (files: File[]) => Promise<Array<{ id: string; url: string }>>
  addScheduleEvent: (event: Omit<Schedule, 'id'>) => Promise<boolean>
  updateScheduleEvent: (eventId: string, updates: Partial<Schedule>) => Promise<boolean>
  deleteScheduleEvent: (eventId: string) => Promise<boolean>
  getUpcomingEvents: (days?: number) => Schedule[]
  refetch: () => Promise<void>
}

export function usePortfolio(options: UsePortfolioOptions = {}): UsePortfolioReturn {
  const { contractorId, autoFetch = true } = options
  const { user } = useUser()
  const { error: showError, success: showSuccess } = useToast()
  
  const [portfolioItems, setPortfolioItems] = useState<PortfolioItem[]>([])
  const [schedule, setSchedule] = useState<Schedule[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch portfolio and schedule data
  const fetchData = useCallback(async () => {
    const targetContractorId = contractorId || user?.id
    if (!targetContractorId) return

    setLoading(true)
    setError(null)

    try {
      // Fetch contractor data with portfolio and schedule
      const response = await contractorService.findWithUserData(targetContractorId)
      if (response.success && response.data) {
        const contractorData = response.data
        
        // Parse portfolio items
        const portfolio = contractorData.portfolio || []
        setPortfolioItems(portfolio.sort((a: PortfolioItem, b: PortfolioItem) => 
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        ))
        
        // Parse schedule
        const scheduleData = contractorData.schedule || []
        setSchedule(scheduleData.sort((a: Schedule, b: Schedule) => 
          new Date(a.start_date).getTime() - new Date(b.start_date).getTime()
        ))
      } else {
        setError(response.error || 'Failed to fetch portfolio data')
      }
    } catch (err) {
      setError('An unexpected error occurred while fetching portfolio data')
    } finally {
      setLoading(false)
    }
  }, [contractorId, user])

  // Add portfolio item
  const addPortfolioItem = useCallback(async (item: Omit<PortfolioItem, 'id' | 'created_at' | 'updated_at'>): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to add portfolio items')
      return false
    }

    try {
      const newItem: PortfolioItem = {
        ...item,
        id: `portfolio_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      const updatedPortfolio = [newItem, ...portfolioItems]
      
      // Update contractor record
      const response = await contractorService.update(user.id, {
        portfolio: updatedPortfolio
      })

      if (response.success) {
        setPortfolioItems(updatedPortfolio)
        showSuccess('Portfolio item added successfully')
        return true
      } else {
        showError(response.error || 'Failed to add portfolio item')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while adding portfolio item')
      return false
    }
  }, [user, portfolioItems, showError, showSuccess])

  // Update portfolio item
  const updatePortfolioItem = useCallback(async (itemId: string, updates: Partial<PortfolioItem>): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to update portfolio items')
      return false
    }

    try {
      const updatedPortfolio = portfolioItems.map(item =>
        item.id === itemId 
          ? { ...item, ...updates, updated_at: new Date().toISOString() }
          : item
      )

      const response = await contractorService.update(user.id, {
        portfolio: updatedPortfolio
      })

      if (response.success) {
        setPortfolioItems(updatedPortfolio)
        showSuccess('Portfolio item updated successfully')
        return true
      } else {
        showError(response.error || 'Failed to update portfolio item')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while updating portfolio item')
      return false
    }
  }, [user, portfolioItems, showError, showSuccess])

  // Delete portfolio item
  const deletePortfolioItem = useCallback(async (itemId: string): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to delete portfolio items')
      return false
    }

    try {
      const updatedPortfolio = portfolioItems.filter(item => item.id !== itemId)

      const response = await contractorService.update(user.id, {
        portfolio: updatedPortfolio
      })

      if (response.success) {
        setPortfolioItems(updatedPortfolio)
        showSuccess('Portfolio item deleted successfully')
        return true
      } else {
        showError(response.error || 'Failed to delete portfolio item')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while deleting portfolio item')
      return false
    }
  }, [user, portfolioItems, showError, showSuccess])

  // Reorder portfolio
  const reorderPortfolio = useCallback(async (itemIds: string[]): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to reorder portfolio')
      return false
    }

    try {
      const reorderedItems = itemIds.map(id => 
        portfolioItems.find(item => item.id === id)
      ).filter(Boolean) as PortfolioItem[]

      const response = await contractorService.update(user.id, {
        portfolio: reorderedItems
      })

      if (response.success) {
        setPortfolioItems(reorderedItems)
        showSuccess('Portfolio reordered successfully')
        return true
      } else {
        showError(response.error || 'Failed to reorder portfolio')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while reordering portfolio')
      return false
    }
  }, [user, portfolioItems, showError, showSuccess])

  // Upload images
  const uploadImages = useCallback(async (files: File[]): Promise<Array<{ id: string; url: string }>> => {
    if (!user) {
      showError('You must be logged in to upload images')
      return []
    }

    try {
      const uploadedImages = []
      
      for (const file of files) {
        // In a real implementation, this would upload to Supabase Storage
        const imageId = `img_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        const imageUrl = URL.createObjectURL(file) // Temporary URL for demo
        
        uploadedImages.push({
          id: imageId,
          url: imageUrl
        })
      }
      
      return uploadedImages
    } catch (err) {
      showError('Failed to upload images')
      return []
    }
  }, [user, showError])

  // Add schedule event
  const addScheduleEvent = useCallback(async (event: Omit<Schedule, 'id'>): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to add schedule events')
      return false
    }

    try {
      const newEvent: Schedule = {
        ...event,
        id: `event_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }

      const updatedSchedule = [...schedule, newEvent].sort((a, b) => 
        new Date(a.start_date).getTime() - new Date(b.start_date).getTime()
      )

      const response = await contractorService.update(user.id, {
        schedule: updatedSchedule
      })

      if (response.success) {
        setSchedule(updatedSchedule)
        showSuccess('Event added to schedule')
        return true
      } else {
        showError(response.error || 'Failed to add schedule event')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while adding schedule event')
      return false
    }
  }, [user, schedule, showError, showSuccess])

  // Update schedule event
  const updateScheduleEvent = useCallback(async (eventId: string, updates: Partial<Schedule>): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to update schedule events')
      return false
    }

    try {
      const updatedSchedule = schedule.map(event =>
        event.id === eventId ? { ...event, ...updates } : event
      ).sort((a, b) => new Date(a.start_date).getTime() - new Date(b.start_date).getTime())

      const response = await contractorService.update(user.id, {
        schedule: updatedSchedule
      })

      if (response.success) {
        setSchedule(updatedSchedule)
        showSuccess('Schedule event updated')
        return true
      } else {
        showError(response.error || 'Failed to update schedule event')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while updating schedule event')
      return false
    }
  }, [user, schedule, showError, showSuccess])

  // Delete schedule event
  const deleteScheduleEvent = useCallback(async (eventId: string): Promise<boolean> => {
    if (!user) {
      showError('You must be logged in to delete schedule events')
      return false
    }

    try {
      const updatedSchedule = schedule.filter(event => event.id !== eventId)

      const response = await contractorService.update(user.id, {
        schedule: updatedSchedule
      })

      if (response.success) {
        setSchedule(updatedSchedule)
        showSuccess('Schedule event deleted')
        return true
      } else {
        showError(response.error || 'Failed to delete schedule event')
        return false
      }
    } catch (err) {
      showError('An unexpected error occurred while deleting schedule event')
      return false
    }
  }, [user, schedule, showError, showSuccess])

  // Get upcoming events
  const getUpcomingEvents = useCallback((days: number = 7): Schedule[] => {
    const now = new Date()
    const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)
    
    return schedule.filter(event => {
      const eventDate = new Date(event.start_date)
      return eventDate >= now && eventDate <= futureDate
    })
  }, [schedule])

  // Refetch data
  const refetch = useCallback(async () => {
    await fetchData()
  }, [fetchData])

  // Initialize
  useEffect(() => {
    if (autoFetch) {
      fetchData()
    }
  }, [autoFetch, fetchData])

  return {
    portfolioItems,
    schedule,
    loading,
    error,
    addPortfolioItem,
    updatePortfolioItem,
    deletePortfolioItem,
    reorderPortfolio,
    uploadImages,
    addScheduleEvent,
    updateScheduleEvent,
    deleteScheduleEvent,
    getUpcomingEvents,
    refetch
  }
}
