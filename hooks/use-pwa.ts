import { useState, useEffect } from 'react'

interface PWAInstallPrompt {
  prompt: () => Promise<void>
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>
}

interface PWAState {
  isInstallable: boolean
  isInstalled: boolean
  isOnline: boolean
  installPrompt: PWAInstallPrompt | null
}

export function usePWA() {
  const [pwaState, setPwaState] = useState<PWAState>({
    isInstallable: false,
    isInstalled: false,
    isOnline: typeof navigator !== 'undefined' ? navigator.onLine : true,
    installPrompt: null
  })

  useEffect(() => {
    // Check if app is already installed
    const checkInstalled = () => {
      const isStandalone = window.matchMedia('(display-mode: standalone)').matches
      const isIOSStandalone = (window.navigator as any).standalone === true
      
      setPwaState(prev => ({
        ...prev,
        isInstalled: isStandalone || isIOSStandalone
      }))
    }

    // Listen for install prompt
    const handleBeforeInstallPrompt = (e: Event) => {
      e.preventDefault()
      setPwaState(prev => ({
        ...prev,
        isInstallable: true,
        installPrompt: e as any
      }))
    }

    // Listen for app installed
    const handleAppInstalled = () => {
      setPwaState(prev => ({
        ...prev,
        isInstalled: true,
        isInstallable: false,
        installPrompt: null
      }))
    }

    // Listen for online/offline status
    const handleOnline = () => {
      setPwaState(prev => ({ ...prev, isOnline: true }))
    }

    const handleOffline = () => {
      setPwaState(prev => ({ ...prev, isOnline: false }))
    }

    if (typeof window !== 'undefined') {
      checkInstalled()
      
      window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
      window.addEventListener('appinstalled', handleAppInstalled)
      window.addEventListener('online', handleOnline)
      window.addEventListener('offline', handleOffline)

      return () => {
        window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
        window.removeEventListener('appinstalled', handleAppInstalled)
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
      }
    }
  }, [])

  const installApp = async () => {
    if (!pwaState.installPrompt) return false

    try {
      await pwaState.installPrompt.prompt()
      const choiceResult = await pwaState.installPrompt.userChoice
      
      if (choiceResult.outcome === 'accepted') {
        setPwaState(prev => ({
          ...prev,
          isInstallable: false,
          installPrompt: null
        }))
        return true
      }
      
      return false
    } catch (error) {
      console.error('Error installing PWA:', error)
      return false
    }
  }

  return {
    ...pwaState,
    installApp
  }
}

// Hook for service worker management
export function useServiceWorker() {
  const [swState, setSwState] = useState({
    isSupported: false,
    isRegistered: false,
    isUpdating: false,
    hasUpdate: false,
    registration: null as ServiceWorkerRegistration | null
  })

  useEffect(() => {
    if ('serviceWorker' in navigator) {
      setSwState(prev => ({ ...prev, isSupported: true }))
      
      navigator.serviceWorker.register('/sw.js')
        .then((registration) => {
          console.log('Service Worker registered:', registration)
          
          setSwState(prev => ({
            ...prev,
            isRegistered: true,
            registration
          }))

          // Check for updates
          registration.addEventListener('updatefound', () => {
            const newWorker = registration.installing
            
            if (newWorker) {
              setSwState(prev => ({ ...prev, isUpdating: true }))
              
              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  setSwState(prev => ({
                    ...prev,
                    isUpdating: false,
                    hasUpdate: true
                  }))
                }
              })
            }
          })
        })
        .catch((error) => {
          console.error('Service Worker registration failed:', error)
        })
    }
  }, [])

  const updateServiceWorker = () => {
    if (swState.registration) {
      swState.registration.waiting?.postMessage({ type: 'SKIP_WAITING' })
      window.location.reload()
    }
  }

  return {
    ...swState,
    updateServiceWorker
  }
}

// Hook for push notifications
export function usePushNotifications() {
  const [notificationState, setNotificationState] = useState({
    isSupported: false,
    permission: 'default' as NotificationPermission,
    isSubscribed: false,
    subscription: null as PushSubscription | null
  })

  useEffect(() => {
    if ('Notification' in window && 'serviceWorker' in navigator) {
      setNotificationState(prev => ({
        ...prev,
        isSupported: true,
        permission: Notification.permission
      }))

      // Check existing subscription
      navigator.serviceWorker.ready.then((registration) => {
        return registration.pushManager.getSubscription()
      }).then((subscription) => {
        setNotificationState(prev => ({
          ...prev,
          isSubscribed: !!subscription,
          subscription
        }))
      })
    }
  }, [])

  const requestPermission = async () => {
    if (!notificationState.isSupported) return false

    try {
      const permission = await Notification.requestPermission()
      setNotificationState(prev => ({ ...prev, permission }))
      return permission === 'granted'
    } catch (error) {
      console.error('Error requesting notification permission:', error)
      return false
    }
  }

  const subscribe = async () => {
    if (!notificationState.isSupported || notificationState.permission !== 'granted') {
      return null
    }

    try {
      const registration = await navigator.serviceWorker.ready
      
      // You would need to replace this with your actual VAPID public key
      const vapidPublicKey = 'YOUR_VAPID_PUBLIC_KEY'
      
      const subscription = await registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: vapidPublicKey
      })

      setNotificationState(prev => ({
        ...prev,
        isSubscribed: true,
        subscription
      }))

      // Send subscription to your server
      // await sendSubscriptionToServer(subscription)

      return subscription
    } catch (error) {
      console.error('Error subscribing to push notifications:', error)
      return null
    }
  }

  const unsubscribe = async () => {
    if (!notificationState.subscription) return false

    try {
      await notificationState.subscription.unsubscribe()
      
      setNotificationState(prev => ({
        ...prev,
        isSubscribed: false,
        subscription: null
      }))

      // Remove subscription from your server
      // await removeSubscriptionFromServer(notificationState.subscription)

      return true
    } catch (error) {
      console.error('Error unsubscribing from push notifications:', error)
      return false
    }
  }

  const showNotification = (title: string, options?: NotificationOptions) => {
    if (notificationState.permission === 'granted') {
      return new Notification(title, {
        icon: '/icons/icon-192x192.png',
        badge: '/icons/badge-72x72.png',
        ...options
      })
    }
    return null
  }

  return {
    ...notificationState,
    requestPermission,
    subscribe,
    unsubscribe,
    showNotification
  }
}
