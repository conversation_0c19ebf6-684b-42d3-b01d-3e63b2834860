import { useEffect, useRef, useState } from 'react'

interface PerformanceMetrics {
  loadTime: number
  renderTime: number
  interactionTime: number
  memoryUsage?: number
}

export function usePerformance(componentName: string) {
  const startTime = useRef<number>(Date.now())
  const renderStartTime = useRef<number>(Date.now())
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null)

  useEffect(() => {
    const renderEndTime = Date.now()
    const renderTime = renderEndTime - renderStartTime.current

    // Measure load time
    const loadTime = renderEndTime - startTime.current

    // Measure memory usage (if available)
    let memoryUsage: number | undefined
    if ('memory' in performance) {
      const memory = (performance as any).memory
      memoryUsage = memory.usedJSHeapSize / 1024 / 1024 // Convert to MB
    }

    const performanceMetrics: PerformanceMetrics = {
      loadTime,
      renderTime,
      interactionTime: 0, // Will be updated on interactions
      memoryUsage
    }

    setMetrics(performanceMetrics)

    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚀 Performance Metrics: ${componentName}`)
      console.log(`Load Time: ${loadTime}ms`)
      console.log(`Render Time: ${renderTime}ms`)
      if (memoryUsage) {
        console.log(`Memory Usage: ${memoryUsage.toFixed(2)}MB`)
      }
      console.groupEnd()
    }

    // Send to analytics in production
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to analytics service
      // analytics.track('component_performance', {
      //   component: componentName,
      //   loadTime,
      //   renderTime,
      //   memoryUsage
      // })
    }
  }, [componentName])

  const measureInteraction = (interactionName: string) => {
    const interactionStart = Date.now()
    
    return () => {
      const interactionEnd = Date.now()
      const interactionTime = interactionEnd - interactionStart
      
      setMetrics(prev => prev ? { ...prev, interactionTime } : null)
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`⚡ ${interactionName} took ${interactionTime}ms`)
      }
    }
  }

  return { metrics, measureInteraction }
}

// Hook for measuring API call performance
export function useApiPerformance() {
  const measureApiCall = async <T>(
    apiCall: () => Promise<T>,
    endpoint: string
  ): Promise<T> => {
    const startTime = Date.now()
    
    try {
      const result = await apiCall()
      const endTime = Date.now()
      const duration = endTime - startTime
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🌐 API Call ${endpoint}: ${duration}ms`)
      }
      
      // Send to analytics in production
      if (process.env.NODE_ENV === 'production') {
        // analytics.track('api_performance', {
        //   endpoint,
        //   duration,
        //   success: true
        // })
      }
      
      return result
    } catch (error) {
      const endTime = Date.now()
      const duration = endTime - startTime
      
      if (process.env.NODE_ENV === 'development') {
        console.error(`❌ API Call ${endpoint} failed after ${duration}ms:`, error)
      }
      
      // Send error to analytics in production
      if (process.env.NODE_ENV === 'production') {
        // analytics.track('api_performance', {
        //   endpoint,
        //   duration,
        //   success: false,
        //   error: error.message
        // })
      }
      
      throw error
    }
  }

  return { measureApiCall }
}

// Hook for measuring page load performance
export function usePagePerformance(pageName: string) {
  useEffect(() => {
    // Wait for page to be fully loaded
    const handleLoad = () => {
      if (typeof window !== 'undefined' && 'performance' in window) {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        const metrics = {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstPaint: 0,
          firstContentfulPaint: 0
        }

        // Get paint metrics if available
        const paintEntries = performance.getEntriesByType('paint')
        paintEntries.forEach((entry) => {
          if (entry.name === 'first-paint') {
            metrics.firstPaint = entry.startTime
          } else if (entry.name === 'first-contentful-paint') {
            metrics.firstContentfulPaint = entry.startTime
          }
        })

        if (process.env.NODE_ENV === 'development') {
          console.group(`📊 Page Performance: ${pageName}`)
          console.log(`DOM Content Loaded: ${metrics.domContentLoaded}ms`)
          console.log(`Load Complete: ${metrics.loadComplete}ms`)
          if (metrics.firstPaint) {
            console.log(`First Paint: ${metrics.firstPaint}ms`)
          }
          if (metrics.firstContentfulPaint) {
            console.log(`First Contentful Paint: ${metrics.firstContentfulPaint}ms`)
          }
          console.groupEnd()
        }

        // Send to analytics in production
        if (process.env.NODE_ENV === 'production') {
          // analytics.track('page_performance', {
          //   page: pageName,
          //   ...metrics
          // })
        }
      }
    }

    if (document.readyState === 'complete') {
      handleLoad()
    } else {
      window.addEventListener('load', handleLoad)
      return () => window.removeEventListener('load', handleLoad)
    }
  }, [pageName])
}

// Hook for Core Web Vitals
export function useCoreWebVitals() {
  useEffect(() => {
    if (typeof window === 'undefined') return

    // Largest Contentful Paint (LCP)
    const observeLCP = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          const lastEntry = entries[entries.length - 1]
          
          if (process.env.NODE_ENV === 'development') {
            console.log(`🎯 LCP: ${lastEntry.startTime}ms`)
          }
        })
        
        observer.observe({ entryTypes: ['largest-contentful-paint'] })
        
        return () => observer.disconnect()
      }
    }

    // First Input Delay (FID)
    const observeFID = () => {
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry) => {
            const fidEntry = entry as any // PerformanceEventTiming
            const fid = fidEntry.processingStart - fidEntry.startTime

            if (process.env.NODE_ENV === 'development') {
              console.log(`⚡ FID: ${fid}ms`)
            }
          })
        })
        
        observer.observe({ entryTypes: ['first-input'] })
        
        return () => observer.disconnect()
      }
    }

    // Cumulative Layout Shift (CLS)
    const observeCLS = () => {
      if ('PerformanceObserver' in window) {
        let clsValue = 0
        
        const observer = new PerformanceObserver((list) => {
          const entries = list.getEntries()
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value
            }
          })
          
          if (process.env.NODE_ENV === 'development') {
            console.log(`📐 CLS: ${clsValue}`)
          }
        })
        
        observer.observe({ entryTypes: ['layout-shift'] })
        
        return () => observer.disconnect()
      }
    }

    const cleanupLCP = observeLCP()
    const cleanupFID = observeFID()
    const cleanupCLS = observeCLS()

    return () => {
      cleanupLCP?.()
      cleanupFID?.()
      cleanupCLS?.()
    }
  }, [])
}
