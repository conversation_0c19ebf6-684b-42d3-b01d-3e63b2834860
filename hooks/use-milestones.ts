"use client"

import { useState, useEffect, useCallback } from 'react'
import { useUser } from '@/contexts/user-context'
import { useToastActions } from '@/components/ui/toast-system'
import { projectService } from '@/services/database'
import type { Tables } from '@/lib/supabase'

export interface Milestone {
  id: string
  title: string
  description: string
  status: 'pending' | 'in-progress' | 'completed' | 'blocked'
  due_date?: string
  completed_date?: string
  order: number
  requirements?: string[]
  attachments?: Array<{
    name: string
    url: string
    type: string
  }>
}

export interface ProjectStatus {
  id: string
  status: 'draft' | 'active' | 'in-progress' | 'completed' | 'cancelled' | 'on-hold'
  progress_percentage: number
  milestones: Milestone[]
  status_history: Array<{
    status: string
    changed_at: string
    changed_by: string
    notes?: string
  }>
}

export interface UseMilestonesOptions {
  projectId: string
  autoFetch?: boolean
}

export interface UseMilestonesReturn {
  project: ProjectStatus | null
  milestones: Milestone[]
  loading: boolean
  error: string | null
  updateProjectStatus: (status: ProjectStatus['status'], notes?: string) => Promise<boolean>
  updateMilestone: (milestoneId: string, updates: Partial<Milestone>) => Promise<boolean>
  addMilestone: (milestone: Omit<Milestone, 'id'>) => Promise<boolean>
  deleteMilestone: (milestoneId: string) => Promise<boolean>
  reorderMilestones: (milestoneIds: string[]) => Promise<boolean>
  calculateProgress: () => number
  getNextMilestone: () => Milestone | null
  refetch: () => Promise<void>
}

export function useMilestones(options: UseMilestonesOptions): UseMilestonesReturn {
  const { projectId, autoFetch = true } = options
  const { user } = useUser()
  const { showError, showSuccess } = useToastActions()
  
  const [project, setProject] = useState<ProjectStatus | null>(null)
  const [milestones, setMilestones] = useState<Milestone[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Fetch project with milestones
  const fetchProject = useCallback(async () => {
    if (!projectId) return

    console.log('Fetching project with ID:', projectId, 'for user:', user?.id)
    setLoading(true)
    setError(null)

    try {
      const response = await projectService.findById(projectId)
      console.log('Project fetch response:', { success: response.success, error: response.error, hasData: !!response.data })
      if (response.success && response.data) {
        const projectData = response.data as any
        
        // Parse milestones from project data
        const projectMilestones = projectData.milestones || []
        const statusHistory = projectData.status_history || []
        
        const projectStatus: ProjectStatus = {
          id: projectData.id,
          status: projectData.status,
          progress_percentage: calculateProgressFromMilestones(projectMilestones),
          milestones: projectMilestones,
          status_history: statusHistory
        }
        
        setProject(projectStatus)
        setMilestones(projectMilestones.sort((a: Milestone, b: Milestone) => a.order - b.order))
      } else {
        setError(response.error || 'Failed to fetch project')
      }
    } catch (err) {
      console.error('Error fetching project:', err)
      setError('An unexpected error occurred while fetching project')
    } finally {
      setLoading(false)
    }
  }, [projectId, user])

  // Calculate progress from milestones
  const calculateProgressFromMilestones = (milestoneList: Milestone[]): number => {
    if (milestoneList.length === 0) return 0
    const completedCount = milestoneList.filter(m => m.status === 'completed').length
    return Math.round((completedCount / milestoneList.length) * 100)
  }

  // Update project status
  const updateProjectStatus = useCallback(async (status: ProjectStatus['status'], notes?: string): Promise<boolean> => {
    if (!project || !user) {
      showError('Cannot update project status')
      return false
    }

    try {
      const statusHistoryEntry = {
        status,
        changed_at: new Date().toISOString(),
        changed_by: user.id,
        notes
      }

      const updates = {
        status,
        status_history: [...(project.status_history || []), statusHistoryEntry],
        updated_at: new Date().toISOString()
      }

      const response = await projectService.update(project.id, updates)
      if (response.success && response.data) {
        setProject(prev => prev ? { ...prev, status, status_history: updates.status_history } : null)
        showSuccess('Project status updated successfully')
        return true
      } else {
        showError(response.error || 'Failed to update project status')
        return false
      }
    } catch (err) {
      console.error('Error updating project status:', err)
      showError('An unexpected error occurred while updating project status')
      return false
    }
  }, [project, user, showError, showSuccess])

  // Update milestone
  const updateMilestone = useCallback(async (milestoneId: string, updates: Partial<Milestone>): Promise<boolean> => {
    if (!project) {
      showError('No project loaded')
      return false
    }

    try {
      const updatedMilestones = milestones.map(m => 
        m.id === milestoneId ? { ...m, ...updates } : m
      )

      const projectUpdates = {
        milestones: updatedMilestones,
        progress_percentage: calculateProgressFromMilestones(updatedMilestones),
        updated_at: new Date().toISOString()
      }

      const response = await projectService.update(project.id, projectUpdates)
      if (response.success) {
        setMilestones(updatedMilestones)
        setProject(prev => prev ? { 
          ...prev, 
          milestones: updatedMilestones,
          progress_percentage: projectUpdates.progress_percentage
        } : null)
        showSuccess('Milestone updated successfully')
        return true
      } else {
        showError(response.error || 'Failed to update milestone')
        return false
      }
    } catch (err) {
      console.error('Error updating milestone:', err)
      showError('An unexpected error occurred while updating milestone')
      return false
    }
  }, [project, milestones, showError, showSuccess])

  // Add milestone
  const addMilestone = useCallback(async (milestone: Omit<Milestone, 'id'>): Promise<boolean> => {
    if (!project) {
      showError('No project loaded')
      return false
    }

    try {
      const newMilestone: Milestone = {
        ...milestone,
        id: `milestone_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
      }

      const updatedMilestones = [...milestones, newMilestone].sort((a, b) => a.order - b.order)

      const projectUpdates = {
        milestones: updatedMilestones,
        updated_at: new Date().toISOString()
      }

      const response = await projectService.update(project.id, projectUpdates)
      if (response.success) {
        setMilestones(updatedMilestones)
        setProject(prev => prev ? { ...prev, milestones: updatedMilestones } : null)
        showSuccess('Milestone added successfully')
        return true
      } else {
        showError(response.error || 'Failed to add milestone')
        return false
      }
    } catch (err) {
      console.error('Error adding milestone:', err)
      showError('An unexpected error occurred while adding milestone')
      return false
    }
  }, [project, milestones, showError, showSuccess])

  // Delete milestone
  const deleteMilestone = useCallback(async (milestoneId: string): Promise<boolean> => {
    if (!project) {
      showError('No project loaded')
      return false
    }

    try {
      const updatedMilestones = milestones.filter(m => m.id !== milestoneId)

      const projectUpdates = {
        milestones: updatedMilestones,
        progress_percentage: calculateProgressFromMilestones(updatedMilestones),
        updated_at: new Date().toISOString()
      }

      const response = await projectService.update(project.id, projectUpdates)
      if (response.success) {
        setMilestones(updatedMilestones)
        setProject(prev => prev ? { 
          ...prev, 
          milestones: updatedMilestones,
          progress_percentage: projectUpdates.progress_percentage
        } : null)
        showSuccess('Milestone deleted successfully')
        return true
      } else {
        showError(response.error || 'Failed to delete milestone')
        return false
      }
    } catch (err) {
      console.error('Error deleting milestone:', err)
      showError('An unexpected error occurred while deleting milestone')
      return false
    }
  }, [project, milestones, showError, showSuccess])

  // Reorder milestones
  const reorderMilestones = useCallback(async (milestoneIds: string[]): Promise<boolean> => {
    if (!project) {
      showError('No project loaded')
      return false
    }

    try {
      const reorderedMilestones = milestoneIds.map((id, index) => {
        const milestone = milestones.find(m => m.id === id)
        return milestone ? { ...milestone, order: index + 1 } : null
      }).filter(Boolean) as Milestone[]

      const projectUpdates = {
        milestones: reorderedMilestones,
        updated_at: new Date().toISOString()
      }

      const response = await projectService.update(project.id, projectUpdates)
      if (response.success) {
        setMilestones(reorderedMilestones)
        setProject(prev => prev ? { ...prev, milestones: reorderedMilestones } : null)
        showSuccess('Milestones reordered successfully')
        return true
      } else {
        showError(response.error || 'Failed to reorder milestones')
        return false
      }
    } catch (err) {
      console.error('Error reordering milestones:', err)
      showError('An unexpected error occurred while reordering milestones')
      return false
    }
  }, [project, milestones, showError, showSuccess])

  // Calculate current progress
  const calculateProgress = useCallback((): number => {
    return calculateProgressFromMilestones(milestones)
  }, [milestones])

  // Get next milestone
  const getNextMilestone = useCallback((): Milestone | null => {
    const nextMilestone = milestones.find(m => m.status === 'pending' || m.status === 'in-progress')
    return nextMilestone || null
  }, [milestones])

  // Refetch data
  const refetch = useCallback(async () => {
    await fetchProject()
  }, [fetchProject])

  // Initialize
  useEffect(() => {
    if (autoFetch && projectId) {
      fetchProject()
    }
  }, [autoFetch, projectId, fetchProject])

  return {
    project,
    milestones,
    loading,
    error,
    updateProjectStatus,
    updateMilestone,
    addMilestone,
    deleteMilestone,
    reorderMilestones,
    calculateProgress,
    getNextMilestone,
    refetch
  }
}
