#!/bin/bash

# Setup Database Script
# This script runs database migrations and seeds the database with test data

set -e

echo "🚀 Setting up RenovHub database..."

# Check if Supabase CLI is installed
if ! command -v supabase &> /dev/null; then
    echo "❌ Supabase CLI is not installed. Installing it now..."
    npm install -g supabase

    if [ $? -ne 0 ]; then
        echo "❌ Failed to install Supabase CLI. Please install it manually:"
        echo "   npm install -g supabase"
        exit 1
    fi

    echo "✅ Supabase CLI installed successfully!"
fi

# Check if we're in a Supabase project
if [ ! -f "supabase/config.toml" ]; then
    echo "❌ Not in a Supabase project directory. Please run this from the project root."
    exit 1
fi

echo "📊 Running database migrations..."

# Run migrations
supabase db push

if [ $? -eq 0 ]; then
    echo "✅ Database migrations completed successfully!"
else
    echo "❌ Database migrations failed!"
    exit 1
fi

echo "🌱 Seeding database with test data..."

# Run the seeding script
npx tsx scripts/seed-database.ts

if [ $? -eq 0 ]; then
    echo "✅ Database seeding completed successfully!"
else
    echo "❌ Database seeding failed!"
    exit 1
fi

echo "🎉 Database setup complete!"
echo ""
echo "Your RenovHub database is now ready with:"
echo "  - All tables and relationships"
echo "  - Row Level Security policies"
echo "  - Comprehensive test data"
echo ""
echo "You can now start the development server with: npm run dev"
