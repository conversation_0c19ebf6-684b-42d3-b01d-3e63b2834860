// Core User Types
export type UserRole = "customer" | "pro"
export type UserStatus = "active" | "inactive" | "suspended" | "pending"

export interface User {
  id: string
  email: string
  name: string
  role: UserRole
  status: UserStatus
  avatar?: string
  phone?: string
  location?: string
  createdAt: Date
  updatedAt: Date
  verified: boolean
  preferences: UserPreferences
}

export interface UserPreferences {
  notifications: NotificationSettings
  privacy: PrivacySettings
  theme: "light" | "dark" | "system"
  language: string
}

export interface NotificationSettings {
  email: boolean
  push: boolean
  sms: boolean
  marketing: boolean
  projectUpdates: boolean
  bidAlerts: boolean
  messageAlerts: boolean
}

export interface PrivacySettings {
  profileVisibility: "public" | "private" | "contractors-only"
  showLocation: boolean
  showPhone: boolean
  allowDirectContact: boolean
}

// Project Types
export type ProjectStatus = "draft" | "active" | "in-progress" | "completed" | "cancelled"
export type ProjectCategory = "kitchen" | "bathroom" | "flooring" | "painting" | "electrical" | "plumbing" | "roofing" | "landscaping" | "general"
export type ProjectPriority = "low" | "medium" | "high" | "urgent"

export interface Project {
  id: string
  title: string
  description: string
  category: ProjectCategory
  status: ProjectStatus
  priority: ProjectPriority
  customerId: string
  customer: User
  budget: BudgetRange
  timeline: Timeline
  location: Location
  photos: ProjectPhoto[]
  requirements: string[]
  bids: Bid[]
  selectedBid?: string
  contractorId?: string
  contractor?: Contractor
  createdAt: Date
  updatedAt: Date
  completedAt?: Date
  milestones: Milestone[]
  tags: string[]
}

export interface BudgetRange {
  min: number
  max: number
  currency: string
  flexible: boolean
}

export interface Timeline {
  startDate?: Date
  endDate?: Date
  duration?: number // in days
  flexible: boolean
  urgency: "asap" | "within-week" | "within-month" | "flexible"
}

export interface Location {
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  coordinates?: {
    lat: number
    lng: number
  }
}

export interface ProjectPhoto {
  id: string
  url: string
  caption?: string
  type: "before" | "reference" | "plan" | "progress" | "after"
  uploadedAt: Date
  uploadedBy: string
}

export interface Milestone {
  id: string
  title: string
  description: string
  status: "pending" | "in-progress" | "completed" | "delayed"
  dueDate: Date
  completedAt?: Date
  progress: number // 0-100
  dependencies: string[] // milestone IDs
}

// Contractor Types
export type ContractorStatus = "active" | "inactive" | "suspended" | "pending-verification"
export type ContractorTier = "basic" | "premium" | "elite"

export interface Contractor {
  id: string
  userId: string
  user: User
  businessName: string
  license: string
  insurance: InsuranceInfo
  specialties: ProjectCategory[]
  serviceAreas: Location[]
  tier: ContractorTier
  status: ContractorStatus
  rating: Rating
  portfolio: PortfolioItem[]
  certifications: Certification[]
  workingHours: WorkingHours
  responseTime: number // average in hours
  completionRate: number // percentage
  onTimeRate: number // percentage
  createdAt: Date
  updatedAt: Date
}

export interface InsuranceInfo {
  provider: string
  policyNumber: string
  coverage: number
  expiresAt: Date
  verified: boolean
}

export interface Rating {
  average: number
  count: number
  breakdown: {
    quality: number
    communication: number
    timeliness: number
    value: number
  }
}

export interface PortfolioItem {
  id: string
  title: string
  description: string
  category: ProjectCategory
  photos: string[]
  completedAt: Date
  budget?: number
  duration?: number
  featured: boolean
}

export interface Certification {
  id: string
  name: string
  issuer: string
  issuedAt: Date
  expiresAt?: Date
  verified: boolean
  documentUrl?: string
}

export interface WorkingHours {
  monday: DaySchedule
  tuesday: DaySchedule
  wednesday: DaySchedule
  thursday: DaySchedule
  friday: DaySchedule
  saturday: DaySchedule
  sunday: DaySchedule
}

export interface DaySchedule {
  isWorking: boolean
  startTime?: string // HH:mm format
  endTime?: string // HH:mm format
  breaks?: TimeSlot[]
}

export interface TimeSlot {
  startTime: string
  endTime: string
}

// Bid Types
export type BidStatus = "pending" | "submitted" | "accepted" | "rejected" | "withdrawn"

export interface Bid {
  id: string
  projectId: string
  project: Project
  contractorId: string
  contractor: Contractor
  status: BidStatus
  amount: number
  currency: string
  timeline: Timeline
  description: string
  breakdown: BidBreakdown[]
  terms: string
  warranty: WarrantyInfo
  submittedAt: Date
  updatedAt: Date
  expiresAt: Date
  questions: BidQuestion[]
  attachments: BidAttachment[]
}

export interface BidBreakdown {
  category: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

export interface WarrantyInfo {
  duration: number // in months
  coverage: string
  terms: string
}

export interface BidQuestion {
  id: string
  question: string
  answer?: string
  askedAt: Date
  answeredAt?: Date
}

export interface BidAttachment {
  id: string
  name: string
  url: string
  type: string
  size: number
  uploadedAt: Date
}

// Message Types
export type MessageType = "text" | "image" | "file" | "system"
export type MessageStatus = "sent" | "delivered" | "read"

export interface Conversation {
  id: string
  projectId: string
  participants: string[] // user IDs
  lastMessage?: Message
  unreadCount: number
  createdAt: Date
  updatedAt: Date
}

export interface Message {
  id: string
  conversationId: string
  senderId: string
  sender: User
  type: MessageType
  content: string
  attachments: MessageAttachment[]
  status: MessageStatus
  sentAt: Date
  readAt?: Date
  editedAt?: Date
  replyTo?: string // message ID
}

export interface MessageAttachment {
  id: string
  name: string
  url: string
  type: string
  size: number
  thumbnail?: string
}

// Review Types
export interface Review {
  id: string
  projectId: string
  project: Project
  reviewerId: string
  reviewer: User
  revieweeId: string
  reviewee: User
  rating: number
  title: string
  content: string
  photos: string[]
  helpful: number
  verified: boolean
  response?: ReviewResponse
  createdAt: Date
  updatedAt: Date
}

export interface ReviewResponse {
  content: string
  respondedAt: Date
}

// Payment Types
export type PaymentStatus = "pending" | "processing" | "completed" | "failed" | "refunded"
export type PaymentMethod = "card" | "bank" | "paypal" | "apple-pay" | "google-pay"

export interface Payment {
  id: string
  projectId: string
  bidId: string
  payerId: string
  payeeId: string
  amount: number
  currency: string
  method: PaymentMethod
  status: PaymentStatus
  description: string
  fees: PaymentFee[]
  processedAt?: Date
  refundedAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface PaymentFee {
  type: "platform" | "processing" | "tax"
  amount: number
  description: string
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean
  data?: T
  error?: ApiError
  meta?: ApiMeta
}

export interface ApiError {
  code: string
  message: string
  details?: Record<string, any>
}

export interface ApiMeta {
  page?: number
  limit?: number
  total?: number
  hasMore?: boolean
}

// Form Types
export interface FormField {
  name: string
  label: string
  type: "text" | "email" | "password" | "number" | "select" | "textarea" | "checkbox" | "radio" | "file" | "date"
  required: boolean
  placeholder?: string
  options?: FormOption[]
  validation?: FormValidation
}

export interface FormOption {
  value: string
  label: string
  disabled?: boolean
}

export interface FormValidation {
  min?: number
  max?: number
  pattern?: string
  message?: string
}

// Search and Filter Types
export interface SearchFilters {
  category?: ProjectCategory[]
  location?: string
  budget?: BudgetRange
  rating?: number
  verified?: boolean
  availability?: boolean
  sortBy?: "rating" | "price" | "distance" | "reviews" | "recent"
  sortOrder?: "asc" | "desc"
}

export interface SearchResult<T> {
  items: T[]
  total: number
  filters: SearchFilters
  suggestions?: string[]
}
