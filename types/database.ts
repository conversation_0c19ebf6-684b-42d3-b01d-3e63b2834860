export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          role: 'customer' | 'pro'
          status: 'active' | 'inactive' | 'suspended' | 'pending'
          avatar_url: string | null
          phone: string | null
          location: string | null
          created_at: string
          updated_at: string
          verified: boolean
          preferences: Json
        }
        Insert: {
          id?: string
          email: string
          name: string
          role: 'customer' | 'pro'
          status?: 'active' | 'inactive' | 'suspended' | 'pending'
          avatar_url?: string | null
          phone?: string | null
          location?: string | null
          created_at?: string
          updated_at?: string
          verified?: boolean
          preferences?: Json
        }
        Update: {
          id?: string
          email?: string
          name?: string
          role?: 'customer' | 'pro'
          status?: 'active' | 'inactive' | 'suspended' | 'pending'
          avatar_url?: string | null
          phone?: string | null
          location?: string | null
          created_at?: string
          updated_at?: string
          verified?: boolean
          preferences?: <PERSON><PERSON>
        }
      }
      contractors: {
        Row: {
          id: string
          user_id: string
          business_name: string
          license: string
          insurance_info: Json
          specialties: string[]
          service_areas: Json[]
          tier: 'basic' | 'premium' | 'elite'
          status: 'active' | 'inactive' | 'suspended' | 'pending-verification'
          rating_average: number
          rating_count: number
          rating_breakdown: Json
          portfolio: Json[]
          certifications: Json[]
          working_hours: Json
          response_time: number
          completion_rate: number
          on_time_rate: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          business_name: string
          license: string
          insurance_info: Json
          specialties: string[]
          service_areas: Json[]
          tier?: 'basic' | 'premium' | 'elite'
          status?: 'active' | 'inactive' | 'suspended' | 'pending-verification'
          rating_average?: number
          rating_count?: number
          rating_breakdown?: Json
          portfolio?: Json[]
          certifications?: Json[]
          working_hours?: Json
          response_time?: number
          completion_rate?: number
          on_time_rate?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          business_name?: string
          license?: string
          insurance_info?: Json
          specialties?: string[]
          service_areas?: Json[]
          tier?: 'basic' | 'premium' | 'elite'
          status?: 'active' | 'inactive' | 'suspended' | 'pending-verification'
          rating_average?: number
          rating_count?: number
          rating_breakdown?: Json
          portfolio?: Json[]
          certifications?: Json[]
          working_hours?: Json
          response_time?: number
          completion_rate?: number
          on_time_rate?: number
          created_at?: string
          updated_at?: string
        }
      }
      projects: {
        Row: {
          id: string
          title: string
          description: string
          category: 'kitchen' | 'bathroom' | 'flooring' | 'painting' | 'electrical' | 'plumbing' | 'roofing' | 'landscaping' | 'general'
          status: 'draft' | 'active' | 'in-progress' | 'completed' | 'cancelled'
          priority: 'low' | 'medium' | 'high' | 'urgent'
          customer_id: string
          budget: Json
          timeline: Json
          location: Json
          photos: Json[]
          requirements: string[]
          selected_bid_id: string | null
          contractor_id: string | null
          created_at: string
          updated_at: string
          completed_at: string | null
          milestones: Json[]
          tags: string[]
        }
        Insert: {
          id?: string
          title: string
          description: string
          category: 'kitchen' | 'bathroom' | 'flooring' | 'painting' | 'electrical' | 'plumbing' | 'roofing' | 'landscaping' | 'general'
          status?: 'draft' | 'active' | 'in-progress' | 'completed' | 'cancelled'
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          customer_id: string
          budget: Json
          timeline: Json
          location: Json
          photos?: Json[]
          requirements?: string[]
          selected_bid_id?: string | null
          contractor_id?: string | null
          created_at?: string
          updated_at?: string
          completed_at?: string | null
          milestones?: Json[]
          tags?: string[]
        }
        Update: {
          id?: string
          title?: string
          description?: string
          category?: 'kitchen' | 'bathroom' | 'flooring' | 'painting' | 'electrical' | 'plumbing' | 'roofing' | 'landscaping' | 'general'
          status?: 'draft' | 'active' | 'in-progress' | 'completed' | 'cancelled'
          priority?: 'low' | 'medium' | 'high' | 'urgent'
          customer_id?: string
          budget?: Json
          timeline?: Json
          location?: Json
          photos?: Json[]
          requirements?: string[]
          selected_bid_id?: string | null
          contractor_id?: string | null
          created_at?: string
          updated_at?: string
          completed_at?: string | null
          milestones?: Json[]
          tags?: string[]
        }
      }
      bids: {
        Row: {
          id: string
          project_id: string
          contractor_id: string
          status: 'pending' | 'submitted' | 'accepted' | 'rejected' | 'withdrawn'
          amount: number
          currency: string
          timeline: Json
          description: string
          breakdown: Json[]
          terms: string
          warranty: Json
          submitted_at: string
          updated_at: string
          expires_at: string
          questions: Json[]
          attachments: Json[]
        }
        Insert: {
          id?: string
          project_id: string
          contractor_id: string
          status?: 'pending' | 'submitted' | 'accepted' | 'rejected' | 'withdrawn'
          amount: number
          currency?: string
          timeline: Json
          description: string
          breakdown: Json[]
          terms: string
          warranty: Json
          submitted_at?: string
          updated_at?: string
          expires_at: string
          questions?: Json[]
          attachments?: Json[]
        }
        Update: {
          id?: string
          project_id?: string
          contractor_id?: string
          status?: 'pending' | 'submitted' | 'accepted' | 'rejected' | 'withdrawn'
          amount?: number
          currency?: string
          timeline?: Json
          description?: string
          breakdown?: Json[]
          terms?: string
          warranty?: Json
          submitted_at?: string
          updated_at?: string
          expires_at?: string
          questions?: Json[]
          attachments?: Json[]
        }
      }
      conversations: {
        Row: {
          id: string
          project_id: string
          participants: string[]
          last_message_id: string | null
          unread_count: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          participants: string[]
          last_message_id?: string | null
          unread_count?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          participants?: string[]
          last_message_id?: string | null
          unread_count?: number
          created_at?: string
          updated_at?: string
        }
      }
      messages: {
        Row: {
          id: string
          conversation_id: string
          sender_id: string
          type: 'text' | 'image' | 'file' | 'system'
          content: string
          attachments: Json[]
          status: 'sent' | 'delivered' | 'read'
          sent_at: string
          read_at: string | null
          edited_at: string | null
          reply_to: string | null
        }
        Insert: {
          id?: string
          conversation_id: string
          sender_id: string
          type?: 'text' | 'image' | 'file' | 'system'
          content: string
          attachments?: Json[]
          status?: 'sent' | 'delivered' | 'read'
          sent_at?: string
          read_at?: string | null
          edited_at?: string | null
          reply_to?: string | null
        }
        Update: {
          id?: string
          conversation_id?: string
          sender_id?: string
          type?: 'text' | 'image' | 'file' | 'system'
          content?: string
          attachments?: Json[]
          status?: 'sent' | 'delivered' | 'read'
          sent_at?: string
          read_at?: string | null
          edited_at?: string | null
          reply_to?: string | null
        }
      }
      reviews: {
        Row: {
          id: string
          project_id: string
          reviewer_id: string
          reviewee_id: string
          rating: number
          title: string
          content: string
          photos: string[]
          helpful: number
          verified: boolean
          response: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          reviewer_id: string
          reviewee_id: string
          rating: number
          title: string
          content: string
          photos?: string[]
          helpful?: number
          verified?: boolean
          response?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          reviewer_id?: string
          reviewee_id?: string
          rating?: number
          title?: string
          content?: string
          photos?: string[]
          helpful?: number
          verified?: boolean
          response?: Json | null
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          project_id: string
          bid_id: string
          payer_id: string
          payee_id: string
          amount: number
          currency: string
          method: string
          status: string
          description: string
          fees: Json[]
          processed_at: string | null
          refunded_at: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          project_id: string
          bid_id: string
          payer_id: string
          payee_id: string
          amount: number
          currency?: string
          method: string
          status?: string
          description: string
          fees?: Json[]
          processed_at?: string | null
          refunded_at?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          project_id?: string
          bid_id?: string
          payer_id?: string
          payee_id?: string
          amount?: number
          currency?: string
          method?: string
          status?: string
          description?: string
          fees?: Json[]
          processed_at?: string | null
          refunded_at?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
