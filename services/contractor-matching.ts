import { supabase } from '@/lib/supabase'
import { ServiceResponse, createResponse } from './database'
import type { Tables } from '@/lib/supabase'

export interface MatchingCriteria {
  category: string
  location: {
    address: string
    city: string
    state: string
    zipCode: string
    radius?: number // in miles
  }
  budget: {
    min: number
    max: number
  }
  timeline: {
    urgency: 'asap' | 'within-week' | 'within-month' | 'flexible'
    duration: number // in days
  }
  requirements: string[]
  preferences: {
    rating_minimum?: number
    verified_only?: boolean
    response_time_max?: number // in hours
    completion_rate_min?: number // percentage
    portfolio_required?: boolean
  }
}

export interface ContractorMatch {
  contractor: Tables<'contractors'> & {
    users: Tables<'users'>
  }
  match_score: number
  match_reasons: string[]
  availability: {
    available: boolean
    earliest_start: string
    estimated_duration: number
  }
  distance: number // in miles
  estimated_cost: {
    min: number
    max: number
    confidence: number // 0-1
  }
}

export interface AvailabilitySlot {
  id: string
  contractor_id: string
  start_date: string
  end_date: string
  status: 'available' | 'booked' | 'tentative'
  project_type?: string
  notes?: string
}

class ContractorMatchingService {
  // Smart contractor recommendation algorithm
  async findMatches(
    projectId: string, 
    criteria: MatchingCriteria, 
    limit: number = 10
  ): Promise<ServiceResponse<ContractorMatch[]>> {
    try {
      // Get contractors that match basic criteria
      const { data: contractors, error } = await supabase
        .from('contractors')
        .select(`
          *,
          users!user_id (*)
        `)
        .contains('specialties', [criteria.category])
        .eq('status', 'active')
        .gte('rating_average', criteria.preferences.rating_minimum || 0)

      if (error) {
        console.error('Error fetching contractors:', error)
        return createResponse(null, error.message)
      }

      if (!contractors || contractors.length === 0) {
        return createResponse([])
      }

      // Calculate match scores and additional data
      const matches: ContractorMatch[] = []

      for (const contractor of contractors) {
        const matchScore = await this.calculateMatchScore(contractor, criteria)
        const availability = await this.checkAvailability(contractor.id, criteria.timeline)
        const distance = await this.calculateDistance(contractor, criteria.location)
        const estimatedCost = await this.estimateCost(contractor, criteria)

        // Filter based on preferences
        if (criteria.preferences.verified_only && !contractor.users.verified) continue
        if (criteria.preferences.response_time_max && contractor.response_time > criteria.preferences.response_time_max) continue
        if (criteria.preferences.completion_rate_min && contractor.completion_rate < criteria.preferences.completion_rate_min) continue

        const matchReasons = this.generateMatchReasons(contractor, criteria, matchScore)

        matches.push({
          contractor,
          match_score: matchScore,
          match_reasons: matchReasons,
          availability,
          distance,
          estimated_cost: estimatedCost
        })
      }

      // Sort by match score and return top matches
      const sortedMatches = matches
        .sort((a, b) => b.match_score - a.match_score)
        .slice(0, limit)

      return createResponse(sortedMatches)
    } catch (error) {
      console.error('Unexpected error in findMatches:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  // Calculate match score based on multiple factors
  private async calculateMatchScore(
    contractor: any, 
    criteria: MatchingCriteria
  ): Promise<number> {
    let score = 0
    let maxScore = 0

    // Category match (30 points)
    maxScore += 30
    if (contractor.specialties.includes(criteria.category)) {
      score += 30
    }

    // Rating (25 points)
    maxScore += 25
    score += (contractor.rating_average / 5) * 25

    // Response time (15 points)
    maxScore += 15
    const responseTimeScore = Math.max(0, 15 - (contractor.response_time / 24) * 15)
    score += responseTimeScore

    // Completion rate (15 points)
    maxScore += 15
    score += (contractor.completion_rate / 100) * 15

    // Portfolio quality (10 points)
    maxScore += 10
    const portfolioScore = Math.min(10, (contractor.portfolio?.length || 0) * 2)
    score += portfolioScore

    // Verification status (5 points)
    maxScore += 5
    if (contractor.users.verified) {
      score += 5
    }

    // Return percentage score
    return Math.round((score / maxScore) * 100)
  }

  // Check contractor availability
  private async checkAvailability(
    contractorId: string, 
    timeline: MatchingCriteria['timeline']
  ): Promise<ContractorMatch['availability']> {
    try {
      // Get contractor's availability slots
      const { data: slots, error } = await supabase
        .from('contractor_availability')
        .select('*')
        .eq('contractor_id', contractorId)
        .eq('status', 'available')
        .gte('start_date', new Date().toISOString())
        .order('start_date', { ascending: true })

      if (error) {
        console.error('Error checking availability:', error)
        return {
          available: false,
          earliest_start: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          estimated_duration: timeline.duration
        }
      }

      // Find earliest available slot that can accommodate the project
      const now = new Date()
      const urgencyDays = this.getUrgencyDays(timeline.urgency)
      const preferredStart = new Date(now.getTime() + urgencyDays * 24 * 60 * 60 * 1000)

      if (slots && slots.length > 0) {
        const earliestSlot = slots[0]
        const slotStart = new Date(earliestSlot.start_date)
        const slotEnd = new Date(earliestSlot.end_date)
        const slotDuration = Math.floor((slotEnd.getTime() - slotStart.getTime()) / (1000 * 60 * 60 * 24))

        return {
          available: slotDuration >= timeline.duration,
          earliest_start: earliestSlot.start_date,
          estimated_duration: timeline.duration
        }
      }

      // Default availability estimate
      return {
        available: true,
        earliest_start: preferredStart.toISOString(),
        estimated_duration: timeline.duration
      }
    } catch (error) {
      console.error('Error in checkAvailability:', error)
      return {
        available: false,
        earliest_start: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        estimated_duration: timeline.duration
      }
    }
  }

  // Calculate distance between contractor and project location
  private async calculateDistance(
    contractor: any, 
    location: MatchingCriteria['location']
  ): Promise<number> {
    // This would integrate with a geocoding service like Google Maps API
    // For now, return a mock distance based on service areas
    const contractorAreas = contractor.service_areas || []
    
    // Check if contractor serves the project location
    const servesArea = contractorAreas.some((area: any) => 
      area.city?.toLowerCase() === location.city.toLowerCase() ||
      area.state?.toLowerCase() === location.state.toLowerCase()
    )

    // Return estimated distance (mock implementation)
    return servesArea ? Math.random() * 25 : Math.random() * 50 + 25
  }

  // Estimate project cost based on contractor's historical data
  private async estimateCost(
    contractor: any, 
    criteria: MatchingCriteria
  ): Promise<ContractorMatch['estimated_cost']> {
    try {
      // Get historical bids from this contractor for similar projects
      const { data: historicalBids, error } = await supabase
        .from('bids')
        .select(`
          amount,
          projects!project_id (category, budget)
        `)
        .eq('contractor_id', contractor.id)
        .eq('projects.category', criteria.category)
        .limit(10)

      if (error || !historicalBids || historicalBids.length === 0) {
        // Use industry averages or contractor tier-based estimates
        const baseRate = this.getBaseRateByTier(contractor.tier, criteria.category)
        const projectComplexity = this.estimateComplexity(criteria)
        
        return {
          min: Math.round(baseRate * projectComplexity * 0.8),
          max: Math.round(baseRate * projectComplexity * 1.2),
          confidence: 0.6
        }
      }

      // Calculate average from historical data
      const amounts = historicalBids.map(bid => bid.amount)
      const avgAmount = amounts.reduce((sum, amount) => sum + amount, 0) / amounts.length
      const variance = Math.sqrt(amounts.reduce((sum, amount) => sum + Math.pow(amount - avgAmount, 2), 0) / amounts.length)

      return {
        min: Math.round(avgAmount - variance),
        max: Math.round(avgAmount + variance),
        confidence: Math.min(0.9, historicalBids.length / 10)
      }
    } catch (error) {
      console.error('Error estimating cost:', error)
      return {
        min: criteria.budget.min,
        max: criteria.budget.max,
        confidence: 0.5
      }
    }
  }

  // Generate human-readable match reasons
  private generateMatchReasons(
    contractor: any, 
    criteria: MatchingCriteria, 
    score: number
  ): string[] {
    const reasons: string[] = []

    if (contractor.specialties.includes(criteria.category)) {
      reasons.push(`Specializes in ${criteria.category} projects`)
    }

    if (contractor.rating_average >= 4.5) {
      reasons.push(`Excellent rating (${contractor.rating_average}/5.0)`)
    }

    if (contractor.response_time <= 2) {
      reasons.push(`Fast response time (${contractor.response_time} hours)`)
    }

    if (contractor.completion_rate >= 95) {
      reasons.push(`High completion rate (${contractor.completion_rate}%)`)
    }

    if (contractor.users.verified) {
      reasons.push('Verified contractor')
    }

    if (contractor.portfolio && contractor.portfolio.length >= 5) {
      reasons.push('Extensive portfolio')
    }

    if (score >= 90) {
      reasons.push('Perfect match for your project')
    } else if (score >= 80) {
      reasons.push('Excellent match for your project')
    } else if (score >= 70) {
      reasons.push('Good match for your project')
    }

    return reasons
  }

  // Helper methods
  private getUrgencyDays(urgency: string): number {
    switch (urgency) {
      case 'asap': return 1
      case 'within-week': return 7
      case 'within-month': return 30
      default: return 60
    }
  }

  private getBaseRateByTier(tier: string, category: string): number {
    const rates: Record<string, Record<string, number>> = {
      basic: {
        kitchen: 15000,
        bathroom: 8000,
        flooring: 5000,
        painting: 3000,
        general: 5000
      },
      premium: {
        kitchen: 25000,
        bathroom: 15000,
        flooring: 8000,
        painting: 5000,
        general: 8000
      },
      elite: {
        kitchen: 40000,
        bathroom: 25000,
        flooring: 12000,
        painting: 8000,
        general: 12000
      }
    }

    return rates[tier]?.[category] || rates[tier]?.general || 10000
  }

  private estimateComplexity(criteria: MatchingCriteria): number {
    let complexity = 1

    // Budget-based complexity
    if (criteria.budget.max > 50000) complexity += 0.5
    if (criteria.budget.max > 100000) complexity += 0.5

    // Timeline-based complexity
    if (criteria.timeline.duration > 60) complexity += 0.3
    if (criteria.timeline.urgency === 'asap') complexity += 0.2

    // Requirements-based complexity
    complexity += criteria.requirements.length * 0.1

    return Math.min(complexity, 2.0)
  }

  // Availability management
  async getContractorAvailability(
    contractorId: string,
    startDate?: string,
    endDate?: string
  ): Promise<ServiceResponse<AvailabilitySlot[]>> {
    try {
      let query = supabase
        .from('contractor_availability')
        .select('*')
        .eq('contractor_id', contractorId)
        .order('start_date', { ascending: true })

      if (startDate) {
        query = query.gte('start_date', startDate)
      }

      if (endDate) {
        query = query.lte('end_date', endDate)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching availability:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in getContractorAvailability:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async updateAvailability(
    contractorId: string,
    slots: Omit<AvailabilitySlot, 'id' | 'contractor_id'>[]
  ): Promise<ServiceResponse<AvailabilitySlot[]>> {
    try {
      // Delete existing availability
      await supabase
        .from('contractor_availability')
        .delete()
        .eq('contractor_id', contractorId)

      // Insert new availability slots
      const slotsWithContractor = slots.map(slot => ({
        ...slot,
        contractor_id: contractorId
      }))

      const { data, error } = await supabase
        .from('contractor_availability')
        .insert(slotsWithContractor)
        .select()

      if (error) {
        console.error('Error updating availability:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in updateAvailability:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }
}

// Export singleton instance
export const contractorMatchingService = new ContractorMatchingService()
