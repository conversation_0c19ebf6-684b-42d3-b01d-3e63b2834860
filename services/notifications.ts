import { supabase } from '@/lib/supabase'
import { ServiceResponse, createResponse } from './database'

export interface NotificationData {
  id?: string
  user_id: string
  type: 'bid_received' | 'bid_accepted' | 'bid_rejected' | 'project_update' | 'message_received' | 'payment_received' | 'review_received' | 'system'
  title: string
  message: string
  data?: Record<string, any>
  read: boolean
  created_at?: string
}

export interface EmailTemplate {
  to: string
  subject: string
  html: string
  text?: string
}

export interface PushNotificationPayload {
  title: string
  body: string
  icon?: string
  badge?: string
  data?: Record<string, any>
}

export class NotificationService {
  // In-app notifications
  async createNotification(notification: Omit<NotificationData, 'id' | 'created_at'>): Promise<ServiceResponse<NotificationData>> {
    try {
      const { data, error } = await supabase
        .from('notifications')
        .insert({
          user_id: notification.user_id,
          type: notification.type,
          title: notification.title,
          message: notification.message,
          data: notification.data || {},
          read: false
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating notification:', error)
        return createResponse(null, error.message)
      }

      // Send real-time notification
      await this.sendRealtimeNotification(notification.user_id, data)

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in createNotification:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async getUserNotifications(userId: string, unreadOnly: boolean = false): Promise<ServiceResponse<NotificationData[]>> {
    try {
      let query = supabase
        .from('notifications')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })

      if (unreadOnly) {
        query = query.eq('read', false)
      }

      const { data, error } = await query

      if (error) {
        console.error('Error fetching notifications:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in getUserNotifications:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async markAsRead(notificationId: string): Promise<ServiceResponse<void>> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)

      if (error) {
        console.error('Error marking notification as read:', error)
        return createResponse(null, error.message)
      }

      return createResponse(null)
    } catch (error) {
      console.error('Unexpected error in markAsRead:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async markAllAsRead(userId: string): Promise<ServiceResponse<void>> {
    try {
      const { error } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', userId)
        .eq('read', false)

      if (error) {
        console.error('Error marking all notifications as read:', error)
        return createResponse(null, error.message)
      }

      return createResponse(null)
    } catch (error) {
      console.error('Unexpected error in markAllAsRead:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  // Real-time notifications
  private async sendRealtimeNotification(userId: string, notification: NotificationData): Promise<void> {
    try {
      await supabase
        .channel('notifications')
        .send({
          type: 'broadcast',
          event: 'notification',
          payload: {
            user_id: userId,
            notification
          }
        })
    } catch (error) {
      console.error('Error sending real-time notification:', error)
    }
  }

  subscribeToNotifications(userId: string, callback: (notification: NotificationData) => void) {
    const channel = supabase
      .channel('notifications')
      .on('broadcast', { event: 'notification' }, (payload) => {
        if (payload.payload.user_id === userId) {
          callback(payload.payload.notification)
        }
      })
      .subscribe()

    return () => {
      supabase.removeChannel(channel)
    }
  }

  // Email notifications (placeholder - integrate with your email service)
  async sendEmail(template: EmailTemplate): Promise<ServiceResponse<void>> {
    try {
      // This is a placeholder implementation
      // In production, integrate with services like:
      // - SendGrid
      // - Mailgun
      // - AWS SES
      // - Resend
      
      console.log('Email would be sent:', template)
      
      // For now, just log the email
      // In production, replace with actual email service call
      
      return createResponse(null)
    } catch (error) {
      console.error('Error sending email:', error)
      return createResponse(null, 'Failed to send email')
    }
  }

  // Push notifications (placeholder - integrate with your push service)
  async sendPushNotification(userId: string, payload: PushNotificationPayload): Promise<ServiceResponse<void>> {
    try {
      // This is a placeholder implementation
      // In production, integrate with services like:
      // - Firebase Cloud Messaging (FCM)
      // - Apple Push Notification Service (APNs)
      // - OneSignal
      // - Pusher
      
      console.log('Push notification would be sent:', { userId, payload })
      
      return createResponse(null)
    } catch (error) {
      console.error('Error sending push notification:', error)
      return createResponse(null, 'Failed to send push notification')
    }
  }

  // Convenience methods for common notification scenarios
  async notifyBidReceived(customerId: string, projectTitle: string, contractorName: string): Promise<void> {
    await this.createNotification({
      user_id: customerId,
      type: 'bid_received',
      title: 'New Bid Received',
      message: `${contractorName} submitted a bid for "${projectTitle}"`,
      read: false
    })

    await this.sendEmail({
      to: '', // Get user email
      subject: 'New Bid Received - RenovHub',
      html: `<p>You received a new bid for your project "${projectTitle}" from ${contractorName}.</p>`
    })
  }

  async notifyBidAccepted(contractorId: string, projectTitle: string): Promise<void> {
    await this.createNotification({
      user_id: contractorId,
      type: 'bid_accepted',
      title: 'Bid Accepted!',
      message: `Your bid for "${projectTitle}" has been accepted`,
      read: false
    })
  }

  async notifyProjectUpdate(userId: string, projectTitle: string, updateMessage: string): Promise<void> {
    await this.createNotification({
      user_id: userId,
      type: 'project_update',
      title: 'Project Update',
      message: `${projectTitle}: ${updateMessage}`,
      read: false
    })
  }

  async notifyNewMessage(userId: string, senderName: string, projectTitle: string): Promise<void> {
    await this.createNotification({
      user_id: userId,
      type: 'message_received',
      title: 'New Message',
      message: `${senderName} sent you a message about "${projectTitle}"`,
      read: false
    })
  }

  async notifyPaymentReceived(contractorId: string, amount: number, projectTitle: string): Promise<void> {
    await this.createNotification({
      user_id: contractorId,
      type: 'payment_received',
      title: 'Payment Received',
      message: `You received $${amount} for "${projectTitle}"`,
      read: false
    })
  }

  async notifyReviewReceived(contractorId: string, rating: number, projectTitle: string): Promise<void> {
    await this.createNotification({
      user_id: contractorId,
      type: 'review_received',
      title: 'New Review',
      message: `You received a ${rating}-star review for "${projectTitle}"`,
      read: false
    })
  }
}

// Export singleton instance
export const notificationService = new NotificationService()
