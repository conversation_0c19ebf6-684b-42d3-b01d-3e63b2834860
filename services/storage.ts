import { supabase } from '@/lib/supabase'
import { ServiceResponse, createResponse } from './database'

export interface UploadOptions {
  bucket: string
  folder?: string
  fileName?: string
  maxSize?: number // in bytes
  allowedTypes?: string[]
  quality?: number // for image compression (0-1)
}

export interface UploadResult {
  url: string
  path: string
  fileName: string
  size: number
  type: string
}

export class StorageService {
  private readonly defaultBuckets = {
    avatars: 'avatars',
    projects: 'project-images',
    documents: 'documents',
    portfolios: 'portfolio-images'
  }

  private readonly defaultOptions: Partial<UploadOptions> = {
    maxSize: 10 * 1024 * 1024, // 10MB
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'application/pdf'],
    quality: 0.8
  }

  async uploadFile(
    file: File,
    options: UploadOptions
  ): Promise<ServiceResponse<UploadResult>> {
    try {
      // Validate file
      const validation = this.validateFile(file, options)
      if (!validation.success) {
        return createResponse(null, validation.error!)
      }

      // Generate file path
      const filePath = this.generateFilePath(file, options)

      // Compress image if needed
      const processedFile = await this.processFile(file, options)

      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from(options.bucket)
        .upload(filePath, processedFile, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        console.error('Storage upload error:', error)
        return createResponse(null, error.message)
      }

      // Get public URL
      const { data: urlData } = supabase.storage
        .from(options.bucket)
        .getPublicUrl(data.path)

      const result: UploadResult = {
        url: urlData.publicUrl,
        path: data.path,
        fileName: file.name,
        size: processedFile.size,
        type: file.type
      }

      return createResponse(result)
    } catch (error) {
      console.error('Unexpected error in uploadFile:', error)
      return createResponse(null, 'An unexpected error occurred during upload')
    }
  }

  async uploadMultipleFiles(
    files: File[],
    options: UploadOptions
  ): Promise<ServiceResponse<UploadResult[]>> {
    try {
      const results: UploadResult[] = []
      const errors: string[] = []

      for (const file of files) {
        const result = await this.uploadFile(file, options)
        if (result.success && result.data) {
          results.push(result.data)
        } else {
          errors.push(`${file.name}: ${result.error}`)
        }
      }

      if (errors.length > 0 && results.length === 0) {
        return createResponse(null, `All uploads failed: ${errors.join(', ')}`)
      }

      if (errors.length > 0) {
        console.warn('Some uploads failed:', errors)
      }

      return createResponse(results)
    } catch (error) {
      console.error('Unexpected error in uploadMultipleFiles:', error)
      return createResponse(null, 'An unexpected error occurred during batch upload')
    }
  }

  async deleteFile(bucket: string, path: string): Promise<ServiceResponse<void>> {
    try {
      const { error } = await supabase.storage
        .from(bucket)
        .remove([path])

      if (error) {
        console.error('Storage delete error:', error)
        return createResponse(null, error.message)
      }

      return createResponse(null)
    } catch (error) {
      console.error('Unexpected error in deleteFile:', error)
      return createResponse(null, 'An unexpected error occurred during deletion')
    }
  }

  async getSignedUrl(
    bucket: string,
    path: string,
    expiresIn: number = 3600
  ): Promise<ServiceResponse<string>> {
    try {
      const { data, error } = await supabase.storage
        .from(bucket)
        .createSignedUrl(path, expiresIn)

      if (error) {
        console.error('Error creating signed URL:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data.signedUrl)
    } catch (error) {
      console.error('Unexpected error in getSignedUrl:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  private validateFile(file: File, options: UploadOptions): ServiceResponse<void> {
    const opts = { ...this.defaultOptions, ...options }

    // Check file size
    if (opts.maxSize && file.size > opts.maxSize) {
      const maxSizeMB = opts.maxSize / (1024 * 1024)
      return createResponse(null, `File size exceeds ${maxSizeMB}MB limit`)
    }

    // Check file type
    if (opts.allowedTypes && !opts.allowedTypes.includes(file.type)) {
      return createResponse(null, `File type ${file.type} is not allowed`)
    }

    return createResponse(null)
  }

  private generateFilePath(file: File, options: UploadOptions): string {
    const timestamp = Date.now()
    const randomString = Math.random().toString(36).substring(2, 15)
    const extension = file.name.split('.').pop()
    
    const fileName = options.fileName || `${timestamp}_${randomString}.${extension}`
    const folder = options.folder || 'uploads'
    
    return `${folder}/${fileName}`
  }

  private async processFile(file: File, options: UploadOptions): Promise<File> {
    // For now, return the original file
    // In the future, you could add image compression here
    return file
  }

  // Convenience methods for common upload scenarios
  async uploadAvatar(file: File, userId: string): Promise<ServiceResponse<UploadResult>> {
    return this.uploadFile(file, {
      bucket: this.defaultBuckets.avatars,
      folder: `users/${userId}`,
      fileName: `avatar_${Date.now()}.${file.name.split('.').pop()}`,
      maxSize: 5 * 1024 * 1024, // 5MB
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
    })
  }

  async uploadProjectImages(files: File[], projectId: string): Promise<ServiceResponse<UploadResult[]>> {
    return this.uploadMultipleFiles(files, {
      bucket: this.defaultBuckets.projects,
      folder: `projects/${projectId}`,
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
    })
  }

  async uploadDocument(file: File, userId: string, type: 'license' | 'insurance' | 'certification'): Promise<ServiceResponse<UploadResult>> {
    return this.uploadFile(file, {
      bucket: this.defaultBuckets.documents,
      folder: `users/${userId}/${type}`,
      maxSize: 20 * 1024 * 1024, // 20MB
      allowedTypes: ['application/pdf', 'image/jpeg', 'image/png']
    })
  }

  async uploadPortfolioImages(files: File[], contractorId: string): Promise<ServiceResponse<UploadResult[]>> {
    return this.uploadMultipleFiles(files, {
      bucket: this.defaultBuckets.portfolios,
      folder: `contractors/${contractorId}`,
      maxSize: 10 * 1024 * 1024, // 10MB
      allowedTypes: ['image/jpeg', 'image/png', 'image/webp']
    })
  }
}

// Export singleton instance
export const storageService = new StorageService()
