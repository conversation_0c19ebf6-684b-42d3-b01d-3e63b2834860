import { apiClient } from './api'
import { ApiResponse } from '@/types'

export interface FavoriteItem {
  id: string
  userId: string
  itemId: string
  itemType: 'contractor' | 'project'
  createdAt: string
}

export interface LikeItem {
  id: string
  userId: string
  itemId: string
  itemType: 'contractor' | 'project' | 'review'
  createdAt: string
}

export class FavoritesService {
  // Bookmark/Favorite functionality
  async addFavorite(itemId: string, itemType: 'contractor' | 'project'): Promise<ApiResponse<FavoriteItem>> {
    return apiClient.post<FavoriteItem>('/favorites', {
      itemId,
      itemType
    })
  }

  async removeFavorite(itemId: string, itemType: 'contractor' | 'project'): Promise<ApiResponse<void>> {
    return apiClient.delete(`/favorites/${itemType}/${itemId}`)
  }

  async getFavorites(itemType?: 'contractor' | 'project'): Promise<ApiResponse<FavoriteItem[]>> {
    const params = itemType ? { type: itemType } : {}
    return apiClient.get<FavoriteItem[]>('/favorites', params)
  }

  async isFavorite(itemId: string, itemType: 'contractor' | 'project'): Promise<ApiResponse<boolean>> {
    return apiClient.get<boolean>(`/favorites/${itemType}/${itemId}/check`)
  }

  // Like functionality
  async addLike(itemId: string, itemType: 'contractor' | 'project' | 'review'): Promise<ApiResponse<LikeItem>> {
    return apiClient.post<LikeItem>('/likes', {
      itemId,
      itemType
    })
  }

  async removeLike(itemId: string, itemType: 'contractor' | 'project' | 'review'): Promise<ApiResponse<void>> {
    return apiClient.delete(`/likes/${itemType}/${itemId}`)
  }

  async getLikes(itemType?: 'contractor' | 'project' | 'review'): Promise<ApiResponse<LikeItem[]>> {
    const params = itemType ? { type: itemType } : {}
    return apiClient.get<LikeItem[]>('/likes', params)
  }

  async isLiked(itemId: string, itemType: 'contractor' | 'project' | 'review'): Promise<ApiResponse<boolean>> {
    return apiClient.get<boolean>(`/likes/${itemType}/${itemId}/check`)
  }

  async getLikeCount(itemId: string, itemType: 'contractor' | 'project' | 'review'): Promise<ApiResponse<number>> {
    return apiClient.get<number>(`/likes/${itemType}/${itemId}/count`)
  }

  // Bulk operations
  async getFavoriteStatus(items: Array<{id: string, type: 'contractor' | 'project'}>): Promise<ApiResponse<Record<string, boolean>>> {
    return apiClient.post<Record<string, boolean>>('/favorites/bulk-check', { items })
  }

  async getLikeStatus(items: Array<{id: string, type: 'contractor' | 'project' | 'review'}>): Promise<ApiResponse<Record<string, boolean>>> {
    return apiClient.post<Record<string, boolean>>('/likes/bulk-check', { items })
  }

  // Local storage fallback for offline functionality
  private getLocalFavorites(): string[] {
    if (typeof window === 'undefined') return []
    const stored = localStorage.getItem('favorites')
    return stored ? JSON.parse(stored) : []
  }

  private setLocalFavorites(favorites: string[]): void {
    if (typeof window === 'undefined') return
    localStorage.setItem('favorites', JSON.stringify(favorites))
  }

  private getLocalLikes(): string[] {
    if (typeof window === 'undefined') return []
    const stored = localStorage.getItem('likes')
    return stored ? JSON.parse(stored) : []
  }

  private setLocalLikes(likes: string[]): void {
    if (typeof window === 'undefined') return
    localStorage.setItem('likes', JSON.stringify(likes))
  }

  // Offline-capable methods
  toggleFavoriteLocal(itemId: string): boolean {
    const favorites = this.getLocalFavorites()
    const index = favorites.indexOf(itemId)
    
    if (index > -1) {
      favorites.splice(index, 1)
      this.setLocalFavorites(favorites)
      return false
    } else {
      favorites.push(itemId)
      this.setLocalFavorites(favorites)
      return true
    }
  }

  isFavoriteLocal(itemId: string): boolean {
    const favorites = this.getLocalFavorites()
    return favorites.includes(itemId)
  }

  toggleLikeLocal(itemId: string): boolean {
    const likes = this.getLocalLikes()
    const index = likes.indexOf(itemId)
    
    if (index > -1) {
      likes.splice(index, 1)
      this.setLocalLikes(likes)
      return false
    } else {
      likes.push(itemId)
      this.setLocalLikes(likes)
      return true
    }
  }

  isLikedLocal(itemId: string): boolean {
    const likes = this.getLocalLikes()
    return likes.includes(itemId)
  }

  // Sync local data with server when online
  async syncLocalData(): Promise<void> {
    try {
      const localFavorites = this.getLocalFavorites()
      const localLikes = this.getLocalLikes()

      // Sync favorites
      for (const itemId of localFavorites) {
        await this.addFavorite(itemId, 'contractor') // Default to contractor, could be enhanced
      }

      // Sync likes
      for (const itemId of localLikes) {
        await this.addLike(itemId, 'contractor') // Default to contractor, could be enhanced
      }

      // Clear local storage after successful sync
      this.setLocalFavorites([])
      this.setLocalLikes([])
    } catch (error) {
      console.error('Failed to sync local favorites/likes:', error)
    }
  }
}

export const favoritesService = new FavoritesService()
