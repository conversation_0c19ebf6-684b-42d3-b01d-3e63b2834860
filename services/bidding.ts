import { supabase } from '@/lib/supabase'
import { ServiceResponse, createResponse } from './database'
import { notificationService } from './notifications'
import { messagingService } from './messaging'
import type { Tables } from '@/lib/supabase'

export interface BidData {
  id?: string
  project_id: string
  contractor_id: string
  status: 'pending' | 'submitted' | 'accepted' | 'rejected' | 'withdrawn'
  amount: number
  currency: string
  timeline: {
    startDate?: string
    endDate?: string
    duration: number
    milestones?: BidMilestone[]
  }
  description: string
  breakdown: BidBreakdownItem[]
  terms: string
  warranty: {
    duration: number
    coverage: string
    terms: string
  }
  questions?: BidQuestion[]
  attachments?: BidAttachment[]
  submitted_at?: string
  expires_at: string
}

export interface BidBreakdownItem {
  category: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

export interface BidMilestone {
  title: string
  description: string
  duration: number
  payment: number
}

export interface BidQuestion {
  id: string
  question: string
  answer?: string
  askedAt: string
  answeredAt?: string
}

export interface BidAttachment {
  id: string
  name: string
  url: string
  type: string
  size: number
}

export interface BidWithDetails extends BidData {
  contractor?: {
    id: string
    business_name: string
    rating_average: number
    rating_count: number
    user: {
      name: string
      avatar_url?: string
      verified: boolean
    }
  }
  project?: {
    id: string
    title: string
    description: string
    customer: {
      name: string
      avatar_url?: string
    }
  }
}

export class BiddingService {
  // Bid creation and management
  async createBid(bidData: Omit<BidData, 'id' | 'submitted_at' | 'status'>): Promise<ServiceResponse<BidData>> {
    try {
      // Check if contractor already has a bid for this project
      const { data: existingBid } = await supabase
        .from('bids')
        .select('id')
        .eq('project_id', bidData.project_id)
        .eq('contractor_id', bidData.contractor_id)
        .single()

      if (existingBid) {
        return createResponse(null, 'You have already submitted a bid for this project')
      }

      // Create the bid
      const { data, error } = await supabase
        .from('bids')
        .insert({
          project_id: bidData.project_id,
          contractor_id: bidData.contractor_id,
          status: 'submitted',
          amount: bidData.amount,
          currency: bidData.currency || 'USD',
          timeline: bidData.timeline,
          description: bidData.description,
          breakdown: bidData.breakdown,
          terms: bidData.terms,
          warranty: bidData.warranty,
          questions: bidData.questions || [],
          attachments: bidData.attachments || [],
          expires_at: bidData.expires_at,
          submitted_at: new Date().toISOString()
        })
        .select()
        .single()

      if (error) {
        console.error('Error creating bid:', error)
        return createResponse(null, error.message)
      }

      // Notify project owner
      await this.notifyBidSubmitted(bidData.project_id, bidData.contractor_id)

      // Create conversation if it doesn't exist
      await this.createBidConversation(bidData.project_id, bidData.contractor_id)

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in createBid:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async updateBid(bidId: string, updates: Partial<BidData>): Promise<ServiceResponse<BidData>> {
    try {
      const { data, error } = await supabase
        .from('bids')
        .update({
          ...updates,
          updated_at: new Date().toISOString()
        })
        .eq('id', bidId)
        .select()
        .single()

      if (error) {
        console.error('Error updating bid:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in updateBid:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async withdrawBid(bidId: string, contractorId: string): Promise<ServiceResponse<void>> {
    try {
      const { error } = await supabase
        .from('bids')
        .update({ 
          status: 'withdrawn',
          updated_at: new Date().toISOString()
        })
        .eq('id', bidId)
        .eq('contractor_id', contractorId)

      if (error) {
        console.error('Error withdrawing bid:', error)
        return createResponse(null, error.message)
      }

      return createResponse(null)
    } catch (error) {
      console.error('Unexpected error in withdrawBid:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async acceptBid(bidId: string, customerId: string): Promise<ServiceResponse<void>> {
    try {
      // Start a transaction-like operation
      const { data: bid, error: bidError } = await supabase
        .from('bids')
        .select('project_id, contractor_id')
        .eq('id', bidId)
        .single()

      if (bidError || !bid) {
        return createResponse(null, 'Bid not found')
      }

      // Verify the customer owns the project
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('customer_id')
        .eq('id', bid.project_id)
        .eq('customer_id', customerId)
        .single()

      if (projectError || !project) {
        return createResponse(null, 'Project not found or access denied')
      }

      // Accept the bid
      const { error: acceptError } = await supabase
        .from('bids')
        .update({ 
          status: 'accepted',
          updated_at: new Date().toISOString()
        })
        .eq('id', bidId)

      if (acceptError) {
        console.error('Error accepting bid:', acceptError)
        return createResponse(null, acceptError.message)
      }

      // Reject all other bids for this project
      await supabase
        .from('bids')
        .update({ 
          status: 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('project_id', bid.project_id)
        .neq('id', bidId)

      // Update project with selected contractor
      await supabase
        .from('projects')
        .update({
          contractor_id: bid.contractor_id,
          selected_bid_id: bidId,
          status: 'in-progress',
          updated_at: new Date().toISOString()
        })
        .eq('id', bid.project_id)

      // Notify contractor
      await this.notifyBidAccepted(bid.contractor_id, bid.project_id)

      return createResponse(null)
    } catch (error) {
      console.error('Unexpected error in acceptBid:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async rejectBid(bidId: string, customerId: string, reason?: string): Promise<ServiceResponse<void>> {
    try {
      const { data: bid, error: bidError } = await supabase
        .from('bids')
        .select('project_id, contractor_id')
        .eq('id', bidId)
        .single()

      if (bidError || !bid) {
        return createResponse(null, 'Bid not found')
      }

      // Verify the customer owns the project
      const { data: project, error: projectError } = await supabase
        .from('projects')
        .select('customer_id')
        .eq('id', bid.project_id)
        .eq('customer_id', customerId)
        .single()

      if (projectError || !project) {
        return createResponse(null, 'Project not found or access denied')
      }

      // Reject the bid
      const { error } = await supabase
        .from('bids')
        .update({ 
          status: 'rejected',
          updated_at: new Date().toISOString()
        })
        .eq('id', bidId)

      if (error) {
        console.error('Error rejecting bid:', error)
        return createResponse(null, error.message)
      }

      // Notify contractor
      await this.notifyBidRejected(bid.contractor_id, bid.project_id, reason)

      return createResponse(null)
    } catch (error) {
      console.error('Unexpected error in rejectBid:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  // Bid retrieval
  async getProjectBids(projectId: string): Promise<ServiceResponse<BidWithDetails[]>> {
    try {
      const { data, error } = await supabase
        .from('bids')
        .select(`
          *,
          contractors!contractor_id (
            id,
            business_name,
            rating_average,
            rating_count,
            users!user_id (
              name,
              avatar_url,
              verified
            )
          )
        `)
        .eq('project_id', projectId)
        .order('submitted_at', { ascending: false })

      if (error) {
        console.error('Error fetching project bids:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in getProjectBids:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async getContractorBids(contractorId: string): Promise<ServiceResponse<BidWithDetails[]>> {
    try {
      const { data, error } = await supabase
        .from('bids')
        .select(`
          *,
          projects!project_id (
            id,
            title,
            description,
            status,
            users!customer_id (
              name,
              avatar_url
            )
          )
        `)
        .eq('contractor_id', contractorId)
        .order('submitted_at', { ascending: false })

      if (error) {
        console.error('Error fetching contractor bids:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data || [])
    } catch (error) {
      console.error('Unexpected error in getContractorBids:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  async getBid(bidId: string): Promise<ServiceResponse<BidWithDetails>> {
    try {
      const { data, error } = await supabase
        .from('bids')
        .select(`
          *,
          contractors!contractor_id (
            id,
            business_name,
            rating_average,
            rating_count,
            users!user_id (
              name,
              avatar_url,
              verified
            )
          ),
          projects!project_id (
            id,
            title,
            description,
            users!customer_id (
              name,
              avatar_url
            )
          )
        `)
        .eq('id', bidId)
        .single()

      if (error) {
        console.error('Error fetching bid:', error)
        return createResponse(null, error.message)
      }

      return createResponse(data)
    } catch (error) {
      console.error('Unexpected error in getBid:', error)
      return createResponse(null, 'An unexpected error occurred')
    }
  }

  // Private helper methods
  private async notifyBidSubmitted(projectId: string, contractorId: string): Promise<void> {
    try {
      const { data: project } = await supabase
        .from('projects')
        .select('title, customer_id')
        .eq('id', projectId)
        .single()

      const { data: contractor } = await supabase
        .from('contractors')
        .select('business_name')
        .eq('id', contractorId)
        .single()

      if (project && contractor) {
        await notificationService.notifyBidReceived(
          project.customer_id,
          project.title,
          contractor.business_name
        )
      }
    } catch (error) {
      console.error('Error notifying bid submitted:', error)
    }
  }

  private async notifyBidAccepted(contractorId: string, projectId: string): Promise<void> {
    try {
      const { data: project } = await supabase
        .from('projects')
        .select('title')
        .eq('id', projectId)
        .single()

      const { data: contractor } = await supabase
        .from('contractors')
        .select('user_id')
        .eq('id', contractorId)
        .single()

      if (project && contractor) {
        await notificationService.notifyBidAccepted(
          contractor.user_id,
          project.title
        )
      }
    } catch (error) {
      console.error('Error notifying bid accepted:', error)
    }
  }

  private async notifyBidRejected(contractorId: string, projectId: string, reason?: string): Promise<void> {
    try {
      const { data: project } = await supabase
        .from('projects')
        .select('title')
        .eq('id', projectId)
        .single()

      const { data: contractor } = await supabase
        .from('contractors')
        .select('user_id')
        .eq('id', contractorId)
        .single()

      if (project && contractor) {
        await notificationService.createNotification({
          user_id: contractor.user_id,
          type: 'bid_rejected',
          title: 'Bid Not Selected',
          message: `Your bid for "${project.title}" was not selected${reason ? `: ${reason}` : ''}`,
          read: false
        })
      }
    } catch (error) {
      console.error('Error notifying bid rejected:', error)
    }
  }

  private async createBidConversation(projectId: string, contractorId: string): Promise<void> {
    try {
      const { data: project } = await supabase
        .from('projects')
        .select('customer_id')
        .eq('id', projectId)
        .single()

      const { data: contractor } = await supabase
        .from('contractors')
        .select('user_id')
        .eq('id', contractorId)
        .single()

      if (project && contractor) {
        await messagingService.createConversation(projectId, [
          project.customer_id,
          contractor.user_id
        ])
      }
    } catch (error) {
      console.error('Error creating bid conversation:', error)
    }
  }
}

// Export singleton instance
export const biddingService = new BiddingService()
