import { supabase } from '@/lib/supabase'
import type { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js'
import type { Tables } from '@/lib/supabase'

// Real-time event types
export type RealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE'

export interface RealtimePayload<T = any> {
  eventType: RealtimeEvent
  new: T | null
  old: T | null
  table: string
}

// Real-time service for managing subscriptions
export class RealtimeService {
  private channels: Map<string, RealtimeChannel> = new Map()
  private callbacks: Map<string, Set<(payload: RealtimePayload) => void>> = new Map()

  // Subscribe to table changes
  subscribeToTable<T extends keyof Tables>(
    table: T,
    callback: (payload: RealtimePayload<Tables[T]>) => void,
    filter?: string
  ): () => void {
    const channelName = filter ? `${table}_${filter}` : table
    
    // Add callback to the set
    if (!this.callbacks.has(channelName)) {
      this.callbacks.set(channelName, new Set())
    }
    this.callbacks.get(channelName)!.add(callback)

    // Create channel if it doesn't exist
    if (!this.channels.has(channelName)) {
      const channel = supabase
        .channel(channelName)
        .on(
          'postgres_changes',
          {
            event: '*',
            schema: 'public',
            table: table as string,
            filter,
          },
          (payload: RealtimePostgresChangesPayload<any>) => {
            const realtimePayload: RealtimePayload<Tables[T]> = {
              eventType: payload.eventType as RealtimeEvent,
              new: payload.new as Tables[T] | null,
              old: payload.old as Tables[T] | null,
              table: table as string,
            }

            // Call all callbacks for this channel
            const callbacks = this.callbacks.get(channelName)
            if (callbacks) {
              callbacks.forEach(cb => cb(realtimePayload))
            }
          }
        )
        .subscribe()

      this.channels.set(channelName, channel)
    }

    // Return unsubscribe function
    return () => {
      const callbacks = this.callbacks.get(channelName)
      if (callbacks) {
        callbacks.delete(callback)
        
        // If no more callbacks, remove the channel
        if (callbacks.size === 0) {
          const channel = this.channels.get(channelName)
          if (channel) {
            supabase.removeChannel(channel)
            this.channels.delete(channelName)
            this.callbacks.delete(channelName)
          }
        }
      }
    }
  }

  // Subscribe to messages for a specific conversation
  subscribeToMessages(
    conversationId: string,
    callback: (payload: RealtimePayload<Tables['messages']>) => void
  ): () => void {
    return this.subscribeToTable(
      'messages',
      callback,
      `conversation_id=eq.${conversationId}`
    )
  }

  // Subscribe to project updates
  subscribeToProject(
    projectId: string,
    callback: (payload: RealtimePayload<Tables['projects']>) => void
  ): () => void {
    return this.subscribeToTable(
      'projects',
      callback,
      `id=eq.${projectId}`
    )
  }

  // Subscribe to bids for a specific project
  subscribeToProjectBids(
    projectId: string,
    callback: (payload: RealtimePayload<Tables['bids']>) => void
  ): () => void {
    return this.subscribeToTable(
      'bids',
      callback,
      `project_id=eq.${projectId}`
    )
  }

  // Subscribe to contractor bids
  subscribeToContractorBids(
    contractorId: string,
    callback: (payload: RealtimePayload<Tables['bids']>) => void
  ): () => void {
    return this.subscribeToTable(
      'bids',
      callback,
      `contractor_id=eq.${contractorId}`
    )
  }

  // Subscribe to user conversations
  subscribeToUserConversations(
    userId: string,
    callback: (payload: RealtimePayload<Tables['conversations']>) => void
  ): () => void {
    return this.subscribeToTable(
      'conversations',
      callback,
      `participants.cs.{${userId}}`
    )
  }

  // Subscribe to reviews for a user
  subscribeToUserReviews(
    userId: string,
    callback: (payload: RealtimePayload<Tables['reviews']>) => void
  ): () => void {
    return this.subscribeToTable(
      'reviews',
      callback,
      `reviewee_id=eq.${userId}`
    )
  }

  // Subscribe to payments for a user
  subscribeToUserPayments(
    userId: string,
    callback: (payload: RealtimePayload<Tables['payments']>) => void
  ): () => void {
    const unsubscribePayer = this.subscribeToTable(
      'payments',
      callback,
      `payer_id=eq.${userId}`
    )
    
    const unsubscribePayee = this.subscribeToTable(
      'payments',
      callback,
      `payee_id=eq.${userId}`
    )

    return () => {
      unsubscribePayer()
      unsubscribePayee()
    }
  }

  // Subscribe to all active projects (for contractors)
  subscribeToActiveProjects(
    callback: (payload: RealtimePayload<Tables['projects']>) => void
  ): () => void {
    return this.subscribeToTable(
      'projects',
      callback,
      'status=eq.active'
    )
  }

  // Cleanup all subscriptions
  cleanup(): void {
    this.channels.forEach(channel => {
      supabase.removeChannel(channel)
    })
    this.channels.clear()
    this.callbacks.clear()
  }

  // Get connection status
  getConnectionStatus(): 'SUBSCRIBED' | 'TIMED_OUT' | 'CLOSED' | 'CHANNEL_ERROR' {
    // Return the status of the first channel, or 'CLOSED' if no channels
    const firstChannel = Array.from(this.channels.values())[0]
    return firstChannel?.state || 'CLOSED'
  }
}

// Notification service for handling real-time notifications
export class NotificationService {
  private realtimeService: RealtimeService
  private notificationCallbacks: Set<(notification: any) => void> = new Set()

  constructor(realtimeService: RealtimeService) {
    this.realtimeService = realtimeService
  }

  // Subscribe to notifications for a user
  subscribeToNotifications(
    userId: string,
    callback: (notification: any) => void
  ): () => void {
    this.notificationCallbacks.add(callback)

    // Subscribe to various events that should trigger notifications
    const unsubscribeMessages = this.realtimeService.subscribeToUserConversations(
      userId,
      (payload) => {
        if (payload.eventType === 'UPDATE' && payload.new?.unread_count > 0) {
          callback({
            type: 'new_message',
            title: 'New Message',
            message: 'You have a new message',
            data: payload.new,
          })
        }
      }
    )

    const unsubscribeBids = this.realtimeService.subscribeToTable(
      'bids',
      (payload) => {
        // Notify project owners of new bids
        if (payload.eventType === 'INSERT') {
          callback({
            type: 'new_bid',
            title: 'New Bid Received',
            message: 'You received a new bid on your project',
            data: payload.new,
          })
        }
      }
    )

    const unsubscribeReviews = this.realtimeService.subscribeToUserReviews(
      userId,
      (payload) => {
        if (payload.eventType === 'INSERT') {
          callback({
            type: 'new_review',
            title: 'New Review',
            message: 'You received a new review',
            data: payload.new,
          })
        }
      }
    )

    const unsubscribePayments = this.realtimeService.subscribeToUserPayments(
      userId,
      (payload) => {
        if (payload.eventType === 'UPDATE' && payload.new?.status === 'completed') {
          callback({
            type: 'payment_completed',
            title: 'Payment Completed',
            message: 'Your payment has been processed',
            data: payload.new,
          })
        }
      }
    )

    // Return unsubscribe function
    return () => {
      this.notificationCallbacks.delete(callback)
      unsubscribeMessages()
      unsubscribeBids()
      unsubscribeReviews()
      unsubscribePayments()
    }
  }

  // Send a notification to all subscribers
  notify(notification: any): void {
    this.notificationCallbacks.forEach(callback => callback(notification))
  }
}

// Create singleton instances
export const realtimeService = new RealtimeService()
export const notificationService = new NotificationService(realtimeService)

// Cleanup function for when the app unmounts
export const cleanupRealtime = () => {
  realtimeService.cleanup()
}
