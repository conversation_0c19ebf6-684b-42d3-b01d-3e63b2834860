import { apiClient, cachedRequest } from './api'
import { Project, ProjectStatus, ProjectCategory, SearchFilters, SearchResult, ApiResponse } from '@/types'

export class ProjectService {
  // Get all projects for a user
  async getProjects(filters?: Partial<SearchFilters>): Promise<ApiResponse<SearchResult<Project>>> {
    return cachedRequest(
      `projects-${JSON.stringify(filters)}`,
      () => apiClient.get<SearchResult<Project>>('/projects', filters),
      300000 // 5 minutes cache
    )
  }

  // Get a specific project
  async getProject(id: string): Promise<ApiResponse<Project>> {
    return cachedRequest(
      `project-${id}`,
      () => apiClient.get<Project>(`/projects/${id}`),
      300000
    )
  }

  // Create a new project
  async createProject(projectData: Partial<Project>): Promise<ApiResponse<Project>> {
    const response = await apiClient.post<Project>('/projects', projectData)
    
    // Clear related caches
    if (response.success) {
      this.clearProjectCaches()
    }
    
    return response
  }

  // Update a project
  async updateProject(id: string, updates: Partial<Project>): Promise<ApiResponse<Project>> {
    const response = await apiClient.patch<Project>(`/projects/${id}`, updates)
    
    // Clear related caches
    if (response.success) {
      this.clearProjectCaches()
      this.clearProjectCache(id)
    }
    
    return response
  }

  // Delete a project
  async deleteProject(id: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete<void>(`/projects/${id}`)
    
    // Clear related caches
    if (response.success) {
      this.clearProjectCaches()
      this.clearProjectCache(id)
    }
    
    return response
  }

  // Update project status
  async updateProjectStatus(id: string, status: ProjectStatus): Promise<ApiResponse<Project>> {
    return this.updateProject(id, { status })
  }

  // Upload project photos
  async uploadProjectPhoto(projectId: string, file: File, caption?: string): Promise<ApiResponse<{ url: string }>> {
    const response = await apiClient.upload<{ url: string }>(
      `/projects/${projectId}/photos`,
      file,
      { caption }
    )
    
    // Clear project cache
    if (response.success) {
      this.clearProjectCache(projectId)
    }
    
    return response
  }

  // Delete project photo
  async deleteProjectPhoto(projectId: string, photoId: string): Promise<ApiResponse<void>> {
    const response = await apiClient.delete<void>(`/projects/${projectId}/photos/${photoId}`)
    
    // Clear project cache
    if (response.success) {
      this.clearProjectCache(projectId)
    }
    
    return response
  }

  // Search projects
  async searchProjects(query: string, filters?: Partial<SearchFilters>): Promise<ApiResponse<SearchResult<Project>>> {
    return apiClient.get<SearchResult<Project>>('/projects/search', {
      q: query,
      ...filters
    })
  }

  // Get project categories
  async getProjectCategories(): Promise<ApiResponse<ProjectCategory[]>> {
    return cachedRequest(
      'project-categories',
      () => apiClient.get<ProjectCategory[]>('/projects/categories'),
      3600000 // 1 hour cache
    )
  }

  // Get project statistics
  async getProjectStats(userId?: string): Promise<ApiResponse<ProjectStats>> {
    const endpoint = userId ? `/projects/stats?userId=${userId}` : '/projects/stats'
    return apiClient.get<ProjectStats>(endpoint)
  }

  // Get recent projects
  async getRecentProjects(limit: number = 10): Promise<ApiResponse<Project[]>> {
    return cachedRequest(
      `recent-projects-${limit}`,
      () => apiClient.get<Project[]>('/projects/recent', { limit }),
      300000
    )
  }

  // Get featured projects
  async getFeaturedProjects(limit: number = 6): Promise<ApiResponse<Project[]>> {
    return cachedRequest(
      `featured-projects-${limit}`,
      () => apiClient.get<Project[]>('/projects/featured', { limit }),
      600000 // 10 minutes cache
    )
  }

  // Get projects by location
  async getProjectsByLocation(location: string, radius?: number): Promise<ApiResponse<Project[]>> {
    return apiClient.get<Project[]>('/projects/by-location', {
      location,
      radius: radius || 50
    })
  }

  // Get project timeline
  async getProjectTimeline(projectId: string): Promise<ApiResponse<ProjectTimelineEvent[]>> {
    return cachedRequest(
      `project-timeline-${projectId}`,
      () => apiClient.get<ProjectTimelineEvent[]>(`/projects/${projectId}/timeline`),
      300000
    )
  }

  // Add project milestone
  async addProjectMilestone(projectId: string, milestone: Partial<Milestone>): Promise<ApiResponse<Milestone>> {
    const response = await apiClient.post<Milestone>(`/projects/${projectId}/milestones`, milestone)
    
    // Clear project cache
    if (response.success) {
      this.clearProjectCache(projectId)
    }
    
    return response
  }

  // Update project milestone
  async updateProjectMilestone(
    projectId: string, 
    milestoneId: string, 
    updates: Partial<Milestone>
  ): Promise<ApiResponse<Milestone>> {
    const response = await apiClient.patch<Milestone>(
      `/projects/${projectId}/milestones/${milestoneId}`,
      updates
    )
    
    // Clear project cache
    if (response.success) {
      this.clearProjectCache(projectId)
    }
    
    return response
  }

  // Complete project milestone
  async completeMilestone(projectId: string, milestoneId: string): Promise<ApiResponse<Milestone>> {
    return this.updateProjectMilestone(projectId, milestoneId, {
      status: 'completed',
      completedAt: new Date(),
      progress: 100
    })
  }

  // Get project recommendations
  async getProjectRecommendations(projectId: string): Promise<ApiResponse<ProjectRecommendation[]>> {
    return apiClient.get<ProjectRecommendation[]>(`/projects/${projectId}/recommendations`)
  }

  // Cache management
  private clearProjectCache(projectId: string): void {
    const { requestCache } = require('./api')
    requestCache.delete(`project-${projectId}`)
    requestCache.delete(`project-timeline-${projectId}`)
  }

  private clearProjectCaches(): void {
    const { requestCache } = require('./api')
    // Clear all project-related caches
    const keys = ['projects-', 'recent-projects-', 'featured-projects-']
    keys.forEach(key => {
      // Note: In a real implementation, you'd want a more sophisticated cache clearing mechanism
      requestCache.clear()
    })
  }
}

// Additional types for project service
export interface ProjectStats {
  total: number
  active: number
  completed: number
  draft: number
  averageBudget: number
  averageDuration: number
  categoryBreakdown: Record<ProjectCategory, number>
  monthlyTrend: MonthlyProjectData[]
}

export interface MonthlyProjectData {
  month: string
  count: number
  budget: number
}

export interface ProjectTimelineEvent {
  id: string
  type: 'created' | 'updated' | 'bid-received' | 'contractor-selected' | 'milestone-completed' | 'completed'
  title: string
  description: string
  timestamp: Date
  userId: string
  user: {
    name: string
    avatar?: string
  }
  metadata?: Record<string, any>
}

export interface ProjectRecommendation {
  type: 'contractor' | 'material' | 'timeline' | 'budget'
  title: string
  description: string
  confidence: number
  data: any
}

// Import types that might be missing
interface Milestone {
  id: string
  title: string
  description: string
  status: 'pending' | 'in-progress' | 'completed' | 'delayed'
  dueDate: Date
  completedAt?: Date
  progress: number
  dependencies: string[]
}

// Create singleton instance
export const projectService = new ProjectService()
