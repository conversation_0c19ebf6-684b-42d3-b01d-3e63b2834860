"use client"

import { createContext, use<PERSON>ontext, useState, useEffect, ReactNode } from "react"
import { useRouter } from "next/navigation"
import { authService } from '@/services/auth'
import { User } from '@/types'
import { supabase } from '@/lib/supabase'
import { notificationService } from '@/services/realtime'

export type UserRole = "customer" | "pro"

interface UserContextType {
  user: User | null
  setUser: (user: User | null) => void
  isAuthenticated: boolean
  switchRole: (role: UserRole) => void
  logout: () => Promise<void>
  loading: boolean
  session: any
  notifications: any[]
  markNotificationAsRead: (id: string) => void
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const [session, setSession] = useState<any>(null)
  const [notifications, setNotifications] = useState<any[]>([])
  const router = useRouter()

  useEffect(() => {
    // Initialize auth state
    const initializeAuth = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setSession(session)

        if (session) {
          // Force reload user profile if session exists but no current user
          const currentUser = authService.getCurrentUser()
          if (!currentUser) {
            // Load user profile from session
            await authService.initializeFromSession(session)
            setUser(authService.getCurrentUser())
          } else {
            setUser(currentUser)
          }
        } else {
          // No session, ensure user is cleared
          setUser(null)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
        setUser(null)
      } finally {
        setLoading(false)
      }
    }

    initializeAuth()

    // Set up auth state listener
    const unsubscribeAuth = authService.setupAuthListener((user) => {
      console.log('Auth state changed:', user ? 'User logged in' : 'User logged out')
      setUser(user)
      if (!user) {
        setSession(null)
      }
      setLoading(false)
    })

    // Set up Supabase session listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Supabase auth event:', event)
        setSession(session)

        if (event === 'SIGNED_OUT') {
          setUser(null)
          setSession(null)
        }
      }
    )

    return () => {
      unsubscribeAuth()
      subscription.unsubscribe()
    }
  }, [])

  // Set up notifications when user changes
  useEffect(() => {
    if (user) {
      const unsubscribeNotifications = notificationService.subscribeToNotifications(
        user.id,
        (notification) => {
          setNotifications(prev => [
            { ...notification, id: Date.now().toString(), read: false, timestamp: new Date() },
            ...prev
          ])
        }
      )

      return () => {
        unsubscribeNotifications()
      }
    }
  }, [user])

  const switchRole = async (role: UserRole) => {
    if (user) {
      try {
        const result = await authService.updateProfile({ role })
        if (result.success && result.user) {
          setUser(result.user)
        }
      } catch (error) {
        console.error('Error switching role:', error)
      }
    }
  }

  const markNotificationAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notification =>
        notification.id === id
          ? { ...notification, read: true }
          : notification
      )
    )
  }

  const logout = async () => {
    try {
      console.log('Logging out user...')
      await authService.logout()
      setUser(null)
      setSession(null)
      console.log('User logged out successfully')
      // Redirect to home page after logout
      router.push('/')
    } catch (error) {
      console.error('Logout error:', error)
    }
  }

  const value = {
    user,
    setUser,
    isAuthenticated: !!user && !!session,
    switchRole,
    logout,
    loading,
    session,
    notifications,
    markNotificationAsRead
  }

  return (
    <UserContext.Provider value={value}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}

export default UserContext
