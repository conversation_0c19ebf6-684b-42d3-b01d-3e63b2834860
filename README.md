# 🏠 RenovHub - Home Renovation Marketplace

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://vercel.com/ys-projects-9b45821e/renovhub)
[![Built with Next.js](https://img.shields.io/badge/Built%20with-Next.js-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-007ACC?style=for-the-badge&logo=typescript&logoColor=white)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-38B2AC?style=for-the-badge&logo=tailwind-css&logoColor=white)](https://tailwindcss.com/)

A comprehensive home renovation marketplace built with modern web technologies, connecting homeowners with verified contractors for seamless project management.

## 🚀 **IMPLEMENTATION STATUS**

### ✅ **COMPLETED FEATURES**

#### **🏗️ Core Infrastructure**
- ✅ **Next.js 15 App Router** - Modern React framework with app directory
- ✅ **TypeScript** - Full type safety across the application
- ✅ **Supabase Integration** - Database, authentication, storage, and real-time
- ✅ **Tailwind CSS + shadcn/ui** - Modern, responsive UI components
- ✅ **PWA Support** - Progressive Web App with offline capabilities

#### **🔐 Authentication & User Management**
- ✅ **Complete Auth Flow** - Login, registration, password reset, email verification
- ✅ **Role-based Access** - Customer and contractor role management
- ✅ **User Profiles** - Comprehensive profile management with preferences
- ✅ **Session Management** - Secure session handling with Supabase Auth

#### **💾 Database & Services**
- ✅ **Complete Database Schema** - Users, projects, contractors, bids, messages, reviews
- ✅ **Service Layer** - Comprehensive CRUD operations for all entities
- ✅ **File Upload Service** - Image and document upload with Supabase Storage
- ✅ **Search & Filtering** - Advanced search for projects and contractors
- ✅ **Real-time Subscriptions** - Live updates for messaging and notifications

#### **💬 Messaging & Communication**
- ✅ **Real-time Messaging** - Live chat between customers and contractors
- ✅ **Conversation Management** - Project-based conversation threads
- ✅ **Notification System** - In-app, email, and push notifications
- ✅ **Message Attachments** - File and image sharing in conversations

#### **💰 Bidding & Project Management**
- ✅ **Bidding System** - Complete bid submission and management workflow
- ✅ **Project Creation** - Multi-step project creation with rich details
- ✅ **Bid Comparison** - Side-by-side bid comparison for customers
- ✅ **Project Status Tracking** - Real-time project status updates

#### **🧪 Testing & Quality**
- ✅ **Testing Framework** - Jest + React Testing Library setup
- ✅ **Code Quality Tools** - ESLint, Prettier, TypeScript strict mode
- ✅ **Component Tests** - Unit tests for core components
- ✅ **Service Tests** - Unit tests for authentication and database services

## ✨ Features

### For Homeowners
- **Project Creation**: Describe your renovation needs with AI-powered matching
- **Contractor Discovery**: Browse and filter verified professionals
- **Bid Management**: Compare quotes and proposals from multiple contractors
- **Contract Management**: Editable contracts with customizable payment schedules
- **Real-time Messaging**: Communicate directly with contractors
- **Project Tracking**: Monitor progress and milestones
- **Secure Payments**: Escrow-based payment system

### For Contractors
- **Professional Profiles**: Showcase your work and expertise
- **Lead Generation**: Access to qualified renovation projects
- **Bid Submission**: Submit competitive proposals
- **Project Management**: Track active projects and deadlines
- **Client Communication**: Built-in messaging system
- **Payment Processing**: Secure milestone-based payments

### Recent Improvements
- ✅ Enhanced contractor selection UI with visual feedback
- ✅ Fully editable contracts and payment schedules
- ✅ Functional contract signing and PDF download
- ✅ Consistent page padding and spacing
- ✅ Airbnb-inspired design system with Poppins font
- ✅ Proper favicon configuration
- ✅ Fixed navigation errors

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS with custom design system
- **UI Components**: Radix UI primitives
- **Icons**: Lucide React
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Deployment**: Vercel
- **Font**: Poppins (Airbnb-inspired)

## 🛠️ Installation & Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/yonasnh/renovhub.git
   cd renovhub
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Environment Setup**
   ```bash
   cp .env.example .env.local
   ```

   Add your environment variables:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. **Run the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
renovhub/
├── app/                    # Next.js App Router
│   ├── (auth)/            # Authentication pages
│   ├── contractors/       # Contractor discovery
│   ├── dashboard/         # User dashboard
│   ├── messages/          # Messaging system
│   ├── payments/          # Payment management
│   ├── pro/              # Contractor-specific pages
│   ├── project/          # Project management
│   ├── projects/         # Project listing
│   └── globals.css       # Global styles
├── components/           # Reusable components
│   ├── ui/              # UI primitives
│   └── ...              # Feature components
├── contexts/            # React contexts
├── hooks/               # Custom hooks
├── lib/                 # Utilities
├── public/              # Static assets
├── services/            # API services
└── types/               # TypeScript types
```

## 🎨 Design System

RenovHub features an Airbnb-inspired design system with:

- **Colors**: Coral primary (#ff5a5f), Teal secondary (#00848d), Yellow accent (#ffb400)
- **Typography**: Poppins font family with consistent sizing
- **Components**: Modern cards, buttons, and form elements
- **Spacing**: Consistent padding and margins
- **Shadows**: Subtle depth with modern shadow system

## 🚀 Deployment

The application is deployed on Vercel:

**Live URL**: [https://vercel.com/ys-projects-9b45821e/renovhub](https://vercel.com/ys-projects-9b45821e/renovhub)

### Deploy Your Own

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/yonasnh/renovhub)

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Design inspiration from Airbnb
- UI components from Radix UI
- Icons from Lucide React
- Built with Next.js and Tailwind CSS