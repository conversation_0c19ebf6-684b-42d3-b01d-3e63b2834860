-- Insert comprehensive test data for development
-- This file creates realistic sample data to test the application

-- Clear existing test data
DELETE FROM messages WHERE conversation_id IN (SELECT id FROM conversations WHERE project_id IN (SELECT id FROM projects WHERE customer_id LIKE '550e8400%'));
DELETE FROM conversations WHERE project_id IN (SELECT id FROM projects WHERE customer_id LIKE '550e8400%');
DELETE FROM reviews WHERE reviewer_id LIKE '550e8400%' OR reviewee_id LIKE '550e8400%';
DELETE FROM bids WHERE project_id IN (SELECT id FROM projects WHERE customer_id LIKE '550e8400%');
DELETE FROM projects WHERE customer_id LIKE '550e8400%';
DELETE FROM contractors WHERE user_id LIKE '550e8400%';
DELETE FROM users WHERE id LIKE '550e8400%';

-- Insert test users (these would normally be created via auth, but for testing we'll insert directly)
INSERT INTO users (id, email, name, role, status, avatar_url, phone, location, verified) VALUES
-- Customers
('550e8400-e29b-41d4-a716-446655440001', '<EMAIL>', 'John Smith', 'customer', 'active', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150', '******-0101', 'San <PERSON>, CA', true),
('550e8400-e29b-41d4-a716-446655440004', '<EMAIL>', 'Sarah Davis', 'customer', 'active', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150', '******-0104', 'Berkeley, CA', true),
('550e8400-e29b-41d4-a716-446655440005', '<EMAIL>', 'Robert Jones', 'customer', 'active', 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150', '******-0105', 'Oakland, CA', true),
('550e8400-e29b-41d4-a716-446655440006', '<EMAIL>', 'Emily Brown', 'customer', 'active', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150', '******-0106', 'Palo Alto, CA', true),

-- Contractors/Pros
('550e8400-e29b-41d4-a716-446655440002', '<EMAIL>', 'Jane Wilson', 'pro', 'active', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150', '******-0102', 'San Francisco, CA', true),
('550e8400-e29b-41d4-a716-446655440003', '<EMAIL>', 'Mike Johnson', 'pro', 'active', 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150', '******-0103', 'Oakland, CA', true),
('550e8400-e29b-41d4-a716-446655440007', '<EMAIL>', 'David Martinez', 'pro', 'active', 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150', '******-0107', 'San Jose, CA', true),
('550e8400-e29b-41d4-a716-446655440008', '<EMAIL>', 'Lisa Chen', 'pro', 'active', 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150', '******-0108', 'Berkeley, CA', true),
('550e8400-e29b-41d4-a716-446655440009', '<EMAIL>', 'Carlos Rodriguez', 'pro', 'active', 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150', '******-0109', 'Fremont, CA', true),
('550e8400-e29b-41d4-a716-446655440010', '<EMAIL>', 'Amanda Taylor', 'pro', 'active', 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150', '******-0110', 'San Mateo, CA', true);

-- Insert test contractors with comprehensive data
INSERT INTO contractors (id, user_id, business_name, license, specialties, tier, status, rating_average, rating_count, insurance_info, service_areas, portfolio) VALUES
('650e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'Wilson Kitchen & Bath', 'LIC123456', ARRAY['kitchen', 'bathroom'], 'premium', 'active', 4.8, 24,
 '{"provider": "State Farm", "policy": "SF123456", "liability": 1000000, "expires": "2024-12-31"}',
 ARRAY['{"city": "San Francisco", "state": "CA", "radius": 25}', '{"city": "Oakland", "state": "CA", "radius": 15}'],
 ARRAY['{"title": "Modern Kitchen Remodel", "description": "Complete kitchen transformation", "images": ["/portfolio/kitchen1.jpg"], "year": 2023}']
),
('650e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', 'Johnson General Contracting', 'LIC789012', ARRAY['kitchen', 'bathroom', 'flooring', 'painting'], 'elite', 'active', 4.9, 31,
 '{"provider": "Allstate", "policy": "AL789012", "liability": 2000000, "expires": "2024-11-30"}',
 ARRAY['{"city": "Oakland", "state": "CA", "radius": 30}', '{"city": "Berkeley", "state": "CA", "radius": 20}'],
 ARRAY['{"title": "Full Home Renovation", "description": "Complete home makeover", "images": ["/portfolio/home1.jpg"], "year": 2023}']
),
('650e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440007', 'Martinez Flooring Experts', 'LIC345678', ARRAY['flooring'], 'premium', 'active', 4.7, 18,
 '{"provider": "Progressive", "policy": "PR345678", "liability": 1500000, "expires": "2024-10-15"}',
 ARRAY['{"city": "San Jose", "state": "CA", "radius": 35}'],
 ARRAY['{"title": "Hardwood Installation", "description": "Premium hardwood flooring", "images": ["/portfolio/floor1.jpg"], "year": 2023}']
),
('650e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440008', 'Chen Painting Services', 'LIC456789', ARRAY['painting'], 'basic', 'active', 4.6, 12,
 '{"provider": "Farmers", "policy": "FM456789", "liability": 500000, "expires": "2024-09-30"}',
 ARRAY['{"city": "Berkeley", "state": "CA", "radius": 20}'],
 ARRAY['{"title": "Interior Painting", "description": "Professional interior painting", "images": ["/portfolio/paint1.jpg"], "year": 2023}']
),
('650e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440009', 'Rodriguez Electrical', 'LIC567890', ARRAY['electrical'], 'premium', 'active', 4.8, 22,
 '{"provider": "Liberty Mutual", "policy": "LM567890", "liability": 1000000, "expires": "2024-08-31"}',
 ARRAY['{"city": "Fremont", "state": "CA", "radius": 25}'],
 ARRAY['{"title": "Electrical Upgrade", "description": "Complete electrical system upgrade", "images": ["/portfolio/electrical1.jpg"], "year": 2023}']
),
('650e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440010', 'Taylor Plumbing Solutions', 'LIC678901', ARRAY['plumbing'], 'basic', 'active', 4.5, 15,
 '{"provider": "Geico", "policy": "GE678901", "liability": 750000, "expires": "2024-07-31"}',
 ARRAY['{"city": "San Mateo", "state": "CA", "radius": 20}'],
 ARRAY['{"title": "Bathroom Plumbing", "description": "Modern bathroom plumbing installation", "images": ["/portfolio/plumbing1.jpg"], "year": 2023}']
);

-- Insert test projects with comprehensive data
INSERT INTO projects (id, title, description, category, status, priority, customer_id, budget, timeline, location, requirements, photos, tags) VALUES
('750e8400-e29b-41d4-a716-446655440001', 'Modern Kitchen Renovation', 'Complete kitchen remodel with new cabinets, countertops, and appliances. Looking for experienced contractor with modern design expertise.', 'kitchen', 'active', 'medium', '550e8400-e29b-41d4-a716-446655440001',
 '{"min": 15000, "max": 25000, "currency": "USD", "flexible": true}',
 '{"duration": 21, "flexible": true, "urgency": "within-month"}',
 '{"address": "123 Main St", "city": "San Francisco", "state": "CA", "zipCode": "94102", "country": "USA"}',
 ARRAY['Licensed contractor', 'Insurance required', 'Portfolio of kitchen work', 'References available'],
 ARRAY[],
 ARRAY['kitchen', 'modern', 'cabinets', 'countertops']
),
('750e8400-e29b-41d4-a716-446655440002', 'Bathroom Remodel', 'Master bathroom renovation with walk-in shower and double vanity. High-end finishes preferred.', 'bathroom', 'active', 'high', '550e8400-e29b-41d4-a716-446655440004',
 '{"min": 8000, "max": 15000, "currency": "USD", "flexible": false}',
 '{"duration": 14, "flexible": false, "urgency": "within-week"}',
 '{"address": "456 Oak Ave", "city": "Berkeley", "state": "CA", "zipCode": "94704", "country": "USA"}',
 ARRAY['Licensed contractor', 'Bathroom renovation experience', 'Insurance required'],
 ARRAY[],
 ARRAY['bathroom', 'shower', 'vanity', 'luxury']
),
('750e8400-e29b-41d4-a716-446655440003', 'Hardwood Flooring Installation', 'Install hardwood flooring throughout main living areas. Approximately 800 sq ft.', 'flooring', 'active', 'low', '550e8400-e29b-41d4-a716-446655440005',
 '{"min": 5000, "max": 8000, "currency": "USD", "flexible": true}',
 '{"duration": 7, "flexible": true, "urgency": "flexible"}',
 '{"address": "789 Pine St", "city": "Oakland", "state": "CA", "zipCode": "94601", "country": "USA"}',
 ARRAY['Flooring specialist', 'Licensed contractor', 'Material recommendations'],
 ARRAY[],
 ARRAY['flooring', 'hardwood', 'installation']
),
('750e8400-e29b-41d4-a716-446655440004', 'Interior Painting', 'Paint interior of 3-bedroom house. Walls and ceilings, high-quality paint preferred.', 'painting', 'active', 'medium', '550e8400-e29b-41d4-a716-446655440006',
 '{"min": 3000, "max": 5000, "currency": "USD", "flexible": true}',
 '{"duration": 5, "flexible": true, "urgency": "within-month"}',
 '{"address": "321 Elm Dr", "city": "Palo Alto", "state": "CA", "zipCode": "94301", "country": "USA"}',
 ARRAY['Professional painter', 'Quality paint brands', 'Clean work area'],
 ARRAY[],
 ARRAY['painting', 'interior', 'walls', 'ceilings']
),
('750e8400-e29b-41d4-a716-446655440005', 'Electrical Panel Upgrade', 'Upgrade electrical panel to 200 amp service. Include new outlets in garage.', 'electrical', 'completed', 'high', '550e8400-e29b-41d4-a716-446655440001',
 '{"min": 2000, "max": 3500, "currency": "USD", "flexible": false}',
 '{"duration": 2, "flexible": false, "urgency": "within-week"}',
 '{"address": "123 Main St", "city": "San Francisco", "state": "CA", "zipCode": "94102", "country": "USA"}',
 ARRAY['Licensed electrician', 'Permit handling', 'Code compliance'],
 ARRAY[],
 ARRAY['electrical', 'panel', 'upgrade', 'safety']
);

-- Insert test bids with comprehensive data
INSERT INTO bids (id, project_id, contractor_id, status, amount, currency, timeline, description, breakdown, terms, warranty, expires_at) VALUES
-- Bids for Kitchen Renovation
('850e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440001', 'submitted', 22000.00, 'USD',
 '{"startDate": "2024-02-05", "endDate": "2024-02-26", "duration": 21, "milestones": [{"name": "Demolition", "duration": 3}, {"name": "Electrical/Plumbing", "duration": 5}, {"name": "Installation", "duration": 10}, {"name": "Finishing", "duration": 3}]}',
 'Complete kitchen renovation including demolition, electrical, plumbing, and installation of new fixtures. Premium materials and modern design.',
 '[{"category": "Materials", "amount": 12000, "description": "Cabinets, countertops, appliances"}, {"category": "Labor", "amount": 8000, "description": "Installation and finishing"}, {"category": "Permits", "amount": 2000, "description": "City permits and inspections"}]',
 'Payment terms: 30% upfront, 40% at midpoint, 30% on completion. All materials included. Change orders require written approval.',
 '{"duration": 24, "coverage": "Materials and workmanship", "terms": "2-year warranty on all work and materials"}',
 '2024-02-10 23:59:59+00'),
('850e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440001', '650e8400-e29b-41d4-a716-446655440002', 'submitted', 19500.00, 'USD',
 '{"startDate": "2024-02-01", "endDate": "2024-02-22", "duration": 21, "milestones": [{"name": "Demo & Prep", "duration": 2}, {"name": "Rough Work", "duration": 6}, {"name": "Installation", "duration": 10}, {"name": "Final Details", "duration": 3}]}',
 'Full kitchen remodel with premium finishes and energy-efficient appliances. Includes design consultation.',
 '[{"category": "Materials", "amount": 11000, "description": "Quality cabinets and fixtures"}, {"category": "Labor", "amount": 7500, "description": "Professional installation"}, {"category": "Design", "amount": 1000, "description": "Design consultation and planning"}]',
 'Payment terms: 25% upfront, 50% at midpoint, 25% on completion. 2-year warranty included.',
 '{"duration": 24, "coverage": "Full warranty on materials and labor", "terms": "Comprehensive 2-year warranty"}',
 '2024-02-10 23:59:59+00'),

-- Bids for Bathroom Remodel
('850e8400-e29b-41d4-a716-446655440003', '750e8400-e29b-41d4-a716-446655440002', '650e8400-e29b-41d4-a716-446655440001', 'submitted', 12500.00, 'USD',
 '{"startDate": "2024-02-15", "endDate": "2024-03-01", "duration": 14, "milestones": [{"name": "Demolition", "duration": 2}, {"name": "Plumbing/Electrical", "duration": 4}, {"name": "Tiling", "duration": 5}, {"name": "Fixtures", "duration": 3}]}',
 'Master bathroom renovation with walk-in shower, double vanity, and luxury finishes.',
 '[{"category": "Materials", "amount": 7500, "description": "Tiles, fixtures, vanity"}, {"category": "Labor", "amount": 4500, "description": "Installation and plumbing"}, {"category": "Permits", "amount": 500, "description": "Bathroom renovation permit"}]',
 'Payment: 40% start, 40% midpoint, 20% completion. Premium materials included.',
 '{"duration": 12, "coverage": "Plumbing and installation work", "terms": "1-year warranty on workmanship"}',
 '2024-02-12 23:59:59+00'),

-- Bids for Flooring Project
('850e8400-e29b-41d4-a716-446655440004', '750e8400-e29b-41d4-a716-446655440003', '650e8400-e29b-41d4-a716-446655440003', 'submitted', 6800.00, 'USD',
 '{"startDate": "2024-03-01", "endDate": "2024-03-08", "duration": 7, "milestones": [{"name": "Preparation", "duration": 1}, {"name": "Installation", "duration": 5}, {"name": "Finishing", "duration": 1}]}',
 'Professional hardwood flooring installation. 800 sq ft of premium oak flooring.',
 '[{"category": "Materials", "amount": 4800, "description": "Premium oak hardwood flooring"}, {"category": "Labor", "amount": 1800, "description": "Professional installation"}, {"category": "Supplies", "amount": 200, "description": "Underlayment and finishing materials"}]',
 'Payment: 50% upfront for materials, 50% on completion. Includes floor preparation.',
 '{"duration": 12, "coverage": "Installation warranty", "terms": "1-year installation warranty"}',
 '2024-02-25 23:59:59+00'),

-- Bids for Painting Project
('850e8400-e29b-41d4-a716-446655440005', '750e8400-e29b-41d4-a716-446655440004', '650e8400-e29b-41d4-a716-446655440004', 'submitted', 4200.00, 'USD',
 '{"startDate": "2024-03-10", "endDate": "2024-03-15", "duration": 5, "milestones": [{"name": "Prep Work", "duration": 1}, {"name": "Primer", "duration": 1}, {"name": "Paint Application", "duration": 2}, {"name": "Touch-ups", "duration": 1}]}',
 'Interior painting of 3-bedroom house. Walls and ceilings with premium paint.',
 '[{"category": "Materials", "amount": 1200, "description": "Premium paint and supplies"}, {"category": "Labor", "amount": 2800, "description": "Professional painting services"}, {"category": "Prep", "amount": 200, "description": "Surface preparation and protection"}]',
 'Payment: 30% start, 70% completion. All materials and cleanup included.',
 '{"duration": 6, "coverage": "Paint and workmanship", "terms": "6-month touch-up warranty"}',
 '2024-03-05 23:59:59+00'),

-- Completed bid for Electrical Project
('850e8400-e29b-41d4-a716-446655440006', '750e8400-e29b-41d4-a716-446655440005', '650e8400-e29b-41d4-a716-446655440005', 'accepted', 2800.00, 'USD',
 '{"startDate": "2024-01-15", "endDate": "2024-01-17", "duration": 2, "milestones": [{"name": "Panel Installation", "duration": 1}, {"name": "Wiring & Testing", "duration": 1}]}',
 'Electrical panel upgrade to 200 amp service with new garage outlets.',
 '[{"category": "Materials", "amount": 1500, "description": "200 amp panel and components"}, {"category": "Labor", "amount": 1000, "description": "Licensed electrician services"}, {"category": "Permits", "amount": 300, "description": "Electrical permit and inspection"}]',
 'Payment: 50% start, 50% completion. All permits and inspections included.',
 '{"duration": 12, "coverage": "Electrical work warranty", "terms": "1-year warranty on all electrical work"}',
 '2024-01-10 23:59:59+00');

-- Insert test reviews with comprehensive ratings
INSERT INTO reviews (id, project_id, reviewer_id, reviewee_id, rating, rating_breakdown, title, content, verified) VALUES
('950e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440009', 5,
 '{"quality": 5, "communication": 5, "timeliness": 5, "value": 5}',
 'Excellent Electrical Work', 'Carlos did an outstanding job upgrading our electrical panel. Professional, on-time, and very knowledgeable. The work was completed safely and up to code. Highly recommend!', true),
('950e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440002', 5,
 '{"quality": 5, "communication": 4, "timeliness": 5, "value": 5}',
 'Beautiful Kitchen Renovation', 'Jane and her team transformed our kitchen beyond our expectations. The attention to detail and quality of work is exceptional. Great communication throughout the project.', true),
('950e8400-e29b-41d4-a716-446655440003', '750e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440003', 4,
 '{"quality": 4, "communication": 4, "timeliness": 3, "value": 4}',
 'Good General Contracting Work', 'Mike did solid work on our project. Good quality and fair pricing. There were some minor delays but he communicated well and the final result was good.', true),
('950e8400-e29b-41d4-a716-446655440004', '750e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440007', 5,
 '{"quality": 5, "communication": 5, "timeliness": 5, "value": 4}',
 'Perfect Flooring Installation', 'David did an amazing job with our hardwood floors. The installation is flawless and he was very professional throughout. Worth every penny!', true),
('950e8400-e29b-41d4-a716-446655440005', '750e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440008', 4,
 '{"quality": 4, "communication": 4, "timeliness": 4, "value": 5}',
 'Great Painting Job', 'Lisa did excellent work painting our house. Very neat and professional. Great value for the quality of work. Would hire again.', true);

-- Insert test conversations
INSERT INTO conversations (id, project_id, participants) VALUES
('a50e8400-e29b-41d4-a716-446655440001', '750e8400-e29b-41d4-a716-446655440001', ARRAY['550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002']),
('a50e8400-e29b-41d4-a716-446655440002', '750e8400-e29b-41d4-a716-446655440002', ARRAY['550e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440003']),
('a50e8400-e29b-41d4-a716-446655440003', '750e8400-e29b-41d4-a716-446655440003', ARRAY['550e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440007']),
('a50e8400-e29b-41d4-a716-446655440004', '750e8400-e29b-41d4-a716-446655440004', ARRAY['550e8400-e29b-41d4-a716-446655440006', '550e8400-e29b-41d4-a716-446655440008']),
('a50e8400-e29b-41d4-a716-446655440005', '750e8400-e29b-41d4-a716-446655440005', ARRAY['550e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440009']);

-- Insert test messages with realistic conversation flow
INSERT INTO messages (id, conversation_id, sender_id, content, type, status) VALUES
-- Kitchen renovation conversation
('b50e8400-e29b-41d4-a716-446655440001', 'a50e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'Hi Jane, I''m interested in your bid for the kitchen renovation. Can we discuss the timeline?', 'text', 'read'),
('b50e8400-e29b-41d4-a716-446655440002', 'a50e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'Hi John! Absolutely, I''d be happy to discuss the timeline. When would be a good time for a call?', 'text', 'read'),
('b50e8400-e29b-41d4-a716-446655440003', 'a50e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440001', 'How about tomorrow afternoon? I''m available after 2 PM.', 'text', 'read'),
('b50e8400-e29b-41d4-a716-446655440004', 'a50e8400-e29b-41d4-a716-446655440001', '550e8400-e29b-41d4-a716-446655440002', 'Perfect! I''ll call you at 2:30 PM. Also, I wanted to mention that we can start earlier if needed.', 'text', 'delivered'),

-- Bathroom remodel conversation
('b50e8400-e29b-41d4-a716-446655440005', 'a50e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', 'Hello Mike, your bathroom remodel proposal looks great. Do you have availability in February?', 'text', 'read'),
('b50e8400-e29b-41d4-a716-446655440006', 'a50e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440003', 'Hi Sarah! Yes, I have availability starting February 15th. Let me know if that works for you.', 'text', 'read'),
('b50e8400-e29b-41d4-a716-446655440007', 'a50e8400-e29b-41d4-a716-446655440002', '550e8400-e29b-41d4-a716-446655440004', 'That timing works perfectly. Can you provide references from recent bathroom projects?', 'text', 'sent'),

-- Flooring conversation
('b50e8400-e29b-41d4-a716-446655440008', 'a50e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440005', 'Hi David, I saw your bid for the hardwood flooring. What type of wood do you recommend?', 'text', 'read'),
('b50e8400-e29b-41d4-a716-446655440009', 'a50e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440007', 'Hi Robert! For your space, I''d recommend red oak. It''s durable and has beautiful grain patterns. I can show you samples.', 'text', 'delivered'),

-- Painting conversation
('b50e8400-e29b-41d4-a716-446655440010', 'a50e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440006', 'Lisa, your painting quote looks good. What brand of paint do you typically use?', 'text', 'read'),
('b50e8400-e29b-41d4-a716-446655440011', 'a50e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440008', 'I typically use Sherwin-Williams or Benjamin Moore for interior work. Both offer excellent coverage and durability.', 'text', 'sent'),

-- Electrical conversation (completed project)
('b50e8400-e29b-41d4-a716-446655440012', 'a50e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440001', 'Carlos, thank you for the excellent electrical work. Everything is working perfectly!', 'text', 'read'),
('b50e8400-e29b-41d4-a716-446655440013', 'a50e8400-e29b-41d4-a716-446655440005', '550e8400-e29b-41d4-a716-446655440009', 'Thank you John! I''m glad everything is working well. Don''t hesitate to reach out if you need any electrical work in the future.', 'text', 'read');
