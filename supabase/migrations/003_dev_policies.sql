-- Development-friendly policies for testing
-- These policies allow public read access for development and testing
-- In production, these should be removed or made more restrictive

-- Allow public read access to contractors for browsing
CREATE POLICY "Public read access to active contractors" ON contractors
    FOR SELECT USING (status = 'active');

-- Allow public read access to active projects for browsing
CREATE POLICY "Public read access to active projects" ON projects
    FOR SELECT USING (status IN ('active', 'in-progress'));

-- Allow public read access to reviews for browsing
CREATE POLICY "Public read access to reviews" ON reviews
    FOR SELECT USING (true);

-- Allow public read access to bids for project owners and contractors
CREATE POLICY "Public read access to bids" ON bids
    FOR SELECT USING (true);

-- Allow public read access to users for basic profile info
CREATE POLICY "Public read access to user profiles" ON users
    FOR SELECT USING (status = 'active');

-- Allow public read access to conversations for participants
CREATE POLICY "Public read access to conversations" ON conversations
    FOR SELECT USING (true);

-- Allow public read access to messages for conversation participants
CREATE POLICY "Public read access to messages" ON messages
    FOR SELECT USING (true);

-- Allow public read access to payments for involved parties
CREATE POLICY "Public read access to payments" ON payments
    FOR SELECT USING (true);
