-- Enable Row Level Security on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE contractors ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE bids ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversations ENABLE ROW LEVEL SECURITY;
ALTER TABLE messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;

-- Users table policies
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Public profiles are viewable by authenticated users" ON users
    FOR SELECT USING (
        auth.role() = 'authenticated' AND 
        (preferences->>'privacy'->>'profileVisibility' = 'public' OR 
         preferences->>'privacy'->>'profileVisibility' = 'contractors-only')
    );

-- Contractors table policies
CREATE POLICY "Contractors can view their own profile" ON contractors
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Contractors can update their own profile" ON contractors
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Contractors can insert their own profile" ON contractors
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Active contractors are viewable by authenticated users" ON contractors
    FOR SELECT USING (
        auth.role() = 'authenticated' AND 
        status = 'active'
    );

-- Projects table policies
CREATE POLICY "Customers can view their own projects" ON projects
    FOR SELECT USING (customer_id = auth.uid());

CREATE POLICY "Customers can create projects" ON projects
    FOR INSERT WITH CHECK (customer_id = auth.uid());

CREATE POLICY "Customers can update their own projects" ON projects
    FOR UPDATE USING (customer_id = auth.uid());

CREATE POLICY "Contractors can view projects they're involved in" ON projects
    FOR SELECT USING (
        contractor_id IN (
            SELECT id FROM contractors WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Contractors can view active projects in their specialties" ON projects
    FOR SELECT USING (
        auth.role() = 'authenticated' AND
        status = 'active' AND
        category = ANY(
            SELECT unnest(specialties) 
            FROM contractors 
            WHERE user_id = auth.uid() AND status = 'active'
        )
    );

-- Bids table policies
CREATE POLICY "Contractors can view their own bids" ON bids
    FOR SELECT USING (
        contractor_id IN (
            SELECT id FROM contractors WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Contractors can create bids" ON bids
    FOR INSERT WITH CHECK (
        contractor_id IN (
            SELECT id FROM contractors WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Contractors can update their own bids" ON bids
    FOR UPDATE USING (
        contractor_id IN (
            SELECT id FROM contractors WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Project owners can view bids on their projects" ON bids
    FOR SELECT USING (
        project_id IN (
            SELECT id FROM projects WHERE customer_id = auth.uid()
        )
    );

-- Conversations table policies
CREATE POLICY "Users can view conversations they participate in" ON conversations
    FOR SELECT USING (auth.uid() = ANY(participants));

CREATE POLICY "Users can create conversations they participate in" ON conversations
    FOR INSERT WITH CHECK (auth.uid() = ANY(participants));

CREATE POLICY "Users can update conversations they participate in" ON conversations
    FOR UPDATE USING (auth.uid() = ANY(participants));

-- Messages table policies
CREATE POLICY "Users can view messages in their conversations" ON messages
    FOR SELECT USING (
        conversation_id IN (
            SELECT id FROM conversations WHERE auth.uid() = ANY(participants)
        )
    );

CREATE POLICY "Users can send messages in their conversations" ON messages
    FOR INSERT WITH CHECK (
        sender_id = auth.uid() AND
        conversation_id IN (
            SELECT id FROM conversations WHERE auth.uid() = ANY(participants)
        )
    );

CREATE POLICY "Users can update their own messages" ON messages
    FOR UPDATE USING (sender_id = auth.uid());

-- Reviews table policies
CREATE POLICY "Users can view reviews they wrote" ON reviews
    FOR SELECT USING (reviewer_id = auth.uid());

CREATE POLICY "Users can view reviews about them" ON reviews
    FOR SELECT USING (reviewee_id = auth.uid());

CREATE POLICY "Users can create reviews for completed projects" ON reviews
    FOR INSERT WITH CHECK (
        reviewer_id = auth.uid() AND
        project_id IN (
            SELECT id FROM projects 
            WHERE (customer_id = auth.uid() OR contractor_id IN (
                SELECT id FROM contractors WHERE user_id = auth.uid()
            )) AND status = 'completed'
        )
    );

CREATE POLICY "Users can update their own reviews" ON reviews
    FOR UPDATE USING (reviewer_id = auth.uid());

CREATE POLICY "Public reviews are viewable by authenticated users" ON reviews
    FOR SELECT USING (auth.role() = 'authenticated');

-- Payments table policies
CREATE POLICY "Users can view payments they're involved in" ON payments
    FOR SELECT USING (
        payer_id = auth.uid() OR payee_id = auth.uid()
    );

CREATE POLICY "Users can create payments they're paying for" ON payments
    FOR INSERT WITH CHECK (payer_id = auth.uid());

CREATE POLICY "Users can update payments they're involved in" ON payments
    FOR UPDATE USING (
        payer_id = auth.uid() OR payee_id = auth.uid()
    );

-- Create helper function for checking user roles
CREATE OR REPLACE FUNCTION auth.user_role()
RETURNS TEXT AS $$
BEGIN
    RETURN (
        SELECT role::TEXT 
        FROM users 
        WHERE id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Additional policies based on user roles
CREATE POLICY "Pro users can view more project details" ON projects
    FOR SELECT USING (
        auth.user_role() = 'pro' AND
        status IN ('active', 'in-progress')
    );

-- Function to handle user creation from auth trigger
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, name, role)
    VALUES (
        NEW.id,
        NEW.email,
        COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'role', 'customer')::user_role
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger to automatically create user profile
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
