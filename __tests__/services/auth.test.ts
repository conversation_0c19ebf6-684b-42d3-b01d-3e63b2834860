import { authService } from '@/services/auth'
import { supabase } from '@/lib/supabase'

// Mock Supabase
jest.mock('@/lib/supabase')

const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('login', () => {
    it('should login successfully with valid credentials', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        email_confirmed_at: new Date().toISOString(),
      }
      const mockSession = {
        access_token: 'token',
        user: mockUser,
      }

      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      })

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: '123',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'customer',
            status: 'active',
            verified: true,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            preferences: {},
          },
          error: null,
        }),
      } as any)

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'password123',
      })

      expect(result.success).toBe(true)
      expect(result.user).toBeDefined()
      expect(result.user?.email).toBe('<EMAIL>')
    })

    it('should handle login failure with invalid credentials', async () => {
      mockSupabase.auth.signInWithPassword.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'Invalid login credentials' } as any,
      })

      const result = await authService.login({
        email: '<EMAIL>',
        password: 'wrongpassword',
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('Invalid email or password. Please try again.')
    })
  })

  describe('register', () => {
    it('should register successfully with valid data', async () => {
      const mockUser = {
        id: '123',
        email: '<EMAIL>',
        email_confirmed_at: new Date().toISOString(),
      }
      const mockSession = {
        access_token: 'token',
        user: mockUser,
      }

      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: mockUser, session: mockSession },
        error: null,
      })

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: {
            id: '123',
            email: '<EMAIL>',
            name: 'Test User',
            role: 'customer',
            status: 'active',
            verified: false,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            preferences: {},
          },
          error: null,
        }),
      } as any)

      const result = await authService.register({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        role: 'customer',
      })

      expect(result.success).toBe(true)
      expect(result.user).toBeDefined()
    })

    it('should handle registration failure with existing email', async () => {
      mockSupabase.auth.signUp.mockResolvedValue({
        data: { user: null, session: null },
        error: { message: 'User already registered' } as any,
      })

      const result = await authService.register({
        email: '<EMAIL>',
        password: 'password123',
        name: 'Test User',
        role: 'customer',
      })

      expect(result.success).toBe(false)
      expect(result.error).toBe('An account with this email already exists.')
    })
  })

  describe('resetPassword', () => {
    it('should send reset password email successfully', async () => {
      mockSupabase.auth.resetPasswordForEmail.mockResolvedValue({
        data: {},
        error: null,
      })

      const result = await authService.resetPassword('<EMAIL>')

      expect(result.success).toBe(true)
      expect(mockSupabase.auth.resetPasswordForEmail).toHaveBeenCalledWith(
        '<EMAIL>',
        expect.any(Object)
      )
    })

    it('should handle reset password failure', async () => {
      mockSupabase.auth.resetPasswordForEmail.mockResolvedValue({
        data: {},
        error: { message: 'User not found' } as any,
      })

      const result = await authService.resetPassword('<EMAIL>')

      expect(result.success).toBe(false)
      expect(result.error).toBe('User not found')
    })
  })

  describe('logout', () => {
    it('should logout successfully', async () => {
      mockSupabase.auth.signOut.mockResolvedValue({
        error: null,
      })

      const result = await authService.logout()

      expect(result.success).toBe(true)
      expect(mockSupabase.auth.signOut).toHaveBeenCalled()
    })
  })
})
