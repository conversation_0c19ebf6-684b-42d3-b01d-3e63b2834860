import { messagingService } from '@/services/messaging'
import { supabase } from '@/lib/supabase'

// Mock Supabase
jest.mock('@/lib/supabase')

const mockSupabase = supabase as jest.Mocked<typeof supabase>

describe('MessagingService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('getUserConversations', () => {
    it('should fetch user conversations successfully', async () => {
      const mockConversations = [
        {
          id: '1',
          project_id: 'project-1',
          participants: ['user-1', 'user-2'],
          unread_count: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          projects: {
            id: 'project-1',
            title: 'Kitchen Renovation',
            status: 'active'
          }
        }
      ]

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        contains: jest.fn().mockReturnThis(),
        order: jest.fn().mockReturnThis(),
        then: jest.fn().mockResolvedValue({
          data: mockConversations,
          error: null
        })
      } as any)

      // Mock the participant details query
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        contains: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: mockConversations,
          error: null
        })
      } as any)

      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        in: jest.fn().mockResolvedValue({
          data: [
            {
              id: 'user-2',
              name: 'John Contractor',
              avatar_url: null,
              role: 'pro'
            }
          ],
          error: null
        })
      } as any)

      const result = await messagingService.getUserConversations('user-1')

      expect(result.success).toBe(true)
      expect(result.data).toHaveLength(1)
      expect(result.data![0].participants_data).toBeDefined()
    })

    it('should handle errors when fetching conversations', async () => {
      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        contains: jest.fn().mockReturnThis(),
        order: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'Database error' }
        })
      } as any)

      const result = await messagingService.getUserConversations('user-1')

      expect(result.success).toBe(false)
      expect(result.error).toBe('Database error')
    })
  })

  describe('sendMessage', () => {
    it('should send a message successfully', async () => {
      const mockMessage = {
        id: 'msg-1',
        conversation_id: 'conv-1',
        sender_id: 'user-1',
        type: 'text',
        content: 'Hello there!',
        attachments: [],
        status: 'sent',
        sent_at: new Date().toISOString()
      }

      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockMessage,
          error: null
        })
      } as any)

      // Mock the conversation update
      mockSupabase.from.mockReturnValueOnce({
        update: jest.fn().mockReturnThis(),
        eq: jest.fn().mockResolvedValue({
          error: null
        })
      } as any)

      const messageData = {
        conversation_id: 'conv-1',
        sender_id: 'user-1',
        type: 'text' as const,
        content: 'Hello there!'
      }

      const result = await messagingService.sendMessage(messageData)

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockMessage)
    })

    it('should handle errors when sending message', async () => {
      mockSupabase.from.mockReturnValue({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'Failed to send message' }
        })
      } as any)

      const messageData = {
        conversation_id: 'conv-1',
        sender_id: 'user-1',
        type: 'text' as const,
        content: 'Hello there!'
      }

      const result = await messagingService.sendMessage(messageData)

      expect(result.success).toBe(false)
      expect(result.error).toBe('Failed to send message')
    })
  })

  describe('createConversation', () => {
    it('should create a new conversation successfully', async () => {
      const mockConversation = {
        id: 'conv-1',
        project_id: 'project-1',
        participants: ['user-1', 'user-2'],
        unread_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      // Mock check for existing conversation
      mockSupabase.from.mockReturnValueOnce({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: null,
          error: { message: 'No rows returned' }
        })
      } as any)

      // Mock create new conversation
      mockSupabase.from.mockReturnValueOnce({
        insert: jest.fn().mockReturnThis(),
        select: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: mockConversation,
          error: null
        })
      } as any)

      const result = await messagingService.createConversation('project-1', ['user-1', 'user-2'])

      expect(result.success).toBe(true)
      expect(result.data).toEqual(mockConversation)
    })

    it('should return existing conversation if it already exists', async () => {
      const existingConversation = {
        id: 'conv-1',
        project_id: 'project-1',
        participants: ['user-1', 'user-2'],
        unread_count: 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      mockSupabase.from.mockReturnValue({
        select: jest.fn().mockReturnThis(),
        eq: jest.fn().mockReturnThis(),
        single: jest.fn().mockResolvedValue({
          data: existingConversation,
          error: null
        })
      } as any)

      const result = await messagingService.createConversation('project-1', ['user-1', 'user-2'])

      expect(result.success).toBe(true)
      expect(result.data).toEqual(existingConversation)
    })
  })
})
