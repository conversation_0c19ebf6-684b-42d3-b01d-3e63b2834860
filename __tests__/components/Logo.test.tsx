import { render, screen } from '@testing-library/react'
import { Logo } from '@/components/logo'

describe('Logo Component', () => {
  it('renders the logo with default size', () => {
    render(<Logo />)
    
    const logo = screen.getByRole('img', { name: /renovhub/i })
    expect(logo).toBeInTheDocument()
  })

  it('renders with custom size', () => {
    render(<Logo size="lg" />)
    
    const logo = screen.getByRole('img', { name: /renovhub/i })
    expect(logo).toBeInTheDocument()
  })

  it('renders with custom className', () => {
    const customClass = 'custom-logo-class'
    render(<Logo className={customClass} />)
    
    const logoContainer = screen.getByRole('img', { name: /renovhub/i }).parentElement
    expect(logoContainer).toHaveClass(customClass)
  })

  it('has correct accessibility attributes', () => {
    render(<Logo />)

    const logo = screen.getByRole('img', { name: /renovhub/i })
    expect(logo).toHaveAttribute('alt', 'RenovHub')
  })

  it('renders with default variant (gradient background)', () => {
    render(<Logo variant="default" />)

    const svg = screen.getByRole('img', { name: /renovhub/i })
    expect(svg).toBeInTheDocument()

    // Check that the circle uses gradient fill, not white
    const circle = svg.querySelector('circle')
    expect(circle).toHaveAttribute('fill', 'url(#logoGradient)')
  })

  it('renders with light variant (white background)', () => {
    render(<Logo variant="light" />)

    const svg = screen.getByRole('img', { name: /renovhub/i })
    expect(svg).toBeInTheDocument()

    // Check that the circle uses white fill for light variant
    const circle = svg.querySelector('circle')
    expect(circle).toHaveAttribute('fill', '#ffffff')
  })
})
