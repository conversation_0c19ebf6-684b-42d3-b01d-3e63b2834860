"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  CheckCircle, 
  Circle, 
  Clock, 
  AlertTriangle, 
  Plus, 
  Calendar,
  FileText,
  Paperclip,
  MoreVertical,
  Edit,
  Trash2,
  Play,
  Pause
} from 'lucide-react'
import { format } from 'date-fns'
import type { Milestone, ProjectStatus } from '@/hooks/use-milestones'

interface MilestoneTrackerProps {
  project: ProjectStatus
  milestones: Milestone[]
  onUpdateMilestone: (milestoneId: string, updates: Partial<Milestone>) => Promise<boolean>
  onAddMilestone: () => void
  onEditMilestone: (milestone: Milestone) => void
  onDeleteMilestone: (milestoneId: string) => Promise<boolean>
  onUpdateProjectStatus: (status: ProjectStatus['status'], notes?: string) => Promise<boolean>
  loading?: boolean
  canEdit?: boolean
}

export function MilestoneTracker({
  project,
  milestones,
  onUpdateMilestone,
  onAddMilestone,
  onEditMilestone,
  onDeleteMilestone,
  onUpdateProjectStatus,
  loading = false,
  canEdit = true
}: MilestoneTrackerProps) {
  const [expandedMilestone, setExpandedMilestone] = useState<string | null>(null)

  const getStatusIcon = (status: Milestone['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'in-progress':
        return <Play className="h-5 w-5 text-blue-600" />
      case 'blocked':
        return <AlertTriangle className="h-5 w-5 text-red-600" />
      default:
        return <Circle className="h-5 w-5 text-slate-400" />
    }
  }

  const getStatusColor = (status: Milestone['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'in-progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'blocked':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-slate-100 text-slate-800 border-slate-200'
    }
  }

  const getProjectStatusColor = (status: ProjectStatus['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'in-progress':
        return 'bg-blue-100 text-blue-800'
      case 'on-hold':
        return 'bg-yellow-100 text-yellow-800'
      case 'cancelled':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-slate-100 text-slate-800'
    }
  }

  const handleMilestoneStatusChange = async (milestoneId: string, newStatus: Milestone['status']) => {
    const updates: Partial<Milestone> = { status: newStatus }
    
    if (newStatus === 'completed') {
      updates.completed_date = new Date().toISOString()
    }
    
    await onUpdateMilestone(milestoneId, updates)
  }

  const toggleMilestoneExpansion = (milestoneId: string) => {
    setExpandedMilestone(expandedMilestone === milestoneId ? null : milestoneId)
  }

  return (
    <div className="space-y-6">
      {/* Project Status Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <span>Project Progress</span>
              <Badge className={getProjectStatusColor(project.status)}>
                {project.status.replace('-', ' ').toUpperCase()}
              </Badge>
            </CardTitle>
            {canEdit && (
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onUpdateProjectStatus('in-progress')}
                  disabled={loading || project.status === 'in-progress'}
                >
                  <Play className="h-4 w-4 mr-1" />
                  Start
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onUpdateProjectStatus('on-hold')}
                  disabled={loading || project.status === 'on-hold'}
                >
                  <Pause className="h-4 w-4 mr-1" />
                  Pause
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onUpdateProjectStatus('completed')}
                  disabled={loading || project.status === 'completed'}
                >
                  <CheckCircle className="h-4 w-4 mr-1" />
                  Complete
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm text-slate-600 mb-2">
                <span>Overall Progress</span>
                <span>{project.progress_percentage}%</span>
              </div>
              <Progress value={project.progress_percentage} className="h-3" />
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-slate-900">
                  {milestones.filter(m => m.status === 'completed').length}
                </div>
                <div className="text-sm text-slate-500">Completed</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {milestones.filter(m => m.status === 'in-progress').length}
                </div>
                <div className="text-sm text-slate-500">In Progress</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-slate-600">
                  {milestones.filter(m => m.status === 'pending').length}
                </div>
                <div className="text-sm text-slate-500">Pending</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-red-600">
                  {milestones.filter(m => m.status === 'blocked').length}
                </div>
                <div className="text-sm text-slate-500">Blocked</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Milestones List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Milestones</CardTitle>
            {canEdit && (
              <Button size="sm" onClick={onAddMilestone}>
                <Plus className="h-4 w-4 mr-1" />
                Add Milestone
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {milestones.length === 0 ? (
              <div className="text-center py-8 text-slate-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-slate-300" />
                <p>No milestones defined yet</p>
                {canEdit && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onAddMilestone}
                    className="mt-2"
                  >
                    Add your first milestone
                  </Button>
                )}
              </div>
            ) : (
              milestones.map((milestone, index) => (
                <div
                  key={milestone.id}
                  className="border border-slate-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                >
                  <div className="flex items-start space-x-4">
                    {/* Status Icon */}
                    <div className="flex-shrink-0 mt-1">
                      {getStatusIcon(milestone.status)}
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h3 className="font-medium text-slate-900 mb-1">
                            {milestone.title}
                          </h3>
                          <p className="text-sm text-slate-600 mb-2">
                            {milestone.description}
                          </p>
                          
                          <div className="flex items-center space-x-4 text-xs text-slate-500">
                            <Badge className={getStatusColor(milestone.status)}>
                              {milestone.status.replace('-', ' ')}
                            </Badge>
                            
                            {milestone.due_date && (
                              <span className="flex items-center">
                                <Calendar className="h-3 w-3 mr-1" />
                                Due {format(new Date(milestone.due_date), 'MMM d')}
                              </span>
                            )}
                            
                            {milestone.completed_date && (
                              <span className="flex items-center text-green-600">
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Completed {format(new Date(milestone.completed_date), 'MMM d')}
                              </span>
                            )}
                            
                            {milestone.attachments && milestone.attachments.length > 0 && (
                              <span className="flex items-center">
                                <Paperclip className="h-3 w-3 mr-1" />
                                {milestone.attachments.length} files
                              </span>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        {canEdit && (
                          <div className="flex items-center space-x-2">
                            {milestone.status !== 'completed' && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleMilestoneStatusChange(
                                  milestone.id,
                                  milestone.status === 'in-progress' ? 'completed' : 'in-progress'
                                )}
                                disabled={loading}
                              >
                                {milestone.status === 'in-progress' ? (
                                  <CheckCircle className="h-4 w-4" />
                                ) : (
                                  <Play className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                            
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onEditMilestone(milestone)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => onDeleteMilestone(milestone.id)}
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        )}
                      </div>

                      {/* Requirements */}
                      {milestone.requirements && milestone.requirements.length > 0 && (
                        <div className="mt-3">
                          <h4 className="text-xs font-medium text-slate-700 mb-2">Requirements:</h4>
                          <ul className="text-xs text-slate-600 space-y-1">
                            {milestone.requirements.map((req, reqIndex) => (
                              <li key={reqIndex} className="flex items-start">
                                <span className="mr-2">•</span>
                                <span>{req}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
