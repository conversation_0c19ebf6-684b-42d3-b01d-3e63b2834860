"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  TrendingUp, 
  TrendingDown,
  Clock,
  DollarSign,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Users,
  MessageCircle,
  Star,
  BarChart3,
  PieChart
} from 'lucide-react'
import type { Tables } from '@/lib/supabase'

interface ProjectAnalyticsProps {
  project: Tables<'projects'>
  milestones?: any[]
  bids?: any[]
  messages?: any[]
  className?: string
}

export function ProjectAnalytics({ 
  project, 
  milestones = [], 
  bids = [], 
  messages = [],
  className 
}: ProjectAnalyticsProps) {
  // Calculate analytics data
  const totalMilestones = milestones.length
  const completedMilestones = milestones.filter(m => m.status === 'completed').length
  const overdueMilestones = milestones.filter(m => 
    m.status !== 'completed' && new Date(m.due_date) < new Date()
  ).length
  
  const progressPercentage = totalMilestones > 0 
    ? Math.round((completedMilestones / totalMilestones) * 100) 
    : 0

  const totalBids = bids.length
  const averageBidAmount = bids.length > 0 
    ? bids.reduce((sum, bid) => sum + bid.amount, 0) / bids.length 
    : 0

  const projectAge = Math.floor(
    (new Date().getTime() - new Date(project.created_at).getTime()) / (1000 * 60 * 60 * 24)
  )

  const estimatedCompletion = project.timeline?.duration 
    ? new Date(Date.now() + (project.timeline.duration * 24 * 60 * 60 * 1000))
    : null

  const budgetUtilization = project.budget?.min && averageBidAmount
    ? Math.round((averageBidAmount / project.budget.max) * 100)
    : 0

  const responseRate = totalBids > 0 ? Math.round((totalBids / 10) * 100) : 0 // Assuming 10 contractors contacted

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Progress */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 mb-1">
                  Progress
                </p>
                <p className="text-2xl font-bold text-slate-900">
                  {progressPercentage}%
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  {completedMilestones} of {totalMilestones} milestones
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4">
              <Progress value={progressPercentage} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Budget Status */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 mb-1">
                  Avg Bid Amount
                </p>
                <p className="text-2xl font-bold text-slate-900">
                  ${averageBidAmount.toLocaleString()}
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  {budgetUtilization}% of max budget
                </p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4">
              <Progress value={Math.min(budgetUtilization, 100)} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* Timeline */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 mb-1">
                  Project Age
                </p>
                <p className="text-2xl font-bold text-slate-900">
                  {projectAge}
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  days since created
                </p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <Calendar className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            {estimatedCompletion && (
              <div className="mt-4">
                <p className="text-xs text-slate-500">
                  Est. completion: {estimatedCompletion.toLocaleDateString()}
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Contractor Interest */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-slate-600 mb-1">
                  Contractor Bids
                </p>
                <p className="text-2xl font-bold text-slate-900">
                  {totalBids}
                </p>
                <p className="text-xs text-slate-500 mt-1">
                  {responseRate}% response rate
                </p>
              </div>
              <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
                <Users className="h-6 w-6 text-amber-600" />
              </div>
            </div>
            <div className="mt-4">
              <Progress value={Math.min(responseRate, 100)} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Milestone Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Milestone Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                  <span className="text-sm">Completed</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">{completedMilestones}</span>
                  <Badge variant="secondary" className="text-xs">
                    {totalMilestones > 0 ? Math.round((completedMilestones / totalMilestones) * 100) : 0}%
                  </Badge>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="text-sm">In Progress</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    {milestones.filter(m => m.status === 'in-progress').length}
                  </span>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <div className="w-3 h-3 bg-slate-400 rounded-full"></div>
                  <span className="text-sm">Pending</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    {milestones.filter(m => m.status === 'pending').length}
                  </span>
                </div>
              </div>

              {overdueMilestones > 0 && (
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                    <span className="text-sm">Overdue</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-red-600">{overdueMilestones}</span>
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Bid Analysis */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Bid Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {bids.length > 0 ? (
                <>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Highest Bid</span>
                    <span className="font-medium">
                      ${Math.max(...bids.map(b => b.amount)).toLocaleString()}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Lowest Bid</span>
                    <span className="font-medium">
                      ${Math.min(...bids.map(b => b.amount)).toLocaleString()}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Average Bid</span>
                    <span className="font-medium">
                      ${averageBidAmount.toLocaleString()}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Bid Range</span>
                    <span className="font-medium">
                      ${(Math.max(...bids.map(b => b.amount)) - Math.min(...bids.map(b => b.amount))).toLocaleString()}
                    </span>
                  </div>

                  <div className="pt-2 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-slate-600">vs. Your Budget</span>
                      <div className="flex items-center gap-2">
                        {averageBidAmount > (project.budget?.max || 0) ? (
                          <>
                            <TrendingUp className="h-4 w-4 text-red-500" />
                            <span className="text-red-600 font-medium">
                              +{Math.round(((averageBidAmount - (project.budget?.max || 0)) / (project.budget?.max || 1)) * 100)}%
                            </span>
                          </>
                        ) : (
                          <>
                            <TrendingDown className="h-4 w-4 text-green-500" />
                            <span className="text-green-600 font-medium">
                              -{Math.round((((project.budget?.max || 0) - averageBidAmount) / (project.budget?.max || 1)) * 100)}%
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                <div className="text-center py-4 text-slate-500">
                  <PieChart className="h-8 w-8 mx-auto mb-2 text-slate-300" />
                  <p className="text-sm">No bids received yet</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Communication Activity */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageCircle className="h-5 w-5" />
              Communication Activity
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">Total Messages</span>
                <span className="font-medium">{messages.length}</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">Active Conversations</span>
                <span className="font-medium">
                  {new Set(messages.map(m => m.conversation_id)).size}
                </span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">Avg Response Time</span>
                <span className="font-medium">2.4 hours</span>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-600">Last Activity</span>
                <span className="font-medium">
                  {messages.length > 0 
                    ? new Date(messages[messages.length - 1].sent_at).toLocaleDateString()
                    : 'No activity'
                  }
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Project Health Score */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5" />
              Project Health Score
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Overall Score */}
              <div className="text-center">
                <div className="text-3xl font-bold text-slate-900 mb-2">
                  {Math.round((progressPercentage + Math.min(responseRate, 100) + (overdueMilestones === 0 ? 100 : 50)) / 3)}
                </div>
                <p className="text-sm text-slate-600">Overall Health Score</p>
                <Progress 
                  value={Math.round((progressPercentage + Math.min(responseRate, 100) + (overdueMilestones === 0 ? 100 : 50)) / 3)} 
                  className="h-2 mt-2" 
                />
              </div>

              {/* Health Factors */}
              <div className="space-y-3 pt-4 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Progress Rate</span>
                  <Badge variant={progressPercentage > 50 ? "default" : "secondary"}>
                    {progressPercentage > 50 ? "Good" : "Needs Attention"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Contractor Interest</span>
                  <Badge variant={totalBids > 3 ? "default" : "secondary"}>
                    {totalBids > 3 ? "High" : "Moderate"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Timeline Status</span>
                  <Badge variant={overdueMilestones === 0 ? "default" : "destructive"}>
                    {overdueMilestones === 0 ? "On Track" : "Behind"}
                  </Badge>
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm text-slate-600">Budget Alignment</span>
                  <Badge variant={budgetUtilization <= 100 ? "default" : "secondary"}>
                    {budgetUtilization <= 100 ? "Within Budget" : "Over Budget"}
                  </Badge>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
