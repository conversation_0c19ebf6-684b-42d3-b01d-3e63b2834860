"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Calendar,
  DollarSign,
  CheckCircle,
  Clock,
  AlertTriangle,
  FileText,
  Plus,
  X
} from 'lucide-react'

export interface Milestone {
  id: string
  title: string
  description: string
  status: 'pending' | 'in-progress' | 'completed' | 'blocked'
  due_date: string
  completion_date?: string
  payment_amount?: number
  payment_percentage?: number
  dependencies: string[]
  deliverables: string[]
  notes: string
  created_at: string
  updated_at: string
}

interface MilestoneFormProps {
  milestone?: Milestone
  projectId: string
  existingMilestones: Milestone[]
  open: boolean
  onClose: () => void
  onSubmit: (milestone: Omit<Milestone, 'id' | 'created_at' | 'updated_at'>) => Promise<void>
}

const MILESTONE_STATUSES = [
  { value: 'pending', label: 'Pending', icon: Clock, color: 'text-slate-500' },
  { value: 'in-progress', label: 'In Progress', icon: Clock, color: 'text-blue-500' },
  { value: 'completed', label: 'Completed', icon: CheckCircle, color: 'text-green-500' },
  { value: 'blocked', label: 'Blocked', icon: AlertTriangle, color: 'text-red-500' }
]

export function MilestoneForm({
  milestone,
  projectId,
  existingMilestones,
  open,
  onClose,
  onSubmit
}: MilestoneFormProps) {
  const [loading, setLoading] = useState(false)
  const [newDeliverable, setNewDeliverable] = useState('')
  
  const [formData, setFormData] = useState({
    title: milestone?.title || '',
    description: milestone?.description || '',
    status: milestone?.status || 'pending',
    due_date: milestone?.due_date ? milestone.due_date.split('T')[0] : '',
    completion_date: milestone?.completion_date ? milestone.completion_date.split('T')[0] : '',
    payment_amount: milestone?.payment_amount || 0,
    payment_percentage: milestone?.payment_percentage || 0,
    dependencies: milestone?.dependencies || [],
    deliverables: milestone?.deliverables || [],
    notes: milestone?.notes || ''
  })

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const addDeliverable = () => {
    if (newDeliverable.trim() && !formData.deliverables.includes(newDeliverable.trim())) {
      setFormData(prev => ({
        ...prev,
        deliverables: [...prev.deliverables, newDeliverable.trim()]
      }))
      setNewDeliverable('')
    }
  }

  const removeDeliverable = (deliverableToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      deliverables: prev.deliverables.filter(d => d !== deliverableToRemove)
    }))
  }

  const toggleDependency = (milestoneId: string) => {
    setFormData(prev => ({
      ...prev,
      dependencies: prev.dependencies.includes(milestoneId)
        ? prev.dependencies.filter(id => id !== milestoneId)
        : [...prev.dependencies, milestoneId]
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim() || !formData.due_date) {
      return
    }

    setLoading(true)
    try {
      await onSubmit({
        title: formData.title,
        description: formData.description,
        status: formData.status as Milestone['status'],
        due_date: new Date(formData.due_date).toISOString(),
        completion_date: formData.completion_date ? new Date(formData.completion_date).toISOString() : undefined,
        payment_amount: formData.payment_amount,
        payment_percentage: formData.payment_percentage,
        dependencies: formData.dependencies,
        deliverables: formData.deliverables,
        notes: formData.notes
      })
      onClose()
    } finally {
      setLoading(false)
    }
  }

  const availableDependencies = existingMilestones.filter(m => 
    m.id !== milestone?.id && m.status !== 'completed'
  )

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {milestone ? 'Edit Milestone' : 'Add Milestone'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">Milestone Title *</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => handleInputChange('title', e.target.value)}
                  placeholder="e.g., Foundation Complete"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Describe what needs to be accomplished..."
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Select 
                    value={formData.status} 
                    onValueChange={(value) => handleInputChange('status', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {MILESTONE_STATUSES.map((status) => {
                        const Icon = status.icon
                        return (
                          <SelectItem key={status.value} value={status.value}>
                            <div className="flex items-center gap-2">
                              <Icon className={`h-4 w-4 ${status.color}`} />
                              {status.label}
                            </div>
                          </SelectItem>
                        )
                      })}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="due_date">Due Date *</Label>
                  <Input
                    id="due_date"
                    type="date"
                    value={formData.due_date}
                    onChange={(e) => handleInputChange('due_date', e.target.value)}
                    required
                  />
                </div>
              </div>

              {formData.status === 'completed' && (
                <div className="space-y-2">
                  <Label htmlFor="completion_date">Completion Date</Label>
                  <Input
                    id="completion_date"
                    type="date"
                    value={formData.completion_date}
                    onChange={(e) => handleInputChange('completion_date', e.target.value)}
                  />
                </div>
              )}
            </CardContent>
          </Card>

          {/* Payment Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5" />
                Payment Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="payment_amount">Payment Amount ($)</Label>
                  <Input
                    id="payment_amount"
                    type="number"
                    value={formData.payment_amount || ''}
                    onChange={(e) => handleInputChange('payment_amount', parseFloat(e.target.value) || 0)}
                    placeholder="0"
                    min="0"
                    step="0.01"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="payment_percentage">Payment Percentage (%)</Label>
                  <Input
                    id="payment_percentage"
                    type="number"
                    value={formData.payment_percentage || ''}
                    onChange={(e) => handleInputChange('payment_percentage', parseFloat(e.target.value) || 0)}
                    placeholder="0"
                    min="0"
                    max="100"
                    step="0.1"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Deliverables */}
          <Card>
            <CardHeader>
              <CardTitle>Deliverables</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input
                  value={newDeliverable}
                  onChange={(e) => setNewDeliverable(e.target.value)}
                  placeholder="Add deliverable (e.g., Electrical inspection passed)"
                  onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addDeliverable())}
                />
                <Button type="button" onClick={addDeliverable} variant="outline">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>

              {formData.deliverables.length > 0 && (
                <div className="space-y-2">
                  {formData.deliverables.map((deliverable, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                      <span className="text-sm">{deliverable}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() => removeDeliverable(deliverable)}
                        className="h-6 w-6 p-0 text-slate-500 hover:text-red-500"
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Dependencies */}
          {availableDependencies.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Dependencies</CardTitle>
                <p className="text-sm text-slate-600">
                  Select milestones that must be completed before this one can start
                </p>
              </CardHeader>
              <CardContent className="space-y-3">
                {availableDependencies.map((dep) => (
                  <div key={dep.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm">{dep.title}</h4>
                      <p className="text-xs text-slate-600">
                        Due: {new Date(dep.due_date).toLocaleDateString()}
                      </p>
                    </div>
                    <Switch
                      checked={formData.dependencies.includes(dep.id)}
                      onCheckedChange={() => toggleDependency(dep.id)}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Notes */}
          <Card>
            <CardHeader>
              <CardTitle>Notes</CardTitle>
            </CardHeader>
            <CardContent>
              <Textarea
                value={formData.notes}
                onChange={(e) => handleInputChange('notes', e.target.value)}
                placeholder="Add any additional notes or comments..."
                rows={3}
              />
            </CardContent>
          </Card>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : milestone ? 'Update Milestone' : 'Add Milestone'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
