"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useUser, UserRole } from "@/contexts/user-context"
import { User, Briefcase } from "lucide-react"

export function RoleSwitcher() {
  const { user, switchRole } = useUser()

  if (!user) return null

  const handleRoleSwitch = (role: UserRole) => {
    switchRole(role)
  }

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="bg-white border border-slate-200 rounded-lg shadow-lg p-3">
        <div className="text-xs text-slate-500 mb-2 text-center">Switch Role</div>
        <div className="flex space-x-2">
          <Button
            variant={user.role === "customer" ? "default" : "outline"}
            size="sm"
            onClick={() => handleRoleSwitch("customer")}
            className={`${
              user.role === "customer" 
                ? "bg-brand-primary hover:bg-brand-primary/90" 
                : "hover:bg-slate-50"
            }`}
          >
            <User className="h-3 w-3 mr-1" />
            Customer
          </Button>
          <Button
            variant={user.role === "pro" ? "default" : "outline"}
            size="sm"
            onClick={() => handleRoleSwitch("pro")}
            className={`${
              user.role === "pro" 
                ? "bg-brand-secondary hover:bg-brand-secondary/90" 
                : "hover:bg-slate-50"
            }`}
          >
            <Briefcase className="h-3 w-3 mr-1" />
            Pro
          </Button>
        </div>
      </div>
    </div>
  )
}

export default RoleSwitcher
