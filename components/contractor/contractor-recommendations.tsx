"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { contractorMatchingService, ContractorMatch, MatchingCriteria } from '@/services/contractor-matching'
import { useToastActions } from '@/components/ui/toast-system'
import { 
  Star, 
  MapPin, 
  Clock, 
  DollarSign, 
  CheckCircle, 
  MessageCircle,
  Calendar,
  Award,
  TrendingUp,
  Users,
  Zap,
  Target,
  Filter,
  RefreshCw
} from 'lucide-react'

interface ContractorRecommendationsProps {
  projectId: string
  criteria: MatchingCriteria
  onContactContractor: (contractorId: string) => void
  onViewProfile: (contractorId: string) => void
  className?: string
}

export function ContractorRecommendations({
  projectId,
  criteria,
  onContactContractor,
  onViewProfile,
  className
}: ContractorRecommendationsProps) {
  const [matches, setMatches] = useState<ContractorMatch[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const { showError, showSuccess } = useToastActions()

  useEffect(() => {
    loadRecommendations()
  }, [projectId, criteria])

  const loadRecommendations = async () => {
    setLoading(true)
    setError(null)

    try {
      const response = await contractorMatchingService.findMatches(projectId, criteria, 10)
      
      if (response.success && response.data) {
        setMatches(response.data)
        showSuccess(`Found ${response.data.length} matching contractors`)
      } else {
        setError(response.error || 'Failed to load recommendations')
        showError(response.error || 'Failed to load recommendations')
      }
    } catch (err) {
      setError('An unexpected error occurred')
      showError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const getMatchScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100'
    if (score >= 80) return 'text-blue-600 bg-blue-100'
    if (score >= 70) return 'text-amber-600 bg-amber-100'
    return 'text-slate-600 bg-slate-100'
  }

  const getAvailabilityBadge = (availability: ContractorMatch['availability']) => {
    if (!availability.available) {
      return <Badge variant="secondary">Not Available</Badge>
    }

    const startDate = new Date(availability.earliest_start)
    const daysUntilStart = Math.ceil((startDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))

    if (daysUntilStart <= 7) {
      return <Badge className="bg-green-100 text-green-800">Available This Week</Badge>
    } else if (daysUntilStart <= 30) {
      return <Badge className="bg-blue-100 text-blue-800">Available This Month</Badge>
    } else {
      return <Badge variant="outline">Available in {daysUntilStart} days</Badge>
    }
  }

  if (loading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Finding Perfect Contractors...</h3>
          <RefreshCw className="h-5 w-5 animate-spin text-blue-600" />
        </div>
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="w-16 h-16 bg-slate-200 rounded-full" />
                <div className="flex-1 space-y-3">
                  <div className="h-4 bg-slate-200 rounded w-1/3" />
                  <div className="h-3 bg-slate-200 rounded w-1/2" />
                  <div className="h-3 bg-slate-200 rounded w-2/3" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <div className="text-red-500 mb-4">
            <Target className="h-12 w-12 mx-auto mb-2" />
            <p className="font-medium">Failed to Load Recommendations</p>
            <p className="text-sm text-slate-600 mt-1">{error}</p>
          </div>
          <Button onClick={loadRecommendations} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    )
  }

  if (matches.length === 0) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Users className="h-12 w-12 text-slate-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 mb-2">
            No Matching Contractors Found
          </h3>
          <p className="text-slate-600 mb-4">
            Try adjusting your project criteria or expanding your search area.
          </p>
          <Button onClick={loadRecommendations} variant="outline">
            <Filter className="h-4 w-4 mr-2" />
            Adjust Criteria
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-slate-900">
            Recommended Contractors ({matches.length})
          </h3>
          <p className="text-sm text-slate-600">
            Ranked by compatibility with your project requirements
          </p>
        </div>
        <Button onClick={loadRecommendations} variant="outline" size="sm">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Contractor Cards */}
      <div className="space-y-4">
        {matches.map((match, index) => (
          <Card key={match.contractor.id} className="overflow-hidden hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                {/* Contractor Avatar */}
                <div className="relative">
                  <Avatar className="h-16 w-16">
                    <AvatarImage src={match.contractor.users.avatar_url} />
                    <AvatarFallback className="bg-blue-100 text-blue-600 text-lg font-semibold">
                      {match.contractor.business_name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  {index < 3 && (
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-amber-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </div>
                  )}
                </div>

                {/* Contractor Info */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="text-lg font-semibold text-slate-900 mb-1">
                        {match.contractor.business_name}
                      </h4>
                      <div className="flex items-center gap-2 mb-2">
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-amber-400 fill-current" />
                          <span className="font-medium">{match.contractor.rating_average}</span>
                          <span className="text-slate-500">({match.contractor.rating_count} reviews)</span>
                        </div>
                        {match.contractor.users.verified && (
                          <Badge variant="secondary" className="text-xs">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Verified
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Match Score */}
                    <div className="text-center">
                      <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getMatchScoreColor(match.match_score)}`}>
                        <Target className="h-4 w-4 mr-1" />
                        {match.match_score}% Match
                      </div>
                    </div>
                  </div>

                  {/* Key Metrics */}
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                    <div className="flex items-center gap-2 text-sm">
                      <Clock className="h-4 w-4 text-slate-400" />
                      <span className="text-slate-600">
                        {match.contractor.response_time}h response
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <TrendingUp className="h-4 w-4 text-slate-400" />
                      <span className="text-slate-600">
                        {match.contractor.completion_rate}% completion
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <MapPin className="h-4 w-4 text-slate-400" />
                      <span className="text-slate-600">
                        {match.distance.toFixed(1)} miles away
                      </span>
                    </div>
                    <div className="flex items-center gap-2 text-sm">
                      <DollarSign className="h-4 w-4 text-slate-400" />
                      <span className="text-slate-600">
                        ${match.estimated_cost.min.toLocaleString()} - ${match.estimated_cost.max.toLocaleString()}
                      </span>
                    </div>
                  </div>

                  {/* Availability */}
                  <div className="flex items-center gap-2 mb-4">
                    <Calendar className="h-4 w-4 text-slate-400" />
                    {getAvailabilityBadge(match.availability)}
                    {match.availability.available && (
                      <span className="text-sm text-slate-600">
                        Can start {new Date(match.availability.earliest_start).toLocaleDateString()}
                      </span>
                    )}
                  </div>

                  {/* Match Reasons */}
                  <div className="mb-4">
                    <h5 className="text-sm font-medium text-slate-700 mb-2">Why this contractor is a great match:</h5>
                    <div className="flex flex-wrap gap-2">
                      {match.match_reasons.slice(0, 3).map((reason, idx) => (
                        <Badge key={idx} variant="outline" className="text-xs">
                          <Zap className="h-3 w-3 mr-1" />
                          {reason}
                        </Badge>
                      ))}
                      {match.match_reasons.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{match.match_reasons.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Cost Confidence */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between text-sm mb-1">
                      <span className="text-slate-600">Cost Estimate Confidence</span>
                      <span className="font-medium">{Math.round(match.estimated_cost.confidence * 100)}%</span>
                    </div>
                    <Progress value={match.estimated_cost.confidence * 100} className="h-2" />
                  </div>

                  {/* Actions */}
                  <div className="flex items-center gap-3">
                    <Button
                      onClick={() => onContactContractor(match.contractor.id)}
                      className="flex-1 sm:flex-none"
                    >
                      <MessageCircle className="h-4 w-4 mr-2" />
                      Contact Contractor
                    </Button>
                    <Button
                      onClick={() => onViewProfile(match.contractor.id)}
                      variant="outline"
                      className="flex-1 sm:flex-none"
                    >
                      View Profile
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Load More */}
      {matches.length >= 10 && (
        <div className="text-center">
          <Button variant="outline" onClick={loadRecommendations}>
            Load More Contractors
          </Button>
        </div>
      )}
    </div>
  )
}
