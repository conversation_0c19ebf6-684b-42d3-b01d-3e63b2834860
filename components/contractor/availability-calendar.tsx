"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { contractorMatchingService, AvailabilitySlot } from '@/services/contractor-matching'
import { useToastActions } from '@/components/ui/toast-system'
import { useUser } from '@/contexts/user-context'
import { 
  Calendar,
  Plus,
  Edit,
  Trash2,
  Clock,
  CheckCircle,
  AlertCircle,
  Save,
  X
} from 'lucide-react'

interface AvailabilityCalendarProps {
  contractorId?: string
  viewOnly?: boolean
  onSlotSelect?: (slot: AvailabilitySlot) => void
  className?: string
}

export function AvailabilityCalendar({
  contractorId,
  viewOnly = false,
  onSlotSelect,
  className
}: AvailabilityCalendarProps) {
  const { user } = useUser()
  const { showError, showSuccess } = useToastActions()
  const [slots, setSlots] = useState<AvailabilitySlot[]>([])
  const [loading, setLoading] = useState(true)
  const [showForm, setShowForm] = useState(false)
  const [editingSlot, setEditingSlot] = useState<AvailabilitySlot | null>(null)
  const [currentMonth, setCurrentMonth] = useState(new Date())

  const [formData, setFormData] = useState({
    start_date: '',
    end_date: '',
    status: 'available' as AvailabilitySlot['status'],
    project_type: '',
    notes: ''
  })

  const effectiveContractorId = contractorId || user?.id

  useEffect(() => {
    if (effectiveContractorId) {
      loadAvailability()
    }
  }, [effectiveContractorId, currentMonth])

  const loadAvailability = async () => {
    if (!effectiveContractorId) return

    setLoading(true)
    try {
      const startOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
      const endOfMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0)

      const response = await contractorMatchingService.getContractorAvailability(
        effectiveContractorId,
        startOfMonth.toISOString(),
        endOfMonth.toISOString()
      )

      if (response.success && response.data) {
        setSlots(response.data)
      } else {
        showError(response.error || 'Failed to load availability')
      }
    } catch (error) {
      showError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleAddSlot = () => {
    setEditingSlot(null)
    setFormData({
      start_date: '',
      end_date: '',
      status: 'available',
      project_type: '',
      notes: ''
    })
    setShowForm(true)
  }

  const handleEditSlot = (slot: AvailabilitySlot) => {
    setEditingSlot(slot)
    setFormData({
      start_date: slot.start_date.split('T')[0],
      end_date: slot.end_date.split('T')[0],
      status: slot.status,
      project_type: slot.project_type || '',
      notes: slot.notes || ''
    })
    setShowForm(true)
  }

  const handleSubmitSlot = async () => {
    if (!effectiveContractorId || !formData.start_date || !formData.end_date) {
      showError('Please fill in all required fields')
      return
    }

    try {
      const newSlot = {
        start_date: new Date(formData.start_date).toISOString(),
        end_date: new Date(formData.end_date).toISOString(),
        status: formData.status,
        project_type: formData.project_type || undefined,
        notes: formData.notes || undefined
      }

      // For simplicity, we'll reload all slots after update
      // In a real app, you'd want more granular updates
      const updatedSlots = editingSlot 
        ? slots.map(slot => slot.id === editingSlot.id ? { ...slot, ...newSlot } : slot)
        : [...slots, { ...newSlot, id: `temp_${Date.now()}`, contractor_id: effectiveContractorId }]

      const response = await contractorMatchingService.updateAvailability(
        effectiveContractorId,
        updatedSlots.map(({ id, contractor_id, ...slot }) => slot)
      )

      if (response.success) {
        showSuccess(editingSlot ? 'Availability updated' : 'Availability added')
        setShowForm(false)
        loadAvailability()
      } else {
        showError(response.error || 'Failed to save availability')
      }
    } catch (error) {
      showError('An unexpected error occurred')
    }
  }

  const handleDeleteSlot = async (slotId: string) => {
    if (!effectiveContractorId) return

    try {
      const updatedSlots = slots.filter(slot => slot.id !== slotId)
      
      const response = await contractorMatchingService.updateAvailability(
        effectiveContractorId,
        updatedSlots.map(({ id, contractor_id, ...slot }) => slot)
      )

      if (response.success) {
        showSuccess('Availability deleted')
        loadAvailability()
      } else {
        showError(response.error || 'Failed to delete availability')
      }
    } catch (error) {
      showError('An unexpected error occurred')
    }
  }

  const getStatusColor = (status: AvailabilitySlot['status']) => {
    switch (status) {
      case 'available': return 'bg-green-100 text-green-800 border-green-200'
      case 'booked': return 'bg-red-100 text-red-800 border-red-200'
      case 'tentative': return 'bg-amber-100 text-amber-800 border-amber-200'
      default: return 'bg-slate-100 text-slate-800 border-slate-200'
    }
  }

  const getStatusIcon = (status: AvailabilitySlot['status']) => {
    switch (status) {
      case 'available': return <CheckCircle className="h-3 w-3" />
      case 'booked': return <X className="h-3 w-3" />
      case 'tentative': return <AlertCircle className="h-3 w-3" />
      default: return <Clock className="h-3 w-3" />
    }
  }

  const formatDateRange = (startDate: string, endDate: string) => {
    const start = new Date(startDate)
    const end = new Date(endDate)
    
    if (start.toDateString() === end.toDateString()) {
      return start.toLocaleDateString()
    }
    
    return `${start.toLocaleDateString()} - ${end.toLocaleDateString()}`
  }

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentMonth(prev => {
      const newMonth = new Date(prev)
      if (direction === 'prev') {
        newMonth.setMonth(newMonth.getMonth() - 1)
      } else {
        newMonth.setMonth(newMonth.getMonth() + 1)
      }
      return newMonth
    })
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {viewOnly ? 'Availability Calendar' : 'Manage Availability'}
            </CardTitle>
            {!viewOnly && (
              <Button onClick={handleAddSlot} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Availability
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {/* Month Navigation */}
          <div className="flex items-center justify-between mb-6">
            <Button variant="outline" onClick={() => navigateMonth('prev')}>
              ← Previous
            </Button>
            <h3 className="text-lg font-semibold">
              {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </h3>
            <Button variant="outline" onClick={() => navigateMonth('next')}>
              Next →
            </Button>
          </div>

          {/* Availability Slots */}
          {loading ? (
            <div className="space-y-3">
              {Array.from({ length: 3 }).map((_, i) => (
                <div key={i} className="h-16 bg-slate-100 rounded-lg animate-pulse" />
              ))}
            </div>
          ) : slots.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No availability slots for this month</p>
              {!viewOnly && (
                <Button onClick={handleAddSlot} variant="outline" className="mt-4">
                  Add Your First Slot
                </Button>
              )}
            </div>
          ) : (
            <div className="space-y-3">
              {slots.map((slot) => (
                <div
                  key={slot.id}
                  className={`p-4 rounded-lg border-2 transition-all ${getStatusColor(slot.status)} ${
                    onSlotSelect ? 'cursor-pointer hover:shadow-md' : ''
                  }`}
                  onClick={() => onSlotSelect?.(slot)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        {getStatusIcon(slot.status)}
                        <span className="font-medium">
                          {formatDateRange(slot.start_date, slot.end_date)}
                        </span>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {slot.status.charAt(0).toUpperCase() + slot.status.slice(1)}
                      </Badge>
                      {slot.project_type && (
                        <Badge variant="secondary" className="text-xs">
                          {slot.project_type}
                        </Badge>
                      )}
                    </div>

                    {!viewOnly && (
                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleEditSlot(slot)
                          }}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            handleDeleteSlot(slot.id)
                          }}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                  </div>

                  {slot.notes && (
                    <p className="text-sm mt-2 opacity-75">{slot.notes}</p>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Slot Modal */}
      {showForm && (
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>
                {editingSlot ? 'Edit Availability' : 'Add Availability'}
              </DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start_date">Start Date</Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={formData.start_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, start_date: e.target.value }))}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_date">End Date</Label>
                  <Input
                    id="end_date"
                    type="date"
                    value={formData.end_date}
                    onChange={(e) => setFormData(prev => ({ ...prev, end_date: e.target.value }))}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="status">Status</Label>
                <Select 
                  value={formData.status} 
                  onValueChange={(value: AvailabilitySlot['status']) => 
                    setFormData(prev => ({ ...prev, status: value }))
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="available">Available</SelectItem>
                    <SelectItem value="booked">Booked</SelectItem>
                    <SelectItem value="tentative">Tentative</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="project_type">Project Type (Optional)</Label>
                <Input
                  id="project_type"
                  value={formData.project_type}
                  onChange={(e) => setFormData(prev => ({ ...prev, project_type: e.target.value }))}
                  placeholder="e.g., Kitchen Renovation"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Notes (Optional)</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Any additional notes..."
                  rows={3}
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setShowForm(false)}>
                Cancel
              </Button>
              <Button onClick={handleSubmitSlot}>
                <Save className="h-4 w-4 mr-2" />
                {editingSlot ? 'Update' : 'Add'} Availability
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}
