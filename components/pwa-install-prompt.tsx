"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { EnhancedCard } from '@/components/ui/enhanced-card'
import { usePWA } from '@/hooks/use-pwa'
import { Download, X, Smartphone, Monitor } from 'lucide-react'

export function PWAInstallPrompt() {
  const { isInstallable, isInstalled, installApp } = usePWA()
  const [isDismissed, setIsDismissed] = useState(false)
  const [isInstalling, setIsInstalling] = useState(false)

  useEffect(() => {
    // Check if user has previously dismissed the prompt
    const dismissed = localStorage.getItem('pwa-install-dismissed')
    if (dismissed) {
      setIsDismissed(true)
    }
  }, [])

  const handleInstall = async () => {
    setIsInstalling(true)
    
    try {
      const success = await installApp()
      if (success) {
        console.log('PWA installed successfully')
      }
    } catch (error) {
      console.error('Error installing PWA:', error)
    } finally {
      setIsInstalling(false)
    }
  }

  const handleDismiss = () => {
    setIsDismissed(true)
    localStorage.setItem('pwa-install-dismissed', 'true')
  }

  // Don't show if not installable, already installed, or dismissed
  if (!isInstallable || isInstalled || isDismissed) {
    return null
  }

  return (
    <div className="fixed bottom-4 left-4 right-4 z-50 md:left-auto md:right-4 md:max-w-sm">
      <EnhancedCard className="p-4 shadow-lg border-brand-primary/20 bg-white">
        <div className="flex items-start space-x-3">
          <div className="flex-shrink-0 p-2 bg-brand-primary/10 rounded-lg">
            <Download className="h-5 w-5 text-brand-primary" />
          </div>
          
          <div className="flex-1 min-w-0">
            <h3 className="font-medium text-slate-900 mb-1">
              Install RenovHub
            </h3>
            <p className="text-sm text-slate-600 mb-3">
              Get the full app experience with offline access and push notifications.
            </p>
            
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                onClick={handleInstall}
                disabled={isInstalling}
                className="bg-brand-primary hover:bg-brand-primary/90"
              >
                {isInstalling ? (
                  <>
                    <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2" />
                    Installing...
                  </>
                ) : (
                  <>
                    <Download className="h-3 w-3 mr-2" />
                    Install
                  </>
                )}
              </Button>
              
              <Button
                size="sm"
                variant="ghost"
                onClick={handleDismiss}
                className="text-slate-500 hover:text-slate-700"
              >
                <X className="h-3 w-3 mr-1" />
                Dismiss
              </Button>
            </div>
          </div>
        </div>
      </EnhancedCard>
    </div>
  )
}

// Component for showing install instructions for different platforms
export function PWAInstallInstructions() {
  const [platform, setPlatform] = useState<'ios' | 'android' | 'desktop' | 'unknown'>('unknown')
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    if (typeof navigator !== 'undefined') {
      const userAgent = navigator.userAgent.toLowerCase()

      if (/iphone|ipad|ipod/.test(userAgent)) {
        setPlatform('ios')
      } else if (/android/.test(userAgent)) {
        setPlatform('android')
      } else if (/windows|mac|linux/.test(userAgent)) {
        setPlatform('desktop')
      }
    }
  }, [])

  const getInstructions = () => {
    switch (platform) {
      case 'ios':
        return {
          icon: <Smartphone className="h-8 w-8 text-blue-500" />,
          title: 'Install on iOS',
          steps: [
            'Tap the Share button in Safari',
            'Scroll down and tap "Add to Home Screen"',
            'Tap "Add" to install RenovHub'
          ]
        }
      
      case 'android':
        return {
          icon: <Smartphone className="h-8 w-8 text-green-500" />,
          title: 'Install on Android',
          steps: [
            'Tap the menu button (⋮) in Chrome',
            'Select "Add to Home screen"',
            'Tap "Add" to install RenovHub'
          ]
        }
      
      case 'desktop':
        return {
          icon: <Monitor className="h-8 w-8 text-purple-500" />,
          title: 'Install on Desktop',
          steps: [
            'Look for the install icon in your browser\'s address bar',
            'Click the install button',
            'Follow the prompts to add RenovHub to your desktop'
          ]
        }
      
      default:
        return {
          icon: <Download className="h-8 w-8 text-slate-500" />,
          title: 'Install RenovHub',
          steps: [
            'Look for an install prompt in your browser',
            'Follow your browser\'s installation instructions',
            'Enjoy the full app experience!'
          ]
        }
    }
  }

  const instructions = getInstructions()

  return (
    <EnhancedCard className="p-6 max-w-md mx-auto">
      <div className="text-center mb-6">
        {instructions.icon}
        <h3 className="text-lg font-medium text-slate-900 mt-3 mb-2">
          {instructions.title}
        </h3>
        <p className="text-sm text-slate-600">
          Install RenovHub for the best experience with offline access and notifications.
        </p>
      </div>
      
      <div className="space-y-3">
        {instructions.steps.map((step, index) => (
          <div key={index} className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-6 h-6 bg-brand-primary text-white rounded-full flex items-center justify-center text-sm font-medium">
              {index + 1}
            </div>
            <p className="text-sm text-slate-700 pt-0.5">{step}</p>
          </div>
        ))}
      </div>
      
      <div className="mt-6 p-3 bg-slate-50 rounded-lg">
        <p className="text-xs text-slate-600">
          <strong>Benefits:</strong> Faster loading, offline access, push notifications, 
          and a native app-like experience on your device.
        </p>
      </div>
    </EnhancedCard>
  )
}

// Offline indicator component
export function OfflineIndicator() {
  const { isOnline } = usePWA()

  if (isOnline) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-yellow-500 text-yellow-900 px-4 py-2 text-center text-sm font-medium">
      📡 You're currently offline. Some features may be limited.
    </div>
  )
}
