'use client'

import { useState, useEffect } from 'react'
import { supabase } from '@/lib/supabase'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { CheckCircle, XCircle, AlertCircle } from 'lucide-react'

interface DatabaseStatus {
  connected: boolean
  tablesExist: {
    users: boolean
    contractors: boolean
    projects: boolean
    bids: boolean
    reviews: boolean
  }
  error?: string
}

export function DatabaseStatus() {
  const [status, setStatus] = useState<DatabaseStatus | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    checkDatabaseStatus()
  }, [])

  const checkDatabaseStatus = async () => {
    try {
      setLoading(true)
      
      // Test basic connection
      const { data: connectionTest, error: connectionError } = await supabase
        .from('users')
        .select('count')
        .limit(1)

      if (connectionError && !connectionError.message?.includes('relation')) {
        setStatus({
          connected: false,
          tablesExist: {
            users: false,
            contractors: false,
            projects: false,
            bids: false,
            reviews: false
          },
          error: connectionError.message
        })
        return
      }

      // Check if tables exist
      const tables = ['users', 'contractors', 'projects', 'bids', 'reviews']
      const tableStatus: Record<string, boolean> = {}

      for (const table of tables) {
        try {
          const { error } = await supabase
            .from(table)
            .select('count')
            .limit(1)
          
          tableStatus[table] = !error || !error.message?.includes('relation')
        } catch {
          tableStatus[table] = false
        }
      }

      setStatus({
        connected: true,
        tablesExist: {
          users: tableStatus.users,
          contractors: tableStatus.contractors,
          projects: tableStatus.projects,
          bids: tableStatus.bids,
          reviews: tableStatus.reviews
        }
      })

    } catch (error) {
      setStatus({
        connected: false,
        tablesExist: {
          users: false,
          contractors: false,
          projects: false,
          bids: false,
          reviews: false
        },
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5" />
            Database Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p>Checking database connection...</p>
        </CardContent>
      </Card>
    )
  }

  if (!status) {
    return null
  }

  const allTablesExist = Object.values(status.tablesExist).every(exists => exists)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {status.connected && allTablesExist ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <XCircle className="h-5 w-5 text-red-500" />
          )}
          Database Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <span>Connection:</span>
          <Badge variant={status.connected ? "default" : "destructive"}>
            {status.connected ? "Connected" : "Disconnected"}
          </Badge>
        </div>

        {status.error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-700">Error: {status.error}</p>
          </div>
        )}

        <div className="space-y-2">
          <h4 className="font-medium">Tables:</h4>
          {Object.entries(status.tablesExist).map(([table, exists]) => (
            <div key={table} className="flex items-center justify-between">
              <span className="capitalize">{table}</span>
              <Badge variant={exists ? "default" : "destructive"}>
                {exists ? "Exists" : "Missing"}
              </Badge>
            </div>
          ))}
        </div>

        {!allTablesExist && (
          <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-700">
              Some database tables are missing. Please run the database migrations:
            </p>
            <code className="block mt-2 p-2 bg-gray-100 rounded text-xs">
              npm run db:setup
            </code>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
