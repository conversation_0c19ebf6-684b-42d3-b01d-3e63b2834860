"use client"

import { cn } from "@/lib/utils"

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "default" | "premium" | "gradient"
  className?: string
}

export function LoadingSpinner({ size = "md", variant = "default", className }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6", 
    lg: "w-8 h-8",
    xl: "w-12 h-12"
  }

  const variantClasses = {
    default: "border-slate-200 border-t-slate-600",
    premium: "border-blue-200 border-t-blue-600",
    gradient: "border-transparent bg-gradient-to-r from-blue-600 to-emerald-600"
  }

  if (variant === "gradient") {
    return (
      <div className={cn("relative", sizeClasses[size], className)}>
        <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-600 to-emerald-600 animate-spin">
          <div className="absolute inset-1 rounded-full bg-white"></div>
        </div>
      </div>
    )
  }

  return (
    <div
      className={cn(
        "animate-spin rounded-full border-2",
        sizeClasses[size],
        variantClasses[variant],
        className
      )}
    />
  )
}

interface LoadingSkeletonProps {
  className?: string
  variant?: "default" | "premium"
}

export function LoadingSkeleton({ className, variant = "default" }: LoadingSkeletonProps) {
  const baseClasses = variant === "premium" 
    ? "animate-pulse bg-gradient-to-r from-slate-100 via-slate-200 to-slate-100 bg-[length:200%_100%] rounded-xl"
    : "animate-pulse bg-slate-200 rounded-lg"

  return <div className={cn(baseClasses, className)} />
}

interface LoadingCardProps {
  className?: string
}

export function LoadingCard({ className }: LoadingCardProps) {
  return (
    <div className={cn("bg-white rounded-2xl p-6 shadow-premium-md", className)}>
      <div className="space-y-4">
        <LoadingSkeleton className="h-4 w-3/4" variant="premium" />
        <LoadingSkeleton className="h-3 w-1/2" variant="premium" />
        <div className="space-y-2">
          <LoadingSkeleton className="h-3 w-full" variant="premium" />
          <LoadingSkeleton className="h-3 w-5/6" variant="premium" />
        </div>
        <div className="flex space-x-2 pt-2">
          <LoadingSkeleton className="h-8 w-20" variant="premium" />
          <LoadingSkeleton className="h-8 w-24" variant="premium" />
        </div>
      </div>
    </div>
  )
}

interface LoadingPageProps {
  title?: string
  description?: string
}

export function LoadingPage({ title = "Loading...", description = "Please wait while we prepare your content" }: LoadingPageProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50/30 flex items-center justify-center">
      <div className="text-center space-y-8 max-w-md mx-auto px-6">
        {/* Premium Loading Animation */}
        <div className="relative">
          <div className="w-20 h-20 mx-auto">
            <LoadingSpinner size="xl" variant="gradient" />
          </div>
          <div className="absolute inset-0 w-20 h-20 mx-auto rounded-full bg-gradient-to-r from-blue-500/20 to-emerald-500/20 animate-ping"></div>
        </div>

        {/* Loading Text */}
        <div className="space-y-3">
          <h2 className="text-2xl font-bold text-slate-900">{title}</h2>
          <p className="text-slate-600">{description}</p>
        </div>

        {/* Progress Dots */}
        <div className="flex justify-center space-x-2">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"
              style={{ animationDelay: `${i * 0.2}s` }}
            />
          ))}
        </div>
      </div>
    </div>
  )
}

interface ProgressBarProps {
  progress: number
  className?: string
  variant?: "default" | "premium" | "gradient"
  size?: "sm" | "md" | "lg"
}

export function ProgressBar({ progress, className, variant = "default", size = "md" }: ProgressBarProps) {
  const sizeClasses = {
    sm: "h-1",
    md: "h-2", 
    lg: "h-3"
  }

  const variantClasses = {
    default: "bg-slate-200",
    premium: "bg-slate-100 border border-slate-200",
    gradient: "bg-gradient-to-r from-slate-100 to-slate-200"
  }

  const fillClasses = {
    default: "bg-blue-600",
    premium: "bg-gradient-to-r from-blue-600 to-blue-700 shadow-sm",
    gradient: "bg-gradient-to-r from-blue-600 via-purple-600 to-emerald-600"
  }

  return (
    <div className={cn("w-full rounded-full overflow-hidden", sizeClasses[size], variantClasses[variant], className)}>
      <div
        className={cn("h-full transition-all duration-500 ease-out rounded-full", fillClasses[variant])}
        style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
      />
    </div>
  )
}

// Pulse Loading Animation for Images
export function ImagePlaceholder({ className }: { className?: string }) {
  return (
    <div className={cn("bg-gradient-to-br from-slate-100 to-slate-200 animate-pulse rounded-xl", className)}>
      <div className="flex items-center justify-center h-full">
        <svg className="w-8 h-8 text-slate-400" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
        </svg>
      </div>
    </div>
  )
}
