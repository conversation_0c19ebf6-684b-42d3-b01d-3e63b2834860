import * as React from "react"

import { cn } from "@/lib/utils"

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<"textarea">
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        "flex min-h-[140px] w-full rounded-xl border-2 border-slate-200 bg-white px-6 py-4 text-base sm:text-sm text-raisin-black placeholder:text-raisin-black/40 focus:border-philippine-green focus:outline-none focus:ring-4 focus:ring-philippine-green/10 focus:ring-offset-0 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 resize-none touch-manipulation hover:border-slate-300 hover:shadow-md focus:shadow-lg focus:scale-[1.02]",
        className
      )}
      ref={ref}
      {...props}
    />
  )
})
Textarea.displayName = "Textarea"

export { Textarea }
