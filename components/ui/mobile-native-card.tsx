import * as React from "react"
import { cn } from "@/lib/utils"

// Mobile-Native Card Variants
const cardVariants = {
  minimal: "bg-white rounded-xl border border-slate-100 shadow-none hover:shadow-sm transition-all duration-200",
  flat: "bg-slate-50/50 rounded-xl border-0 shadow-none",
  elevated: "bg-white rounded-2xl border-0 shadow-sm hover:shadow-md transition-all duration-200",
  interactive: "bg-white rounded-xl border border-slate-100 shadow-none hover:shadow-md hover:scale-[1.01] active:scale-[0.99] transition-all duration-150 cursor-pointer",
}

const spacingVariants = {
  xs: "p-2",
  sm: "p-3",
  md: "p-4",
  lg: "p-6",
}

interface MobileNativeCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: keyof typeof cardVariants
  spacing?: keyof typeof spacingVariants
  children: React.ReactNode
}

export const MobileNativeCard = React.forwardRef<HTMLDivElement, MobileNativeCardProps>(
  ({ className, variant = "minimal", spacing = "md", children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        cardVariants[variant],
        spacingVariants[spacing],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
MobileNativeCard.displayName = "MobileNativeCard"

// Mobile-Native List Item
interface MobileNativeListItemProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  interactive?: boolean
}

export const MobileNativeListItem = React.forwardRef<HTMLDivElement, MobileNativeListItemProps>(
  ({ className, children, interactive = false, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        "flex items-center p-3 rounded-xl transition-colors duration-150 min-h-[44px]",
        interactive && "hover:bg-slate-50 active:bg-slate-100 cursor-pointer",
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
)
MobileNativeListItem.displayName = "MobileNativeListItem"

// Mobile-Native Section
interface MobileNativeSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  title?: string
  subtitle?: string
  children: React.ReactNode
}

export const MobileNativeSection = React.forwardRef<HTMLDivElement, MobileNativeSectionProps>(
  ({ className, title, subtitle, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn("space-y-3", className)}
      {...props}
    >
      {(title || subtitle) && (
        <div className="space-y-1">
          {title && (
            <h3 className="text-base sm:text-lg font-semibold text-slate-900 leading-tight">
              {title}
            </h3>
          )}
          {subtitle && (
            <p className="text-sm text-slate-600">
              {subtitle}
            </p>
          )}
        </div>
      )}
      {children}
    </div>
  )
)
MobileNativeSection.displayName = "MobileNativeSection"

// Mobile-Native Stats Card
interface MobileNativeStatsCardProps {
  label: string
  value: string | number
  icon?: React.ComponentType<{ className?: string }>
  trend?: {
    value: string
    direction: 'up' | 'down' | 'neutral'
  }
  className?: string
}

export const MobileNativeStatsCard = React.forwardRef<HTMLDivElement, MobileNativeStatsCardProps>(
  ({ label, value, icon: Icon, trend, className }, ref) => (
    <MobileNativeCard
      ref={ref}
      variant="minimal"
      spacing="sm"
      className={cn("", className)}
    >
      <div className="flex flex-col gap-1">
        <div className="flex items-center justify-between">
          <p className="text-xs text-slate-500 truncate">{label}</p>
          {Icon && <Icon className="h-3.5 w-3.5 text-slate-400 flex-shrink-0" />}
        </div>
        <div className="flex items-end justify-between">
          <p className="text-lg font-bold text-slate-900">{value}</p>
          {trend && (
            <span className={cn(
              "text-xs font-medium",
              trend.direction === 'up' && "text-green-600",
              trend.direction === 'down' && "text-red-600",
              trend.direction === 'neutral' && "text-slate-500"
            )}>
              {trend.value}
            </span>
          )}
        </div>
      </div>
    </MobileNativeCard>
  )
)
MobileNativeStatsCard.displayName = "MobileNativeStatsCard"

// Mobile-Native Action Card
interface MobileNativeActionCardProps extends React.HTMLAttributes<HTMLDivElement> {
  title: string
  description?: string
  icon?: React.ComponentType<{ className?: string }>
  action?: React.ReactNode
  children?: React.ReactNode
}

export const MobileNativeActionCard = React.forwardRef<HTMLDivElement, MobileNativeActionCardProps>(
  ({ className, title, description, icon: Icon, action, children, ...props }, ref) => (
    <MobileNativeCard
      ref={ref}
      variant="interactive"
      spacing="md"
      className={cn("", className)}
      {...props}
    >
      <div className="flex items-start space-x-3">
        {Icon && (
          <div className="flex-shrink-0 w-8 h-8 bg-slate-100 rounded-lg flex items-center justify-center">
            <Icon className="h-4 w-4 text-slate-600" />
          </div>
        )}
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-slate-900 mb-1">{title}</h4>
          {description && (
            <p className="text-xs text-slate-600 mb-2">{description}</p>
          )}
          {children}
        </div>
        {action && (
          <div className="flex-shrink-0">
            {action}
          </div>
        )}
      </div>
    </MobileNativeCard>
  )
)
MobileNativeActionCard.displayName = "MobileNativeActionCard"

export {
  cardVariants,
  spacingVariants,
}
