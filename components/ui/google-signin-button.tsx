"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { authService } from "@/services/auth"
import { UserRole } from "@/types"
import { Loader2 } from "lucide-react"

interface GoogleSignInButtonProps {
  role?: UserRole
  variant?: "default" | "outline"
  size?: "sm" | "md" | "lg"
  className?: string
  children?: React.ReactNode
  onSuccess?: () => void
  onError?: (error: string) => void
}

export function GoogleSignInButton({
  role,
  variant = "outline",
  size = "lg",
  className = "",
  children,
  onSuccess,
  onError
}: GoogleSignInButtonProps) {
  const [loading, setLoading] = useState(false)

  const handleGoogleSignIn = async () => {
    setLoading(true)
    
    try {
      const result = await authService.signInWithGoogle({ 
        role,
        redirectTo: `${window.location.origin}/auth/callback`
      })
      
      if (result.success) {
        onSuccess?.()
        // OAuth redirect will happen automatically
      } else {
        onError?.(result.error || 'Google sign-in failed')
      }
    } catch (error) {
      console.error('Google sign-in error:', error)
      onError?.('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Button
      type="button"
      variant={variant}
      size={size}
      onClick={handleGoogleSignIn}
      disabled={loading}
      className={`relative ${className}`}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin mr-2" />
      ) : (
        <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24">
          <path
            fill="currentColor"
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
          />
          <path
            fill="currentColor"
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
          />
          <path
            fill="currentColor"
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
          />
          <path
            fill="currentColor"
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
          />
        </svg>
      )}
      {children || (loading ? 'Signing in...' : 'Continue with Google')}
    </Button>
  )
}

// Preset variants for common use cases
export function GoogleSignInButtonPrimary(props: Omit<GoogleSignInButtonProps, 'variant'>) {
  return (
    <GoogleSignInButton
      {...props}
      variant="default"
      className={`bg-white hover:bg-gray-50 text-gray-900 border border-gray-300 shadow-sm hover:shadow-md transition-all duration-300 ${props.className || ''}`}
    />
  )
}

export function GoogleSignInButtonSecondary(props: Omit<GoogleSignInButtonProps, 'variant'>) {
  return (
    <GoogleSignInButton
      {...props}
      variant="outline"
      className={`border-2 border-slate-200 hover:border-slate-300 hover:bg-slate-50 transition-all duration-300 ${props.className || ''}`}
    />
  )
}
