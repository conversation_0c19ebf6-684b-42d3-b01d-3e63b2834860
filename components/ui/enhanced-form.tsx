"use client"

import React from "react"
import { useForm, UseFormReturn, FieldValues, Path } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { cn } from "@/lib/utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { FormField, FormOption } from "@/types"
import { AlertCircle, Upload, X } from "lucide-react"

// Enhanced Form Component
interface EnhancedFormProps<T extends FieldValues> {
  fields: FormField[]
  schema: z.ZodSchema<T>
  onSubmit: (data: T) => void | Promise<void>
  defaultValues?: Partial<T>
  loading?: boolean
  submitText?: string
  className?: string
  children?: React.ReactNode
}

export function EnhancedForm<T extends FieldValues>({
  fields,
  schema,
  onSubmit,
  defaultValues,
  loading = false,
  submitText = "Submit",
  className,
  children
}: EnhancedFormProps<T>) {
  const form = useForm<T>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as any
  })

  const handleSubmit = async (data: T) => {
    try {
      await onSubmit(data)
    } catch (error) {
      console.error("Form submission error:", error)
    }
  }

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className={cn("space-y-6", className)}>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {fields.map((field) => (
          <FormFieldRenderer
            key={field.name}
            field={field}
            form={form}
          />
        ))}
      </div>
      
      {children}
      
      <div className="flex justify-end space-x-3 pt-6 border-t border-slate-200">
        <Button
          type="submit"
          disabled={loading}
          className="bg-brand-primary hover:bg-brand-primary/90 text-white px-8"
        >
          {loading ? "Submitting..." : submitText}
        </Button>
      </div>
    </form>
  )
}

// Form Field Renderer
interface FormFieldRendererProps<T extends FieldValues> {
  field: FormField
  form: UseFormReturn<T>
}

function FormFieldRenderer<T extends FieldValues>({ field, form }: FormFieldRendererProps<T>) {
  const error = form.formState.errors[field.name as Path<T>]
  const isFullWidth = field.type === "textarea" || field.type === "file"

  return (
    <div className={cn("space-y-2", isFullWidth && "md:col-span-2")}>
      <Label htmlFor={field.name} className="text-sm font-medium text-slate-700">
        {field.label}
        {field.required && <span className="text-red-500 ml-1">*</span>}
      </Label>
      
      <FieldInput field={field} form={form} />
      
      {error && (
        <div className="flex items-center space-x-1 text-red-600 text-sm">
          <AlertCircle className="h-4 w-4" />
          <span>{error.message as string}</span>
        </div>
      )}
    </div>
  )
}

// Field Input Component
function FieldInput<T extends FieldValues>({ field, form }: FormFieldRendererProps<T>) {
  const { register, setValue, watch } = form
  const value = watch(field.name as Path<T>)

  switch (field.type) {
    case "text":
    case "email":
    case "password":
    case "number":
      return (
        <Input
          {...register(field.name as Path<T>)}
          type={field.type}
          placeholder={field.placeholder}
          className="border-slate-200 focus:border-brand-primary"
        />
      )

    case "textarea":
      return (
        <Textarea
          {...register(field.name as Path<T>)}
          placeholder={field.placeholder}
          className="border-slate-200 focus:border-brand-primary min-h-[100px]"
        />
      )

    case "select":
      return (
        <Select
          value={value}
          onValueChange={(val) => setValue(field.name as Path<T>, val as any)}
        >
          <SelectTrigger className="border-slate-200 focus:border-brand-primary">
            <SelectValue placeholder={field.placeholder} />
          </SelectTrigger>
          <SelectContent>
            {field.options?.map((option) => (
              <SelectItem key={option.value} value={option.value} disabled={option.disabled}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )

    case "checkbox":
      return (
        <div className="flex items-center space-x-2">
          <Checkbox
            checked={value}
            onCheckedChange={(checked) => setValue(field.name as Path<T>, checked as any)}
          />
          <Label className="text-sm text-slate-600">{field.placeholder}</Label>
        </div>
      )

    case "radio":
      return (
        <RadioGroup
          value={value}
          onValueChange={(val) => setValue(field.name as Path<T>, val as any)}
        >
          {field.options?.map((option) => (
            <div key={option.value} className="flex items-center space-x-2">
              <RadioGroupItem value={option.value} disabled={option.disabled} />
              <Label className="text-sm text-slate-600">{option.label}</Label>
            </div>
          ))}
        </RadioGroup>
      )

    case "file":
      return <FileUpload field={field} form={form} />

    case "date":
      return (
        <Input
          {...register(field.name as Path<T>)}
          type="date"
          className="border-slate-200 focus:border-brand-primary"
        />
      )

    default:
      return (
        <Input
          {...register(field.name as Path<T>)}
          placeholder={field.placeholder}
          className="border-slate-200 focus:border-brand-primary"
        />
      )
  }
}

// File Upload Component
function FileUpload<T extends FieldValues>({ field, form }: FormFieldRendererProps<T>) {
  const { setValue, watch } = form
  const files = watch(field.name as Path<T>) as File[] || []

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || [])
    setValue(field.name as Path<T>, selectedFiles as any)
  }

  const removeFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index)
    setValue(field.name as Path<T>, updatedFiles as any)
  }

  return (
    <div className="space-y-3">
      <div className="border-2 border-dashed border-slate-200 rounded-lg p-6 text-center hover:border-brand-primary transition-colors">
        <Upload className="h-8 w-8 text-slate-400 mx-auto mb-2" />
        <p className="text-sm text-slate-600 mb-2">
          Click to upload or drag and drop
        </p>
        <p className="text-xs text-slate-400">
          PNG, JPG, PDF up to 10MB
        </p>
        <input
          type="file"
          multiple
          onChange={handleFileChange}
          className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
        />
      </div>

      {files.length > 0 && (
        <div className="space-y-2">
          {files.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-2 bg-slate-50 rounded">
              <span className="text-sm text-slate-700 truncate">{file.name}</span>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeFile(index)}
                className="h-6 w-6 p-0 text-slate-400 hover:text-red-500"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

// Quick Form Builder for common forms
export function createProjectForm() {
  const schema = z.object({
    title: z.string().min(1, "Title is required"),
    description: z.string().min(10, "Description must be at least 10 characters"),
    category: z.string().min(1, "Category is required"),
    budget: z.string().min(1, "Budget is required"),
    timeline: z.string().min(1, "Timeline is required"),
    location: z.string().min(1, "Location is required"),
    photos: z.array(z.instanceof(File)).optional()
  })

  const fields: FormField[] = [
    {
      name: "title",
      label: "Project Title",
      type: "text",
      required: true,
      placeholder: "e.g., Kitchen Renovation"
    },
    {
      name: "category",
      label: "Category",
      type: "select",
      required: true,
      placeholder: "Select category",
      options: [
        { value: "kitchen", label: "Kitchen" },
        { value: "bathroom", label: "Bathroom" },
        { value: "flooring", label: "Flooring" },
        { value: "painting", label: "Painting" },
        { value: "electrical", label: "Electrical" },
        { value: "plumbing", label: "Plumbing" }
      ]
    },
    {
      name: "budget",
      label: "Budget Range",
      type: "select",
      required: true,
      placeholder: "Select budget range",
      options: [
        { value: "$1,000 - $5,000", label: "$1,000 - $5,000" },
        { value: "$5,000 - $15,000", label: "$5,000 - $15,000" },
        { value: "$15,000 - $30,000", label: "$15,000 - $30,000" },
        { value: "$30,000 - $50,000", label: "$30,000 - $50,000" },
        { value: "$50,000+", label: "$50,000+" }
      ]
    },
    {
      name: "timeline",
      label: "Timeline",
      type: "select",
      required: true,
      placeholder: "Select timeline",
      options: [
        { value: "Within 2 weeks", label: "Within 2 weeks" },
        { value: "Within 1 month", label: "Within 1 month" },
        { value: "1-3 months", label: "1-3 months" },
        { value: "3-6 months", label: "3-6 months" },
        { value: "Flexible", label: "Flexible" }
      ]
    },
    {
      name: "location",
      label: "Project Location",
      type: "text",
      required: true,
      placeholder: "Enter full address"
    },
    {
      name: "description",
      label: "Project Description",
      type: "textarea",
      required: true,
      placeholder: "Describe your project in detail..."
    },
    {
      name: "photos",
      label: "Project Photos",
      type: "file",
      required: false
    }
  ]

  return { schema, fields }
}
