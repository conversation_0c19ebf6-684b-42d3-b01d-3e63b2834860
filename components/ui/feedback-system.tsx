"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Star, ThumbsUp, ThumbsDown, MessageSquare, Send, X } from "lucide-react"
import { cn } from "@/lib/utils"

interface RatingProps {
  rating: number
  onRatingChange: (rating: number) => void
  size?: "sm" | "md" | "lg"
  readonly?: boolean
  className?: string
}

export function StarRating({ rating, onRatingChange, size = "md", readonly = false, className }: RatingProps) {
  const [hoverRating, setHoverRating] = useState(0)

  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-5 w-5",
    lg: "h-6 w-6"
  }

  return (
    <div className={cn("flex items-center space-x-1", className)}>
      {[1, 2, 3, 4, 5].map((star) => (
        <button
          key={star}
          type="button"
          disabled={readonly}
          className={cn(
            "transition-all duration-200",
            !readonly && "hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 rounded"
          )}
          onMouseEnter={() => !readonly && setHoverRating(star)}
          onMouseLeave={() => !readonly && setHoverRating(0)}
          onClick={() => !readonly && onRatingChange(star)}
        >
          <Star
            className={cn(
              sizeClasses[size],
              "transition-colors duration-200",
              (hoverRating || rating) >= star
                ? "fill-yellow-400 text-yellow-400"
                : "text-slate-300 hover:text-yellow-300"
            )}
          />
        </button>
      ))}
      {rating > 0 && (
        <span className="ml-2 text-sm font-medium text-slate-600">
          {rating}.0
        </span>
      )}
    </div>
  )
}

interface QuickFeedbackProps {
  onFeedback: (type: 'positive' | 'negative', message?: string) => void
  className?: string
}

export function QuickFeedback({ onFeedback, className }: QuickFeedbackProps) {
  const [selectedType, setSelectedType] = useState<'positive' | 'negative' | null>(null)
  const [message, setMessage] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async () => {
    if (!selectedType) return
    
    setIsSubmitting(true)
    await onFeedback(selectedType, message)
    setIsSubmitting(false)
    setSelectedType(null)
    setMessage("")
  }

  return (
    <div className={cn("bg-white rounded-2xl p-6 border border-slate-200 shadow-sm", className)}>
      <h3 className="text-lg font-semibold text-slate-900 mb-4">How was your experience?</h3>
      
      {!selectedType ? (
        <div className="flex space-x-4">
          <button
            onClick={() => setSelectedType('positive')}
            className="flex-1 flex items-center justify-center space-x-2 p-4 border-2 border-slate-200 rounded-xl hover:border-green-300 hover:bg-green-50 transition-all duration-200 group"
          >
            <ThumbsUp className="h-5 w-5 text-slate-600 group-hover:text-green-600" />
            <span className="font-medium text-slate-700 group-hover:text-green-700">Great!</span>
          </button>
          
          <button
            onClick={() => setSelectedType('negative')}
            className="flex-1 flex items-center justify-center space-x-2 p-4 border-2 border-slate-200 rounded-xl hover:border-red-300 hover:bg-red-50 transition-all duration-200 group"
          >
            <ThumbsDown className="h-5 w-5 text-slate-600 group-hover:text-red-600" />
            <span className="font-medium text-slate-700 group-hover:text-red-700">Not so good</span>
          </button>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {selectedType === 'positive' ? (
                <>
                  <ThumbsUp className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-700">Thanks for the positive feedback!</span>
                </>
              ) : (
                <>
                  <ThumbsDown className="h-5 w-5 text-red-600" />
                  <span className="font-medium text-red-700">Help us improve</span>
                </>
              )}
            </div>
            <button
              onClick={() => setSelectedType(null)}
              className="p-1 hover:bg-slate-100 rounded-full transition-colors"
            >
              <X className="h-4 w-4 text-slate-500" />
            </button>
          </div>

          <Textarea
            placeholder={selectedType === 'positive' 
              ? "Tell us what you loved about your experience..." 
              : "Tell us what we can improve..."
            }
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            className="min-h-[100px] resize-none"
          />

          <div className="flex space-x-3">
            <Button
              variant="outline"
              onClick={() => setSelectedType(null)}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="flex-1 bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700"
            >
              {isSubmitting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
              ) : (
                <>
                  <Send className="h-4 w-4 mr-2" />
                  Send Feedback
                </>
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

interface DetailedReviewProps {
  onSubmit: (review: {
    rating: number
    title: string
    comment: string
    categories: Record<string, number>
  }) => void
  categories?: string[]
  className?: string
}

export function DetailedReview({ 
  onSubmit, 
  categories = ["Quality", "Communication", "Timeliness", "Value"], 
  className 
}: DetailedReviewProps) {
  const [rating, setRating] = useState(0)
  const [title, setTitle] = useState("")
  const [comment, setComment] = useState("")
  const [categoryRatings, setCategoryRatings] = useState<Record<string, number>>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleCategoryRating = (category: string, rating: number) => {
    setCategoryRatings(prev => ({ ...prev, [category]: rating }))
  }

  const handleSubmit = async () => {
    if (rating === 0) return
    
    setIsSubmitting(true)
    await onSubmit({
      rating,
      title,
      comment,
      categories: categoryRatings
    })
    setIsSubmitting(false)
    
    // Reset form
    setRating(0)
    setTitle("")
    setComment("")
    setCategoryRatings({})
  }

  const isValid = rating > 0 && title.trim() && comment.trim()

  return (
    <div className={cn("bg-white rounded-2xl p-6 border border-slate-200 shadow-sm", className)}>
      <h3 className="text-xl font-semibold text-slate-900 mb-6">Write a Review</h3>
      
      <div className="space-y-6">
        {/* Overall Rating */}
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Overall Rating
          </label>
          <StarRating rating={rating} onRatingChange={setRating} size="lg" />
        </div>

        {/* Category Ratings */}
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-3">
            Rate Specific Areas
          </label>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {categories.map((category) => (
              <div key={category} className="flex items-center justify-between">
                <span className="text-sm text-slate-600">{category}</span>
                <StarRating
                  rating={categoryRatings[category] || 0}
                  onRatingChange={(rating) => handleCategoryRating(category, rating)}
                  size="sm"
                />
              </div>
            ))}
          </div>
        </div>

        {/* Review Title */}
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Review Title
          </label>
          <input
            type="text"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Summarize your experience..."
            className="w-full px-4 py-3 border border-slate-200 rounded-xl focus:border-blue-500 focus:ring-4 focus:ring-blue-500/10 transition-all duration-200"
            maxLength={100}
          />
        </div>

        {/* Review Comment */}
        <div>
          <label className="block text-sm font-medium text-slate-700 mb-2">
            Your Review
          </label>
          <Textarea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Share details about your experience..."
            className="min-h-[120px] resize-none"
            maxLength={1000}
          />
          <div className="text-xs text-slate-400 mt-1">
            {comment.length}/1000 characters
          </div>
        </div>

        {/* Submit Button */}
        <Button
          onClick={handleSubmit}
          disabled={!isValid || isSubmitting}
          className="w-full bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700 disabled:opacity-50"
        >
          {isSubmitting ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
          ) : (
            "Submit Review"
          )}
        </Button>
      </div>
    </div>
  )
}
