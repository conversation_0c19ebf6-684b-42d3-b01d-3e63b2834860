"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { X, ArrowRight, ArrowLeft, Lightbulb, Target, Users, CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface OnboardingStep {
  id: string
  title: string
  description: string
  icon: React.ReactNode
  target?: string
  position?: "top" | "bottom" | "left" | "right"
}

interface OnboardingTourProps {
  steps: OnboardingStep[]
  isOpen: boolean
  onClose: () => void
  onComplete: () => void
}

export function OnboardingTour({ steps, isOpen, onClose, onComplete }: OnboardingTourProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true)
    }
  }, [isOpen])

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleComplete()
    }
  }

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleComplete = () => {
    setIsVisible(false)
    onComplete()
    setTimeout(onClose, 300)
  }

  const handleSkip = () => {
    setIsVisible(false)
    setTimeout(onClose, 300)
  }

  if (!isOpen) return null

  const currentStepData = steps[currentStep]

  return (
    <>
      {/* Overlay */}
      <div className={cn(
        "fixed inset-0 bg-black/50 backdrop-blur-sm z-50 transition-opacity duration-300",
        isVisible ? "opacity-100" : "opacity-0"
      )} />

      {/* Tour Modal */}
      <div className={cn(
        "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 transition-all duration-300",
        isVisible ? "scale-100 opacity-100" : "scale-95 opacity-0"
      )}>
        <div className="bg-white rounded-3xl shadow-2xl max-w-md w-full mx-4 overflow-hidden">
          {/* Header */}
          <div className="bg-gradient-to-r from-blue-600 to-emerald-600 p-6 text-white relative">
            <button
              onClick={handleSkip}
              className="absolute top-4 right-4 p-2 hover:bg-white/20 rounded-full transition-colors"
              aria-label="Close tour"
            >
              <X className="h-4 w-4" />
            </button>
            
            <div className="flex items-center space-x-3 mb-4">
              <div className="p-2 bg-white/20 rounded-full">
                {currentStepData.icon}
              </div>
              <div>
                <h3 className="text-xl font-bold">{currentStepData.title}</h3>
                <p className="text-blue-100 text-sm">Step {currentStep + 1} of {steps.length}</p>
              </div>
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-white/20 rounded-full h-2">
              <div 
                className="h-full bg-white rounded-full transition-all duration-500"
                style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
              />
            </div>
          </div>

          {/* Content */}
          <div className="p-6">
            <p className="text-slate-600 leading-relaxed mb-6">
              {currentStepData.description}
            </p>

            {/* Navigation */}
            <div className="flex items-center justify-between">
              <div className="flex space-x-2">
                {steps.map((_, index) => (
                  <div
                    key={index}
                    className={cn(
                      "w-2 h-2 rounded-full transition-colors duration-300",
                      index <= currentStep ? "bg-blue-600" : "bg-slate-200"
                    )}
                  />
                ))}
              </div>

              <div className="flex space-x-3">
                {currentStep > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePrevious}
                    className="flex items-center space-x-2"
                  >
                    <ArrowLeft className="h-4 w-4" />
                    <span>Previous</span>
                  </Button>
                )}

                <Button
                  size="sm"
                  onClick={handleNext}
                  className="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-emerald-600 hover:from-blue-700 hover:to-emerald-700"
                >
                  <span>{currentStep === steps.length - 1 ? "Get Started" : "Next"}</span>
                  {currentStep === steps.length - 1 ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <ArrowRight className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

// Predefined onboarding flows
export const customerOnboardingSteps: OnboardingStep[] = [
  {
    id: "welcome",
    title: "Welcome to RenovHub!",
    description: "We're excited to help you transform your home with trusted professionals. Let's take a quick tour to get you started.",
    icon: <Lightbulb className="h-5 w-5" />
  },
  {
    id: "create-project",
    title: "Create Your First Project",
    description: "Start by describing your renovation project. Our AI will help categorize it and match you with the right contractors.",
    icon: <Target className="h-5 w-5" />
  },
  {
    id: "find-contractors",
    title: "Find Verified Contractors",
    description: "Browse through our network of verified professionals, read reviews, and compare quotes to find the perfect match.",
    icon: <Users className="h-5 w-5" />
  },
  {
    id: "manage-projects",
    title: "Manage Your Projects",
    description: "Track progress, communicate with contractors, and manage payments all in one place through your dashboard.",
    icon: <CheckCircle className="h-5 w-5" />
  }
]

export const contractorOnboardingSteps: OnboardingStep[] = [
  {
    id: "welcome-pro",
    title: "Welcome to RenovHub Pro!",
    description: "Join thousands of contractors growing their business with RenovHub. Let's set up your professional profile.",
    icon: <Lightbulb className="h-5 w-5" />
  },
  {
    id: "browse-projects",
    title: "Browse Available Projects",
    description: "Discover renovation projects in your area that match your skills and expertise. Submit competitive bids to win work.",
    icon: <Target className="h-5 w-5" />
  },
  {
    id: "build-reputation",
    title: "Build Your Reputation",
    description: "Complete projects successfully, earn positive reviews, and build a strong reputation to attract more clients.",
    icon: <Users className="h-5 w-5" />
  },
  {
    id: "grow-business",
    title: "Grow Your Business",
    description: "Use our tools to manage projects, communicate with clients, and grow your renovation business efficiently.",
    icon: <CheckCircle className="h-5 w-5" />
  }
]

// Hook for managing onboarding state
export function useOnboarding() {
  const [hasSeenOnboarding, setHasSeenOnboarding] = useState(true) // Default to true to avoid showing on every load
  const [isOnboardingOpen, setIsOnboardingOpen] = useState(false)
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)

    // Check if user has seen onboarding
    if (typeof window !== 'undefined') {
      const seen = localStorage.getItem('renovhub-onboarding-seen')
      if (!seen) {
        setHasSeenOnboarding(false)
        setIsOnboardingOpen(true)
      }
    }
  }, [])

  const completeOnboarding = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('renovhub-onboarding-seen', 'true')
    }
    setHasSeenOnboarding(true)
    setIsOnboardingOpen(false)
  }

  const startOnboarding = () => {
    setIsOnboardingOpen(true)
  }

  return {
    hasSeenOnboarding,
    isOnboardingOpen,
    setIsOnboardingOpen,
    completeOnboarding,
    startOnboarding
  }
}
