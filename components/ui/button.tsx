import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap font-semibold transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-gradient-to-r from-philippine-green to-philippine-green/90 text-white hover:from-philippine-green/90 hover:to-philippine-green focus-visible:ring-philippine-green shadow-lg hover:shadow-xl hover:scale-105 active:scale-95",
        destructive: "bg-gradient-to-r from-red-600 to-red-700 text-white hover:from-red-700 hover:to-red-800 focus-visible:ring-red-500 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95",
        outline: "border-2 border-slate-200 bg-white/80 backdrop-blur-sm text-slate-700 hover:border-slate-300 hover:bg-white hover:shadow-lg focus-visible:ring-slate-500 shadow-md hover:scale-105 active:scale-95",
        secondary: "bg-gradient-to-r from-cerulean-blue to-cerulean-blue/90 text-white hover:from-cerulean-blue/90 hover:to-cerulean-blue focus-visible:ring-cerulean-blue shadow-lg hover:shadow-xl hover:scale-105 active:scale-95",
        ghost: "text-slate-600 hover:text-slate-900 hover:bg-slate-100/80 focus-visible:ring-slate-500 hover:scale-105 active:scale-95",
        link: "text-philippine-green underline-offset-4 hover:underline hover:text-philippine-green/80 focus-visible:ring-philippine-green",
        premium: "bg-gradient-to-r from-philippine-green via-emerald-500 to-philippine-green text-white hover:shadow-2xl focus-visible:ring-philippine-green shadow-lg hover:scale-105 active:scale-95",
        gradient: "bg-gradient-to-r from-philippine-green via-cerulean-blue to-emerald-600 text-white hover:shadow-2xl focus-visible:ring-philippine-green shadow-lg hover:scale-105 active:scale-95",
      },
      size: {
        default: "h-12 px-8 py-3 text-base rounded-xl min-h-[48px]",
        sm: "h-10 px-6 py-2 text-sm rounded-lg min-h-[40px]",
        lg: "h-14 px-10 py-4 text-lg rounded-xl min-h-[56px]",
        xl: "h-16 px-12 py-5 text-xl rounded-2xl min-h-[64px]",
        icon: "h-12 w-12 rounded-xl min-h-[48px] min-w-[48px]",
        action: "h-10 px-4 py-2 text-sm rounded-lg min-h-[40px] min-w-[80px]",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
