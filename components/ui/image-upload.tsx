"use client"

import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Camera, Upload, X, Image as ImageIcon, Plus } from "lucide-react"
import { cn } from "@/lib/utils"

interface ImageUploadProps {
  images: File[]
  onImagesChange: (images: File[]) => void
  maxImages?: number
  maxSizePerImage?: number // in MB
  acceptedTypes?: string[]
  className?: string
}

export function ImageUpload({
  images,
  onImagesChange,
  maxImages = 10,
  maxSizePerImage = 5,
  acceptedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  className
}: ImageUploadProps) {
  const [dragActive, setDragActive] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} is not supported. Please use JPEG, PNG, or WebP.`
    }
    
    if (file.size > maxSizePerImage * 1024 * 1024) {
      return `File size must be less than ${maxSizePerImage}MB.`
    }
    
    return null
  }

  const handleFiles = useCallback((fileList: FileList | null) => {
    if (!fileList) return

    const newFiles = Array.from(fileList)
    const validFiles: File[] = []
    let errorMessage = ""

    // Check total number of images
    if (images.length + newFiles.length > maxImages) {
      errorMessage = `Maximum ${maxImages} images allowed.`
      setError(errorMessage)
      return
    }

    // Validate each file
    for (const file of newFiles) {
      const validationError = validateFile(file)
      if (validationError) {
        errorMessage = validationError
        break
      }
      validFiles.push(file)
    }

    if (errorMessage) {
      setError(errorMessage)
      return
    }

    setError(null)
    onImagesChange([...images, ...validFiles])
  }, [images, maxImages, maxSizePerImage, acceptedTypes, onImagesChange])

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true)
    } else if (e.type === "dragleave") {
      setDragActive(false)
    }
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    handleFiles(e.dataTransfer.files)
  }, [handleFiles])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFiles(e.target.files)
    // Reset input value to allow re-uploading the same file
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index)
    onImagesChange(newImages)
    setError(null)
  }

  const openFileDialog = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Upload Area */}
      <div
        className={cn(
          "relative border-2 border-dashed rounded-2xl p-8 text-center transition-all duration-200 cursor-pointer",
          dragActive
            ? "border-blue-500 bg-blue-50"
            : "border-slate-300 hover:border-slate-400 hover:bg-slate-50",
          images.length >= maxImages && "opacity-50 cursor-not-allowed"
        )}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={images.length < maxImages ? openFileDialog : undefined}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleInputChange}
          className="hidden"
          disabled={images.length >= maxImages}
        />

        <div className="space-y-4">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-emerald-500 rounded-full flex items-center justify-center">
              <Upload className="h-8 w-8 text-white" />
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-slate-900 mb-2">
              {images.length >= maxImages ? "Maximum images reached" : "Upload Project Images"}
            </h3>
            <p className="text-slate-600 mb-4">
              {images.length >= maxImages 
                ? `You've uploaded the maximum of ${maxImages} images.`
                : `Drag and drop images here, or click to browse. Maximum ${maxImages} images.`
              }
            </p>
            
            {images.length < maxImages && (
              <div className="flex flex-wrap justify-center gap-2 text-sm text-slate-500">
                <span>Supported: JPEG, PNG, WebP</span>
                <span>•</span>
                <span>Max {maxSizePerImage}MB per image</span>
              </div>
            )}
          </div>

          {images.length < maxImages && (
            <Button
              type="button"
              variant="outline"
              className="bg-white hover:bg-slate-50"
              onClick={(e) => {
                e.stopPropagation()
                openFileDialog()
              }}
            >
              <Camera className="h-4 w-4 mr-2" />
              Choose Images
            </Button>
          )}
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-xl">
          <p className="text-red-600 text-sm">{error}</p>
        </div>
      )}

      {/* Image Previews */}
      {images.length > 0 && (
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h4 className="text-lg font-semibold text-slate-900">
              Uploaded Images ({images.length}/{maxImages})
            </h4>
            {images.length < maxImages && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={openFileDialog}
                className="flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add More</span>
              </Button>
            )}
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
            {images.map((image, index) => (
              <ImageThumbnail
                key={`${image.name}-${index}`}
                file={image}
                onRemove={() => removeImage(index)}
                index={index}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

interface ImageThumbnailProps {
  file: File
  onRemove: () => void
  index: number
}

function ImageThumbnail({ file, onRemove, index }: ImageThumbnailProps) {
  const [preview, setPreview] = useState<string | null>(null)
  const [loading, setLoading] = useState(true)

  useState(() => {
    const reader = new FileReader()
    reader.onload = (e) => {
      setPreview(e.target?.result as string)
      setLoading(false)
    }
    reader.readAsDataURL(file)
  })

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  return (
    <div className="relative group bg-white rounded-xl border border-slate-200 overflow-hidden shadow-sm hover:shadow-md transition-all duration-200">
      <div className="aspect-square relative">
        {loading ? (
          <div className="w-full h-full bg-slate-100 flex items-center justify-center">
            <ImageIcon className="h-8 w-8 text-slate-400 animate-pulse" />
          </div>
        ) : preview ? (
          <img
            src={preview}
            alt={`Preview ${index + 1}`}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full bg-slate-100 flex items-center justify-center">
            <ImageIcon className="h-8 w-8 text-slate-400" />
          </div>
        )}

        {/* Remove Button */}
        <button
          onClick={onRemove}
          className="absolute top-2 right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg"
          title="Remove image"
        >
          <X className="h-3 w-3" />
        </button>

        {/* Image Index */}
        <div className="absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded-md">
          {index + 1}
        </div>
      </div>

      {/* File Info */}
      <div className="p-3">
        <p className="text-xs font-medium text-slate-900 truncate" title={file.name}>
          {file.name}
        </p>
        <p className="text-xs text-slate-500">
          {formatFileSize(file.size)}
        </p>
      </div>
    </div>
  )
}
