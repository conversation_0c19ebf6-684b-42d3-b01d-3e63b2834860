"use client"

import { useState, useRef, useEffect } from 'react'
import { cn } from '@/lib/utils'

interface ProgressiveImageProps {
  src: string
  alt: string
  placeholder?: string
  className?: string
  width?: number
  height?: number
  priority?: boolean
  onLoad?: () => void
  onError?: () => void
}

export function ProgressiveImage({
  src,
  alt,
  placeholder,
  className,
  width,
  height,
  priority = false,
  onLoad,
  onError
}: ProgressiveImageProps) {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isError, setIsError] = useState(false)
  const [isInView, setIsInView] = useState(priority)
  const imgRef = useRef<HTMLImageElement>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Intersection Observer for lazy loading
  useEffect(() => {
    if (priority) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true)
          observer.disconnect()
        }
      },
      { threshold: 0.1 }
    )

    if (containerRef.current) {
      observer.observe(containerRef.current)
    }

    return () => observer.disconnect()
  }, [priority])

  // Handle image loading
  useEffect(() => {
    if (!isInView) return

    const img = new Image()
    img.src = src
    
    img.onload = () => {
      setIsLoaded(true)
      onLoad?.()
    }
    
    img.onerror = () => {
      setIsError(true)
      onError?.()
    }
  }, [src, isInView, onLoad, onError])

  const generatePlaceholder = (w: number, h: number) => {
    return `data:image/svg+xml;base64,${btoa(`
      <svg width="${w}" height="${h}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="#f1f5f9"/>
        <text x="50%" y="50%" text-anchor="middle" dy=".3em" fill="#94a3b8" font-family="Arial, sans-serif" font-size="14">
          Loading...
        </text>
      </svg>
    `)}`
  }

  const defaultPlaceholder = width && height 
    ? generatePlaceholder(width, height)
    : generatePlaceholder(400, 300)

  return (
    <div 
      ref={containerRef}
      className={cn("relative overflow-hidden", className)}
      style={{ width, height }}
    >
      {/* Placeholder */}
      {!isLoaded && !isError && (
        <img
          src={placeholder || defaultPlaceholder}
          alt=""
          className={cn(
            "absolute inset-0 w-full h-full object-cover transition-opacity duration-300",
            isInView ? "opacity-100" : "opacity-0"
          )}
        />
      )}

      {/* Main image */}
      {isInView && !isError && (
        <img
          ref={imgRef}
          src={src}
          alt={alt}
          className={cn(
            "absolute inset-0 w-full h-full object-cover transition-opacity duration-300",
            isLoaded ? "opacity-100" : "opacity-0"
          )}
          width={width}
          height={height}
        />
      )}

      {/* Error state */}
      {isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-slate-100 text-slate-500">
          <div className="text-center">
            <div className="text-2xl mb-2">📷</div>
            <div className="text-sm">Failed to load image</div>
          </div>
        </div>
      )}

      {/* Loading indicator */}
      {isInView && !isLoaded && !isError && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
        </div>
      )}
    </div>
  )
}

// Optimized image component with WebP support
export function OptimizedImage({
  src,
  alt,
  className,
  width,
  height,
  priority = false,
  quality = 75,
  ...props
}: ProgressiveImageProps & { quality?: number }) {
  const [imageSrc, setImageSrc] = useState<string>('')

  useEffect(() => {
    // Check WebP support
    const checkWebPSupport = () => {
      return new Promise<boolean>((resolve) => {
        const webP = new Image()
        webP.onload = webP.onerror = () => {
          resolve(webP.height === 2)
        }
        webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA'
      })
    }

    checkWebPSupport().then((supportsWebP) => {
      // In a real app, you'd have different image formats available
      // For now, we'll just use the original src
      setImageSrc(src)
    })
  }, [src])

  if (!imageSrc) {
    return (
      <div className={cn("bg-slate-200 animate-pulse", className)} style={{ width, height }} />
    )
  }

  return (
    <ProgressiveImage
      src={imageSrc}
      alt={alt}
      className={className}
      width={width}
      height={height}
      priority={priority}
      {...props}
    />
  )
}
