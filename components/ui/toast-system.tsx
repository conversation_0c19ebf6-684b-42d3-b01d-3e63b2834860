"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, use<PERSON><PERSON>back, ReactNode } from "react"
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from "lucide-react"
import { cn } from "@/lib/utils"

interface Toast {
  id: string
  type: "success" | "error" | "warning" | "info"
  title: string
  description?: string
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

interface ToastContextType {
  toasts: Toast[]
  addToast: (toast: Omit<Toast, "id">) => void
  removeToast: (id: string) => void
  success: (title: string, description?: string) => void
  error: (title: string, description?: string) => void
  warning: (title: string, description?: string) => void
  info: (title: string, description?: string) => void
}

const ToastContext = createContext<ToastContextType | undefined>(undefined)

export function ToastProvider({ children }: { children: ReactNode }) {
  const [toasts, setToasts] = useState<Toast[]>([])

  const addToast = useCallback((toast: Omit<Toast, "id">) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast = { ...toast, id }
    
    setToasts(prev => [...prev, newToast])

    // Auto remove after duration
    const duration = toast.duration || 5000
    setTimeout(() => {
      removeToast(id)
    }, duration)
  }, [])

  const removeToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }, [])

  const success = useCallback((title: string, description?: string) => {
    addToast({ type: "success", title, description })
  }, [addToast])

  const error = useCallback((title: string, description?: string) => {
    addToast({ type: "error", title, description })
  }, [addToast])

  const warning = useCallback((title: string, description?: string) => {
    addToast({ type: "warning", title, description })
  }, [addToast])

  const info = useCallback((title: string, description?: string) => {
    addToast({ type: "info", title, description })
  }, [addToast])

  return (
    <ToastContext.Provider value={{
      toasts,
      addToast,
      removeToast,
      success,
      error,
      warning,
      info
    }}>
      {children}
      <ToastContainer />
    </ToastContext.Provider>
  )
}

export function useToast() {
  const context = useContext(ToastContext)
  if (!context) {
    throw new Error("useToast must be used within a ToastProvider")
  }
  return context
}

function ToastContainer() {
  const { toasts } = useToast()

  return (
    <div className="fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full">
      {toasts.map(toast => (
        <ToastItem key={toast.id} toast={toast} />
      ))}
    </div>
  )
}

function ToastItem({ toast }: { toast: Toast }) {
  const { removeToast } = useToast()

  const icons = {
    success: CheckCircle,
    error: AlertCircle,
    warning: AlertTriangle,
    info: Info
  }

  const colors = {
    success: "bg-green-50 border-green-200 text-green-800",
    error: "bg-red-50 border-red-200 text-red-800",
    warning: "bg-yellow-50 border-yellow-200 text-yellow-800",
    info: "bg-blue-50 border-blue-200 text-blue-800"
  }

  const iconColors = {
    success: "text-green-500",
    error: "text-red-500",
    warning: "text-yellow-500",
    info: "text-blue-500"
  }

  const Icon = icons[toast.type]

  return (
    <div
      className={cn(
        "relative p-4 rounded-lg border shadow-lg backdrop-blur-sm animate-in slide-in-from-right-full duration-300",
        colors[toast.type]
      )}
    >
      <div className="flex items-start space-x-3">
        <Icon className={cn("h-5 w-5 mt-0.5 flex-shrink-0", iconColors[toast.type])} />
        
        <div className="flex-1 min-w-0">
          <h4 className="font-medium text-sm">{toast.title}</h4>
          {toast.description && (
            <p className="text-sm opacity-90 mt-1">{toast.description}</p>
          )}
          
          {toast.action && (
            <button
              onClick={toast.action.onClick}
              className="text-sm font-medium underline mt-2 hover:no-underline"
            >
              {toast.action.label}
            </button>
          )}
        </div>

        <button
          onClick={() => removeToast(toast.id)}
          className="flex-shrink-0 p-1 rounded-md hover:bg-black/10 transition-colors"
        >
          <X className="h-4 w-4" />
        </button>
      </div>

      {/* Progress bar */}
      <div
        className="absolute bottom-0 left-0 h-1 bg-current opacity-30 rounded-b-lg animate-pulse"
        style={{
          animation: `shrink ${(toast.duration || 5000) / 1000}s linear forwards`
        }}
      />
    </div>
  )
}

// Hook for common toast patterns
export function useToastActions() {
  const { success, error, warning, info } = useToast()

  const showSuccess = useCallback((message: string) => {
    success("Success", message)
  }, [success])

  const showError = useCallback((message: string) => {
    error("Error", message)
  }, [error])

  const showSaved = useCallback(() => {
    success("Saved", "Your changes have been saved successfully")
  }, [success])

  const showDeleted = useCallback(() => {
    success("Deleted", "Item has been deleted successfully")
  }, [success])

  const showCopied = useCallback(() => {
    success("Copied", "Copied to clipboard")
  }, [success])

  const showNetworkError = useCallback(() => {
    error("Network Error", "Please check your connection and try again")
  }, [error])

  const showValidationError = useCallback((field: string) => {
    error("Validation Error", `Please check the ${field} field`)
  }, [error])

  const showComingSoon = useCallback(() => {
    info("Coming Soon", "This feature will be available soon")
  }, [info])

  return {
    showSuccess,
    showError,
    showSaved,
    showDeleted,
    showCopied,
    showNetworkError,
    showValidationError,
    showComingSoon
  }
}

// Toast trigger component for testing
export function ToastDemo() {
  const { success, error, warning, info } = useToast()

  return (
    <div className="space-x-2">
      <button
        onClick={() => success("Success!", "This is a success message")}
        className="px-3 py-1 bg-green-500 text-white rounded text-sm"
      >
        Success
      </button>
      <button
        onClick={() => error("Error!", "This is an error message")}
        className="px-3 py-1 bg-red-500 text-white rounded text-sm"
      >
        Error
      </button>
      <button
        onClick={() => warning("Warning!", "This is a warning message")}
        className="px-3 py-1 bg-yellow-500 text-white rounded text-sm"
      >
        Warning
      </button>
      <button
        onClick={() => info("Info!", "This is an info message")}
        className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
      >
        Info
      </button>
    </div>
  )
}
