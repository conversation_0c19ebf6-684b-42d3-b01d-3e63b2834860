"use client"

import { CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface VerifiedBadgeProps {
  isVerified: boolean
  size?: "sm" | "md" | "lg"
  className?: string
  showTooltip?: boolean
}

export function VerifiedBadge({ 
  isVerified, 
  size = "md", 
  className,
  showTooltip = true 
}: VerifiedBadgeProps) {
  if (!isVerified) return null

  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-5 h-5", 
    lg: "w-6 h-6"
  }

  return (
    <div className={cn("relative group", className)}>
      <div className="absolute -bottom-1 -right-1 z-10">
        <div className="relative">
          <CheckCircle
            className={cn(
              "text-philippine-green bg-white rounded-full shadow-lg border-2 border-white transition-all duration-300 group-hover:scale-110 group-hover:shadow-xl",
              sizeClasses[size]
            )}
          />
        </div>
      </div>

      {showTooltip && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-slate-900 text-white text-xs px-2 py-1 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20">
          Verified
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-slate-900"></div>
        </div>
      )}
    </div>
  )
}

// Online/Offline Status Badge
export function OnlineStatusBadge({
  isOnline = false,
  size = "sm",
  className = "",
  showTooltip = true
}: {
  isOnline?: boolean
  size?: "sm" | "md" | "lg"
  className?: string
  showTooltip?: boolean
}) {
  const sizeClasses = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5"
  }

  return (
    <div className={cn("relative group", className)}>
      <div className="absolute -bottom-0.5 -right-0.5 z-10">
        <div className={cn(
          "rounded-full border-2 border-white shadow-lg",
          sizeClasses[size],
          isOnline ? "bg-green-500" : "bg-gray-400"
        )} />
      </div>

      {showTooltip && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-slate-900 text-white text-xs px-2 py-1 rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-20">
          {isOnline ? "Online" : "Offline"}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-2 border-r-2 border-t-2 border-transparent border-t-slate-900"></div>
        </div>
      )}
    </div>
  )
}

interface ProfileIconWithBadgeProps {
  user: {
    name: string
    role: string
    isVerified?: boolean
    isOnline?: boolean
  }
  size?: "sm" | "md" | "lg"
  className?: string
  showName?: boolean
}

export function ProfileIconWithBadge({ 
  user, 
  size = "md", 
  className,
  showName = true 
}: ProfileIconWithBadgeProps) {
  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-9 h-9",
    lg: "w-12 h-12"
  }

  const iconSizeClasses = {
    sm: "h-3 w-3",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  }

  return (
    <div className={cn("flex items-center space-x-3", className)}>
      <div className="relative">
        <div className={cn(
          "bg-gradient-to-br from-slate-600 to-slate-700 rounded-full flex items-center justify-center shadow-lg transition-all duration-200",
          sizeClasses[size]
        )}>
          <svg 
            className={cn("text-white", iconSizeClasses[size])} 
            fill="currentColor" 
            viewBox="0 0 20 20"
          >
            <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
          </svg>
        </div>
        {user.isVerified ? (
          <VerifiedBadge
            isVerified={true}
            size={size === "lg" ? "md" : "sm"}
          />
        ) : (
          <OnlineStatusBadge
            isOnline={user.isOnline || false}
            size={size === "lg" ? "md" : "sm"}
          />
        )}
      </div>
      
      {showName && (
        <div className="hidden lg:block">
          <div className="text-sm font-semibold text-slate-900 flex items-center space-x-1">
            <span>{user.name}</span>
          </div>
          <div className="text-xs text-slate-500 capitalize">{user.role}</div>
        </div>
      )}
    </div>
  )
}
