"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"

// Fade in animation using CSS
export function FadeIn({
  children,
  delay = 0,
  duration = 0.5,
  className = ""
}: {
  children: ReactNode
  delay?: number
  duration?: number
  className?: string
}) {
  return (
    <div
      className={cn("animate-in fade-in slide-in-from-bottom-4", className)}
      style={{
        animationDelay: `${delay}s`,
        animationDuration: `${duration}s`
      }}
    >
      {children}
    </div>
  )
}

// Slide in from direction using CSS
export function SlideIn({
  children,
  direction = "left",
  delay = 0,
  duration = 0.5,
  className = ""
}: {
  children: ReactNode
  direction?: "left" | "right" | "up" | "down"
  delay?: number
  duration?: number
  className?: string
}) {
  const directionClasses = {
    left: "animate-in slide-in-from-left-4",
    right: "animate-in slide-in-from-right-4",
    up: "animate-in slide-in-from-top-4",
    down: "animate-in slide-in-from-bottom-4"
  }

  return (
    <div
      className={cn(directionClasses[direction], className)}
      style={{
        animationDelay: `${delay}s`,
        animationDuration: `${duration}s`
      }}
    >
      {children}
    </div>
  )
}

// Scale animation using CSS
export function ScaleIn({
  children,
  delay = 0,
  duration = 0.3,
  className = ""
}: {
  children: ReactNode
  delay?: number
  duration?: number
  className?: string
}) {
  return (
    <div
      className={cn("animate-in zoom-in-95", className)}
      style={{
        animationDelay: `${delay}s`,
        animationDuration: `${duration}s`
      }}
    >
      {children}
    </div>
  )
}

// Stagger children animation using CSS
export function StaggerChildren({
  children,
  staggerDelay = 0.1,
  className = ""
}: {
  children: ReactNode
  staggerDelay?: number
  className?: string
}) {
  return (
    <div className={cn("space-y-2", className)}>
      {children}
    </div>
  )
}

// Individual stagger item
export function StaggerItem({
  children,
  className = "",
  index = 0
}: {
  children: ReactNode
  className?: string
  index?: number
}) {
  return (
    <div
      className={cn("animate-in fade-in slide-in-from-bottom-4", className)}
      style={{
        animationDelay: `${index * 0.1}s`
      }}
    >
      {children}
    </div>
  )
}

// Hover scale effect using CSS
export function HoverScale({
  children,
  scale = 1.05,
  className = ""
}: {
  children: ReactNode
  scale?: number
  className?: string
}) {
  return (
    <div
      className={cn("transition-transform duration-200 hover:scale-105 active:scale-95", className)}
    >
      {children}
    </div>
  )
}

// Page transition wrapper using CSS
export function PageTransition({
  children,
  className = ""
}: {
  children: ReactNode
  className?: string
}) {
  return (
    <div
      className={cn("animate-in fade-in slide-in-from-bottom-4 duration-300", className)}
    >
      {children}
    </div>
  )
}

// Loading spinner with animation
export function LoadingSpinner({
  size = "md",
  color = "brand-primary"
}: {
  size?: "sm" | "md" | "lg"
  color?: string
}) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-6 h-6",
    lg: "w-8 h-8"
  }

  return (
    <div
      className={cn(
        sizeClasses[size],
        "border-2 border-slate-200 border-t-brand-primary rounded-full animate-spin"
      )}
    />
  )
}

// Pulse animation for loading states
export function PulseAnimation({
  children,
  className = ""
}: {
  children: ReactNode
  className?: string
}) {
  return (
    <div
      className={cn("animate-pulse", className)}
    >
      {children}
    </div>
  )
}

// Bounce animation for notifications
export function BounceIn({
  children,
  className = ""
}: {
  children: ReactNode
  className?: string
}) {
  return (
    <div
      className={cn("animate-bounce", className)}
    >
      {children}
    </div>
  )
}
