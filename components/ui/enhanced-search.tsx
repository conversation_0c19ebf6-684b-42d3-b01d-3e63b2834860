"use client"

import { useState, useRef, useEffect } from "react"
import { Search, X, Filter, SlidersHorizontal, MapPin, Star, DollarSign } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface SearchResult {
  id: string
  title: string
  description: string
  category?: string
  location?: string
  rating?: number
  price?: string
}

interface SearchProps {
  placeholder?: string
  onSearch?: (query: string) => void
  onResultSelect?: (result: SearchResult) => void
  results?: SearchResult[]
  isLoading?: boolean
  className?: string
}

export function EnhancedSearch({
  placeholder = "Search...",
  onSearch,
  onResultSelect,
  results = [],
  isLoading = false,
  className = ""
}: SearchProps) {
  const [query, setQuery] = useState("")
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)
  const inputRef = useRef<HTMLInputElement>(null)
  const resultsRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (query.trim()) {
      onSearch?.(query)
      setIsOpen(true)
    } else {
      setIsOpen(false)
    }
    setSelectedIndex(-1)
  }, [query, onSearch])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen || results.length === 0) return

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault()
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        )
        break
      case "ArrowUp":
        e.preventDefault()
        setSelectedIndex(prev => prev > 0 ? prev - 1 : prev)
        break
      case "Enter":
        e.preventDefault()
        if (selectedIndex >= 0) {
          onResultSelect?.(results[selectedIndex])
          setQuery("")
          setIsOpen(false)
        }
        break
      case "Escape":
        setIsOpen(false)
        inputRef.current?.blur()
        break
    }
  }

  const handleResultClick = (result: SearchResult) => {
    onResultSelect?.(result)
    setQuery("")
    setIsOpen(false)
  }

  const clearSearch = () => {
    setQuery("")
    setIsOpen(false)
    inputRef.current?.focus()
  }

  return (
    <div className={cn("relative w-full max-w-md", className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
        <Input
          ref={inputRef}
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => query.trim() && setIsOpen(true)}
          placeholder={placeholder}
          className="pl-10 pr-10"
        />
        {query && (
          <button
            onClick={clearSearch}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-400 hover:text-slate-600"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {isOpen && (
        <div
          ref={resultsRef}
          className="absolute top-full left-0 right-0 mt-1 bg-white border border-slate-200 rounded-lg shadow-lg z-50 max-h-80 overflow-y-auto animate-in fade-in slide-in-from-top-2 duration-200"
        >
            {isLoading ? (
              <div className="p-4 text-center text-slate-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-brand-primary mx-auto"></div>
                <p className="mt-2 text-sm">Searching...</p>
              </div>
            ) : results.length > 0 ? (
              <div className="py-2">
                {results.map((result, index) => (
                  <button
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className={cn(
                      "w-full px-4 py-3 text-left hover:bg-slate-50 transition-colors",
                      selectedIndex === index && "bg-slate-50"
                    )}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-slate-900 truncate">
                          {result.title}
                        </h4>
                        <p className="text-sm text-slate-600 truncate mt-1">
                          {result.description}
                        </p>
                        <div className="flex items-center space-x-4 mt-2">
                          {result.category && (
                            <Badge variant="secondary" className="text-xs">
                              {result.category}
                            </Badge>
                          )}
                          {result.location && (
                            <div className="flex items-center space-x-1 text-xs text-slate-500">
                              <MapPin className="h-3 w-3" />
                              <span>{result.location}</span>
                            </div>
                          )}
                          {result.rating && (
                            <div className="flex items-center space-x-1 text-xs text-slate-500">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span>{result.rating}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      {result.price && (
                        <div className="text-sm font-medium text-brand-primary">
                          {result.price}
                        </div>
                      )}
                    </div>
                  </button>
                ))}
              </div>
            ) : query.trim() ? (
              <div className="p-4 text-center text-slate-500">
                <p className="text-sm">No results found for "{query}"</p>
              </div>
            ) : null}
        </div>
      )}
    </div>
  )
}

// Advanced Filter Component
interface FilterOption {
  id: string
  label: string
  value: string
  count?: number
}

interface FilterGroup {
  id: string
  label: string
  type: "checkbox" | "radio" | "range" | "select"
  options?: FilterOption[]
  min?: number
  max?: number
  value?: any
}

interface AdvancedFilterProps {
  filters: FilterGroup[]
  onFiltersChange?: (filters: Record<string, any>) => void
  className?: string
}

export function AdvancedFilter({
  filters,
  onFiltersChange,
  className = ""
}: AdvancedFilterProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [activeFilters, setActiveFilters] = useState<Record<string, any>>({})
  const [appliedFilters, setAppliedFilters] = useState<Record<string, any>>({})

  const handleFilterChange = (filterId: string, value: any) => {
    setActiveFilters(prev => ({
      ...prev,
      [filterId]: value
    }))
  }

  const applyFilters = () => {
    setAppliedFilters(activeFilters)
    onFiltersChange?.(activeFilters)
    setIsOpen(false)
  }

  const clearFilters = () => {
    setActiveFilters({})
    setAppliedFilters({})
    onFiltersChange?.({})
  }

  const activeFilterCount = Object.keys(appliedFilters).filter(
    key => appliedFilters[key] !== undefined && appliedFilters[key] !== ""
  ).length

  return (
    <div className={cn("relative", className)}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="relative"
      >
        <SlidersHorizontal className="h-4 w-4 mr-2" />
        Filters
        {activeFilterCount > 0 && (
          <Badge className="ml-2 bg-brand-primary text-white">
            {activeFilterCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <div
          className="absolute top-full left-0 mt-2 w-80 bg-white border border-slate-200 rounded-lg shadow-lg z-50 p-4 animate-in fade-in slide-in-from-top-2 duration-200"
        >
            <div className="space-y-4">
              {filters.map(filter => (
                <div key={filter.id}>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    {filter.label}
                  </label>
                  
                  {filter.type === "checkbox" && filter.options && (
                    <div className="space-y-2">
                      {filter.options.map(option => (
                        <label key={option.id} className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            checked={activeFilters[filter.id]?.includes(option.value) || false}
                            onChange={(e) => {
                              const current = activeFilters[filter.id] || []
                              const newValue = e.target.checked
                                ? [...current, option.value]
                                : current.filter((v: string) => v !== option.value)
                              handleFilterChange(filter.id, newValue)
                            }}
                            className="rounded border-slate-300 text-brand-primary focus:ring-brand-primary"
                          />
                          <span className="text-sm text-slate-600">
                            {option.label}
                            {option.count && (
                              <span className="text-slate-400 ml-1">({option.count})</span>
                            )}
                          </span>
                        </label>
                      ))}
                    </div>
                  )}

                  {filter.type === "range" && (
                    <div className="space-y-2">
                      <input
                        type="range"
                        min={filter.min}
                        max={filter.max}
                        value={activeFilters[filter.id] || filter.min}
                        onChange={(e) => handleFilterChange(filter.id, parseInt(e.target.value))}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-slate-500">
                        <span>${filter.min}</span>
                        <span className="font-medium">
                          ${activeFilters[filter.id] || filter.min}
                        </span>
                        <span>${filter.max}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <div className="flex items-center justify-between mt-6 pt-4 border-t border-slate-200">
              <Button variant="ghost" onClick={clearFilters} size="sm">
                Clear All
              </Button>
              <Button onClick={applyFilters} size="sm">
                Apply Filters
              </Button>
            </div>
        </div>
      )}
    </div>
  )
}
