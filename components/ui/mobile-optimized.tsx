"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

// Mobile-Optimized Container
interface MobileContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: "default" | "tight" | "full"
}

export const MobileContainer = React.forwardRef<HTMLDivElement, MobileContainerProps>(
  ({ className, variant = "default", children, ...props }, ref) => {
    const containerClasses = {
      default: "container-native",
      tight: "container-native-tight", 
      full: "container-native-full"
    }

    return (
      <div
        ref={ref}
        className={cn(containerClasses[variant], className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileContainer.displayName = "MobileContainer"

// Mobile-Optimized Section
interface MobileSectionProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  spacing?: "tight" | "default" | "spacious"
}

export const MobileSection = React.forwardRef<HTMLDivElement, MobileSectionProps>(
  ({ className, spacing = "default", children, ...props }, ref) => {
    const spacingClasses = {
      tight: "section-native-tight",
      default: "section-native",
      spacious: "section-native-spacious"
    }

    return (
      <div
        ref={ref}
        className={cn(spacingClasses[spacing], className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileSection.displayName = "MobileSection"

// Mobile-Optimized Button
interface MobileButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: "primary" | "secondary" | "ghost" | "outline"
  size?: "sm" | "md" | "lg"
  fullWidth?: boolean
}

export const MobileButton = React.forwardRef<HTMLButtonElement, MobileButtonProps>(
  ({ className, variant = "primary", size = "md", fullWidth = false, children, ...props }, ref) => {
    const baseClasses = "inline-flex items-center justify-center font-medium transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none mobile-tap-highlight mobile-focus-ring"
    
    const variantClasses = {
      primary: "btn-native-primary",
      secondary: "btn-native-secondary", 
      ghost: "btn-native-ghost",
      outline: "btn-native-outline"
    }

    const sizeClasses = {
      sm: "text-sm px-3 py-2 rounded-lg min-h-[40px]",
      md: "text-sm px-4 py-3 rounded-xl min-h-[44px]",
      lg: "text-base px-6 py-4 rounded-xl min-h-[48px]"
    }

    return (
      <button
        ref={ref}
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          fullWidth && "w-full",
          className
        )}
        {...props}
      >
        {children}
      </button>
    )
  }
)
MobileButton.displayName = "MobileButton"

// Mobile-Optimized Input
interface MobileInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string
  error?: string
  helper?: string
}

export const MobileInput = React.forwardRef<HTMLInputElement, MobileInputProps>(
  ({ className, label, error, helper, ...props }, ref) => {
    const id = React.useId()

    return (
      <div className="space-y-2">
        {label && (
          <label htmlFor={id} className="block text-sm font-medium text-slate-700">
            {label}
          </label>
        )}
        <input
          ref={ref}
          id={id}
          className={cn(
            "input-mobile-enhanced",
            error && "border-red-300 focus:border-red-500 focus:ring-red-500/20",
            className
          )}
          {...props}
        />
        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
        {helper && !error && (
          <p className="text-sm text-slate-500">{helper}</p>
        )}
      </div>
    )
  }
)
MobileInput.displayName = "MobileInput"

// Mobile-Optimized Textarea
interface MobileTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string
  error?: string
  helper?: string
}

export const MobileTextarea = React.forwardRef<HTMLTextAreaElement, MobileTextareaProps>(
  ({ className, label, error, helper, ...props }, ref) => {
    const id = React.useId()

    return (
      <div className="space-y-2">
        {label && (
          <label htmlFor={id} className="block text-sm font-medium text-slate-700">
            {label}
          </label>
        )}
        <textarea
          ref={ref}
          id={id}
          className={cn(
            "textarea-mobile",
            error && "border-red-300 focus:border-red-500 focus:ring-red-500/20",
            className
          )}
          {...props}
        />
        {error && (
          <p className="text-sm text-red-600">{error}</p>
        )}
        {helper && !error && (
          <p className="text-sm text-slate-500">{helper}</p>
        )}
      </div>
    )
  }
)
MobileTextarea.displayName = "MobileTextarea"

// Mobile-Optimized Grid
interface MobileGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  variant?: "auto" | "cards" | "stats"
}

export const MobileGrid = React.forwardRef<HTMLDivElement, MobileGridProps>(
  ({ className, variant = "auto", children, ...props }, ref) => {
    const gridClasses = {
      auto: "grid-mobile-auto",
      cards: "grid-mobile-cards", 
      stats: "grid-mobile-stats"
    }

    return (
      <div
        ref={ref}
        className={cn(gridClasses[variant], className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileGrid.displayName = "MobileGrid"

// Mobile-Optimized Stack (Vertical Layout)
interface MobileStackProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  spacing?: "xs" | "sm" | "md" | "lg"
}

export const MobileStack = React.forwardRef<HTMLDivElement, MobileStackProps>(
  ({ className, spacing = "md", children, ...props }, ref) => {
    const spacingClasses = {
      xs: "space-mobile-xs",
      sm: "space-mobile-sm",
      md: "space-mobile-md",
      lg: "space-mobile-lg"
    }

    return (
      <div
        ref={ref}
        className={cn("flex flex-col", spacingClasses[spacing], className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
MobileStack.displayName = "MobileStack"

// Mobile-Optimized Typography
interface MobileTextProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode
  variant?: "xs" | "sm" | "base" | "lg" | "xl" | "title" | "subtitle"
  as?: "p" | "span" | "div" | "h1" | "h2" | "h3" | "h4" | "h5" | "h6"
}

export const MobileText = React.forwardRef<HTMLElement, MobileTextProps>(
  ({ className, variant = "base", as: Component = "p", children, ...props }, ref) => {
    const variantClasses = {
      xs: "text-mobile-xs",
      sm: "text-mobile-sm", 
      base: "text-mobile-base",
      lg: "text-mobile-lg",
      xl: "text-mobile-xl",
      title: "text-native-title",
      subtitle: "text-native-subtitle"
    }

    return (
      <Component
        ref={ref as any}
        className={cn(variantClasses[variant], className)}
        {...props}
      >
        {children}
      </Component>
    )
  }
)
MobileText.displayName = "MobileText"

// Hook for mobile-specific behavior
export function useMobileOptimized() {
  const isMobile = useIsMobile()
  
  return {
    isMobile,
    containerClass: isMobile ? "container-native" : "container-premium",
    sectionClass: isMobile ? "section-native" : "section-premium",
    buttonSize: isMobile ? "md" : "default",
    cardSpacing: isMobile ? "sm" : "md"
  }
}
