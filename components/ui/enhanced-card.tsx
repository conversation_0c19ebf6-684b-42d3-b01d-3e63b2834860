import React from "react"
import { cn } from "@/lib/utils"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { BookmarkButton } from "@/components/ui/interactive-elements"

// Enhanced Card Variants
export type CardVariant = "default" | "elevated" | "outlined" | "filled" | "glass"
export type CardSize = "sm" | "md" | "lg" | "xl"

interface EnhancedCardProps extends React.HTMLAttributes<HTMLDivElement> {
  variant?: CardVariant
  size?: CardSize
  hover?: boolean
  loading?: boolean
  children?: React.ReactNode
}

const cardVariants = {
  default: "bg-white border border-slate-200 shadow-sm",
  elevated: "bg-white border-0 shadow-lg hover:shadow-xl",
  outlined: "bg-transparent border-2 border-slate-200 shadow-none",
  filled: "bg-slate-50 border-0 shadow-none",
  glass: "bg-white/80 backdrop-blur-sm border border-white/20 shadow-lg"
}

const cardSizes = {
  sm: "p-4",
  md: "p-6", 
  lg: "p-8",
  xl: "p-10"
}

export function EnhancedCard({ 
  variant = "default", 
  size = "md", 
  hover = true,
  loading = false,
  className, 
  children, 
  ...props 
}: EnhancedCardProps) {
  if (loading) {
    return <CardSkeleton size={size} />
  }

  return (
    <Card
      className={cn(
        "transition-all duration-200",
        cardVariants[variant],
        cardSizes[size],
        hover && "hover:shadow-md hover:-translate-y-0.5",
        className
      )}
      {...props}
    >
      {children}
    </Card>
  )
}

// Project Card Component
interface ProjectCardProps {
  project: {
    id: string
    title: string
    category: string
    status: string
    budget: string
    location: string
    createdAt: string
    bidsCount?: number
    image?: string
  }
  variant?: "customer" | "contractor"
  onView?: (id: string) => void
  onEdit?: (id: string) => void
  onDelete?: (id: string) => void
}

export function ProjectCard({ project, variant = "customer", onView, onEdit, onDelete }: ProjectCardProps) {
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active": return "status-active"
      case "completed": return "status-completed"
      case "draft": return "status-draft"
      case "in-progress": return "status-pending"
      default: return "status-draft"
    }
  }

  return (
    <div className="card-premium p-6 transition-premium hover:shadow-lg">
      {project.image && (
        <div className="h-48 bg-slate-100 rounded-lg overflow-hidden mb-4">
          <img
            src={project.image}
            alt={project.title}
            className="w-full h-full object-cover"
          />
        </div>
      )}

      <div className="space-y-4">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-slate-900 mb-1">
              {project.title}
            </h3>
            <p className="text-sm text-slate-600">
              {project.category} • {project.location}
            </p>
          </div>
          <span className={`px-2 py-1 text-xs font-medium rounded-md border ${getStatusColor(project.status)}`}>
            {project.status}
          </span>
        </div>

        <div className="space-y-3 text-sm">
          <div className="flex justify-between">
            <span className="text-slate-600">Budget</span>
            <span className="font-medium">{project.budget}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-slate-600">Created</span>
            <span className="font-medium">{project.createdAt}</span>
          </div>
          {project.bidsCount !== undefined && (
            <div className="flex justify-between">
              <span className="text-slate-600">Bids</span>
              <span className="font-medium">{project.bidsCount}</span>
            </div>
          )}
        </div>

        <div className="flex gap-2 pt-2">
          {onView && (
            <button
              onClick={() => onView(project.id)}
              className="btn-secondary flex-1"
            >
              View Details
            </button>
          )}
          {variant === "customer" && onEdit && (
            <button
              onClick={() => onEdit(project.id)}
              className="btn-secondary px-4"
            >
              Edit
            </button>
          )}
          {onDelete && (
            <button
              onClick={() => onDelete(project.id)}
              className="btn-secondary px-4 text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              Delete
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

// Contractor Card Component
interface ContractorCardProps {
  contractor: {
    id: string
    name: string
    specialty: string
    rating: number
    reviewCount: number
    location: string
    responseTime: string
    verified: boolean
    image?: string
    priceRange?: string
  }
  onContact?: (id: string) => void
  onView?: (id: string) => void
  onFavorite?: (id: string) => void
  isFavorite?: boolean
}

export function ContractorCard({ 
  contractor, 
  onContact, 
  onView, 
  onFavorite, 
  isFavorite = false 
}: ContractorCardProps) {
  return (
    <EnhancedCard variant="elevated" className="overflow-hidden">
      <div className="relative h-48 bg-slate-100">
        {contractor.image ? (
          <img 
            src={contractor.image} 
            alt={contractor.name}
            className="w-full h-full object-cover"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-brand-primary to-brand-secondary">
            <span className="text-2xl font-bold text-white">
              {contractor.name.charAt(0)}
            </span>
          </div>
        )}
        
        <div className="absolute top-3 right-3">
          <BookmarkButton
            itemId={contractor.id}
            itemType="contractor"
            className="action-button-top-right"
          />
        </div>
      </div>

      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-medium text-slate-900">
          {contractor.name}
        </CardTitle>
        <CardDescription>{contractor.specialty}</CardDescription>
      </CardHeader>

      <CardContent className="space-y-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center space-x-1">
            <span className="text-brand-accent">★</span>
            <span className="font-medium">{contractor.rating}</span>
            <span className="text-slate-500">({contractor.reviewCount})</span>
          </div>
          <span className="text-slate-500">{contractor.location}</span>
        </div>
        
        <div className="flex items-center justify-between text-sm text-slate-500">
          <span>Response: {contractor.responseTime}</span>
          {contractor.priceRange && <span>{contractor.priceRange}</span>}
        </div>
      </CardContent>

      <CardFooter className="flex gap-2">
        {onView && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => onView(contractor.id)}
            className="flex-1"
          >
            View Profile
          </Button>
        )}
        {onContact && (
          <Button
            size="sm"
            onClick={() => onContact(contractor.id)}
          >
            Contact
          </Button>
        )}
      </CardFooter>
    </EnhancedCard>
  )
}

// Stats Card Component
interface StatsCardProps {
  title: string
  value: string | number
  change?: {
    value: number
    type: "increase" | "decrease"
    period: string
  }
  icon?: React.ReactNode
  loading?: boolean
}

export function StatsCard({ title, value, change, icon, loading }: StatsCardProps) {
  if (loading) {
    return <CardSkeleton />
  }

  return (
    <EnhancedCard variant="elevated">
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-slate-600 mb-1">{title}</p>
            <p className="text-2xl font-bold text-slate-900">{value}</p>
            {change && (
              <p className={cn(
                "text-xs mt-1",
                change.type === "increase" ? "text-status-success" : "text-status-error"
              )}>
                {change.type === "increase" ? "↗" : "↘"} {Math.abs(change.value)}% {change.period}
              </p>
            )}
          </div>
          {icon && (
            <div className="p-3 bg-slate-50 rounded-lg">
              {icon}
            </div>
          )}
        </div>
      </CardContent>
    </EnhancedCard>
  )
}

// Card Skeleton for loading states
function CardSkeleton({ size = "md" }: { size?: CardSize }) {
  return (
    <Card className={cn("animate-pulse", cardSizes[size])}>
      <div className="space-y-3">
        <Skeleton className="h-4 w-3/4" />
        <Skeleton className="h-4 w-1/2" />
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-2/3" />
        </div>
      </div>
    </Card>
  )
}
