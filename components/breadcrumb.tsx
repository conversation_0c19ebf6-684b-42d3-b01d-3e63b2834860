"use client"

import { usePathname } from "next/navigation"
import Link from "next/link"
import { ChevronRight, Home } from "lucide-react"
import { cn } from "@/lib/utils"

interface BreadcrumbItem {
  label: string
  href?: string
  isActive?: boolean
}

interface BreadcrumbProps {
  items?: BreadcrumbItem[]
  className?: string
}

export function Breadcrumb({ items, className }: BreadcrumbProps) {
  const pathname = usePathname()

  // Auto-generate breadcrumbs from pathname if items not provided
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    const segments = pathname.split('/').filter(Boolean)
    const breadcrumbs: BreadcrumbItem[] = [
      { label: 'Home', href: '/' }
    ]

    // Route mappings for better labels
    const routeLabels: Record<string, string> = {
      'dashboard': 'Dashboard',
      'projects': 'Projects',
      'project': 'Project',
      'contractors': 'Contractors',
      'contractor': 'Contractor',
      'messages': 'Messages',
      'profile': 'Profile',
      'settings': 'Settings',
      'notifications': 'Notifications',
      'pro': 'Professional',
      'create': 'Create',
      'edit': 'Edit',
      'bids': 'Bids',
      'compare': 'Compare',
      'compare-contractors': 'Compare Contractors',
      'browse': 'Browse',
      'analytics': 'Analytics',
      'earnings': 'Earnings',
      'login': 'Sign In',
      'register': 'Sign Up',
      'onboarding': 'Getting Started'
    }

    let currentPath = ''
    segments.forEach((segment, index) => {
      currentPath += `/${segment}`
      const isLast = index === segments.length - 1
      
      // Skip certain segments or combine them
      if (segment === 'pro' && segments.length > 1) {
        return // Skip 'pro' segment, it will be combined with next
      }

      let label = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
      
      // Handle pro routes
      if (segments[0] === 'pro' && index > 0) {
        const proLabel = routeLabels[segment] || segment.charAt(0).toUpperCase() + segment.slice(1)
        label = `Pro ${proLabel}`
      }

      // Handle project routes
      if (segment === 'project' && segments[index + 1]) {
        const nextSegment = segments[index + 1]
        if (routeLabels[nextSegment]) {
          label = `${label} - ${routeLabels[nextSegment]}`
        }
      }

      breadcrumbs.push({
        label,
        href: isLast ? undefined : currentPath,
        isActive: isLast
      })
    })

    return breadcrumbs
  }

  const breadcrumbItems = items || generateBreadcrumbs()

  if (breadcrumbItems.length <= 1) {
    return null // Don't show breadcrumbs for home page or single-level pages
  }

  return (
    <nav className={cn("flex items-center space-x-1 text-xs text-slate-400", className)}>
      {breadcrumbItems.map((item, index) => (
        <div key={index} className="flex items-center">
          {index > 0 && (
            <span className="mx-1.5 text-slate-300">/</span>
          )}

          {item.href ? (
            <Link
              href={item.href}
              className="hover:text-slate-600 transition-colors"
            >
              {item.label}
            </Link>
          ) : (
            <span className={cn(
              item.isActive && "text-slate-600 font-medium"
            )}>
              {item.label}
            </span>
          )}
        </div>
      ))}
    </nav>
  )
}

// Page header component that includes breadcrumbs
interface PageHeaderProps {
  title: string
  description?: string
  breadcrumbs?: BreadcrumbItem[]
  actions?: React.ReactNode
  className?: string
}

export function PageHeader({
  title,
  description,
  breadcrumbs,
  actions,
  className
}: PageHeaderProps) {
  return (
    <div className={cn("", className)}>
      <div className="flex items-start justify-between">
        <div className="space-y-1">
          <h1 className="text-2xl lg:text-3xl font-bold text-slate-900">{title}</h1>
          {description && (
            <p className="text-slate-500 text-sm max-w-2xl">{description}</p>
          )}
        </div>

        {actions && (
          <div className="flex items-center space-x-2">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}
