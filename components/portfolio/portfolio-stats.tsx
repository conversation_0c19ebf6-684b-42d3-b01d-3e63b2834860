"use client"

import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  FolderOpen, 
  Star, 
  Image as ImageIcon, 
  Tag,
  TrendingUp,
  Eye,
  MessageCircle,
  Award
} from 'lucide-react'

interface PortfolioStatsProps {
  stats: {
    totalItems: number
    featuredItems: number
    totalImages: number
    categoriesUsed: string[]
  }
  className?: string
}

export function PortfolioStats({ stats, className }: PortfolioStatsProps) {
  const featuredPercentage = stats.totalItems > 0 
    ? Math.round((stats.featuredItems / stats.totalItems) * 100) 
    : 0

  const avgImagesPerProject = stats.totalItems > 0 
    ? Math.round(stats.totalImages / stats.totalItems) 
    : 0

  const portfolioCompleteness = Math.min(
    Math.round((stats.totalItems / 10) * 100), 
    100
  )

  return (
    <div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
      {/* Total Projects */}
      <Card className="relative overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600 mb-1">
                Total Projects
              </p>
              <p className="text-2xl font-bold text-slate-900">
                {stats.totalItems}
              </p>
              <p className="text-xs text-slate-500 mt-1">
                {stats.totalItems === 0 ? 'Start building your portfolio' : 
                 stats.totalItems < 5 ? 'Add more projects to showcase your work' :
                 'Great portfolio size!'}
              </p>
            </div>
            <div className="h-12 w-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <FolderOpen className="h-6 w-6 text-blue-600" />
            </div>
          </div>
          
          {/* Progress indicator */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-xs text-slate-500 mb-1">
              <span>Portfolio Completeness</span>
              <span>{portfolioCompleteness}%</span>
            </div>
            <Progress value={portfolioCompleteness} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Featured Projects */}
      <Card className="relative overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600 mb-1">
                Featured Projects
              </p>
              <p className="text-2xl font-bold text-slate-900">
                {stats.featuredItems}
              </p>
              <p className="text-xs text-slate-500 mt-1">
                {featuredPercentage}% of total projects
              </p>
            </div>
            <div className="h-12 w-12 bg-amber-100 rounded-lg flex items-center justify-center">
              <Star className="h-6 w-6 text-amber-600" />
            </div>
          </div>
          
          {/* Featured percentage bar */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-xs text-slate-500 mb-1">
              <span>Featured Ratio</span>
              <span>{featuredPercentage}%</span>
            </div>
            <Progress value={featuredPercentage} className="h-2" />
          </div>
        </CardContent>
      </Card>

      {/* Total Images */}
      <Card className="relative overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600 mb-1">
                Total Images
              </p>
              <p className="text-2xl font-bold text-slate-900">
                {stats.totalImages}
              </p>
              <p className="text-xs text-slate-500 mt-1">
                Avg {avgImagesPerProject} per project
              </p>
            </div>
            <div className="h-12 w-12 bg-green-100 rounded-lg flex items-center justify-center">
              <ImageIcon className="h-6 w-6 text-green-600" />
            </div>
          </div>
          
          {/* Image quality indicator */}
          <div className="mt-4">
            <div className="flex items-center justify-between text-xs text-slate-500 mb-1">
              <span>Image Coverage</span>
              <span>
                {avgImagesPerProject >= 5 ? 'Excellent' : 
                 avgImagesPerProject >= 3 ? 'Good' : 
                 avgImagesPerProject >= 1 ? 'Fair' : 'Poor'}
              </span>
            </div>
            <Progress 
              value={Math.min(avgImagesPerProject * 20, 100)} 
              className="h-2" 
            />
          </div>
        </CardContent>
      </Card>

      {/* Categories */}
      <Card className="relative overflow-hidden">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-slate-600 mb-1">
                Categories
              </p>
              <p className="text-2xl font-bold text-slate-900">
                {stats.categoriesUsed.length}
              </p>
              <p className="text-xs text-slate-500 mt-1">
                {stats.categoriesUsed.length === 0 ? 'No categories yet' :
                 stats.categoriesUsed.length === 1 ? 'Specialized focus' :
                 stats.categoriesUsed.length <= 3 ? 'Good specialization' :
                 'Diverse expertise'}
              </p>
            </div>
            <div className="h-12 w-12 bg-purple-100 rounded-lg flex items-center justify-center">
              <Tag className="h-6 w-6 text-purple-600" />
            </div>
          </div>
          
          {/* Categories list */}
          <div className="mt-4">
            {stats.categoriesUsed.length > 0 ? (
              <div className="flex flex-wrap gap-1">
                {stats.categoriesUsed.slice(0, 3).map((category) => (
                  <Badge 
                    key={category} 
                    variant="secondary" 
                    className="text-xs px-2 py-0.5"
                  >
                    {category}
                  </Badge>
                ))}
                {stats.categoriesUsed.length > 3 && (
                  <Badge variant="outline" className="text-xs px-2 py-0.5">
                    +{stats.categoriesUsed.length - 3}
                  </Badge>
                )}
              </div>
            ) : (
              <p className="text-xs text-slate-400">
                Add projects to see categories
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Additional Metrics Row */}
      <div className="col-span-full grid grid-cols-1 sm:grid-cols-3 gap-4 mt-2">
        {/* Portfolio Performance */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                <TrendingUp className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-900">
                  Portfolio Views
                </p>
                <p className="text-lg font-bold text-slate-900">2,847</p>
                <p className="text-xs text-green-600">+12% this month</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Engagement */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 bg-green-100 rounded-lg flex items-center justify-center">
                <Eye className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-900">
                  Project Views
                </p>
                <p className="text-lg font-bold text-slate-900">1,234</p>
                <p className="text-xs text-green-600">+8% this week</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Rate */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <div className="h-10 w-10 bg-purple-100 rounded-lg flex items-center justify-center">
                <MessageCircle className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-sm font-medium text-slate-900">
                  Contact Rate
                </p>
                <p className="text-lg font-bold text-slate-900">4.2%</p>
                <p className="text-xs text-green-600">Above average</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
