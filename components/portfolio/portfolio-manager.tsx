"use client"

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { usePortfolio } from '@/hooks/use-portfolio'
import { useUser } from '@/contexts/user-context'
import { PortfolioGrid } from './portfolio-grid'
import { PortfolioForm } from './portfolio-form'
import { PortfolioStats } from './portfolio-stats'
import { Plus, Grid, List, BarChart3, Settings } from 'lucide-react'

interface PortfolioManagerProps {
  className?: string
}

export function PortfolioManager({ className }: PortfolioManagerProps) {
  const { user } = useUser()
  const [activeTab, setActiveTab] = useState('overview')
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState<string | null>(null)
  
  const {
    portfolioItems,
    loading,
    error,
    addPortfolioItem,
    updatePortfolioItem,
    deletePortfolioItem,
    reorderPortfolio,
    uploadImages,
    deleteImage,
    reorderImages,
    getPortfolioStats
  } = usePortfolio({ autoFetch: true })

  const stats = getPortfolioStats()

  const handleAddItem = () => {
    setEditingItem(null)
    setShowForm(true)
  }

  const handleEditItem = (itemId: string) => {
    setEditingItem(itemId)
    setShowForm(true)
  }

  const handleFormClose = () => {
    setShowForm(false)
    setEditingItem(null)
  }

  const handleFormSubmit = async (data: any) => {
    try {
      if (editingItem) {
        await updatePortfolioItem(editingItem, data)
      } else {
        await addPortfolioItem(data)
      }
      handleFormClose()
    } catch (error) {
      console.error('Error saving portfolio item:', error)
    }
  }

  if (!user || user.role !== 'pro') {
    return (
      <Card className="p-8 text-center">
        <CardContent>
          <h3 className="text-lg font-medium text-slate-900 mb-2">
            Portfolio Management
          </h3>
          <p className="text-slate-600 mb-4">
            Portfolio management is available for professional contractors only.
          </p>
          <Button variant="outline">
            Switch to Pro Account
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-semibold text-slate-900">Portfolio Management</h1>
          <p className="text-slate-600 mt-1">
            Showcase your best work and attract more clients
          </p>
        </div>
        <Button onClick={handleAddItem} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Project
        </Button>
      </div>

      {/* Quick Stats */}
      <PortfolioStats stats={stats} />

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Grid className="h-4 w-4" />
            <span className="hidden sm:inline">Overview</span>
          </TabsTrigger>
          <TabsTrigger value="grid" className="flex items-center gap-2">
            <Grid className="h-4 w-4" />
            <span className="hidden sm:inline">Grid View</span>
          </TabsTrigger>
          <TabsTrigger value="list" className="flex items-center gap-2">
            <List className="h-4 w-4" />
            <span className="hidden sm:inline">List View</span>
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            <span className="hidden sm:inline">Analytics</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Recent Projects */}
            <div className="lg:col-span-2">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle>Recent Projects</CardTitle>
                  <Button variant="ghost" size="sm" onClick={() => setActiveTab('grid')}>
                    View All
                  </Button>
                </CardHeader>
                <CardContent>
                  <PortfolioGrid
                    items={portfolioItems.slice(0, 6)}
                    loading={loading}
                    onEdit={handleEditItem}
                    onDelete={deletePortfolioItem}
                    compact
                  />
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button 
                    onClick={handleAddItem} 
                    className="w-full justify-start"
                    variant="outline"
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add New Project
                  </Button>
                  <Button 
                    onClick={() => setActiveTab('analytics')} 
                    className="w-full justify-start"
                    variant="outline"
                  >
                    <BarChart3 className="h-4 w-4 mr-2" />
                    View Analytics
                  </Button>
                  <Button 
                    className="w-full justify-start"
                    variant="outline"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Portfolio Settings
                  </Button>
                </CardContent>
              </Card>

              {/* Categories */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Categories</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {stats.categoriesUsed.map((category) => (
                      <Badge key={category} variant="secondary">
                        {category}
                      </Badge>
                    ))}
                    {stats.categoriesUsed.length === 0 && (
                      <p className="text-sm text-slate-500">
                        No categories yet. Add your first project!
                      </p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="grid">
          <PortfolioGrid
            items={portfolioItems}
            loading={loading}
            onEdit={handleEditItem}
            onDelete={deletePortfolioItem}
            onReorder={reorderPortfolio}
          />
        </TabsContent>

        <TabsContent value="list">
          <PortfolioGrid
            items={portfolioItems}
            loading={loading}
            onEdit={handleEditItem}
            onDelete={deletePortfolioItem}
            onReorder={reorderPortfolio}
            viewMode="list"
          />
        </TabsContent>

        <TabsContent value="analytics">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Portfolio Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Total Projects</span>
                    <span className="font-medium">{stats.totalItems}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Featured Projects</span>
                    <span className="font-medium">{stats.featuredItems}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Total Images</span>
                    <span className="font-medium">{stats.totalImages}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Categories</span>
                    <span className="font-medium">{stats.categoriesUsed.length}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Engagement Metrics</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Profile Views</span>
                    <span className="font-medium">1,234</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Portfolio Views</span>
                    <span className="font-medium">856</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Contact Requests</span>
                    <span className="font-medium">42</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Conversion Rate</span>
                    <span className="font-medium">4.9%</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-sm">
                    <p className="font-medium">Project Added</p>
                    <p className="text-slate-600">Modern Kitchen Remodel</p>
                    <p className="text-xs text-slate-500">2 days ago</p>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Images Updated</p>
                    <p className="text-slate-600">Bathroom Renovation</p>
                    <p className="text-xs text-slate-500">1 week ago</p>
                  </div>
                  <div className="text-sm">
                    <p className="font-medium">Project Featured</p>
                    <p className="text-slate-600">Deck Construction</p>
                    <p className="text-xs text-slate-500">2 weeks ago</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Portfolio Form Modal */}
      {showForm && (
        <PortfolioForm
          item={editingItem ? portfolioItems.find(item => item.id === editingItem) : undefined}
          onSubmit={handleFormSubmit}
          onClose={handleFormClose}
          onUploadImages={uploadImages}
          onDeleteImage={deleteImage}
          onReorderImages={reorderImages}
        />
      )}
    </div>
  )
}
