"use client"

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Star, 
  Eye, 
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  Image as ImageIcon,
  GripVertical
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { PortfolioItem } from '@/hooks/use-portfolio'
import { cn } from '@/lib/utils'

interface PortfolioGridProps {
  items: PortfolioItem[]
  loading?: boolean
  onEdit?: (itemId: string) => void
  onDelete?: (itemId: string) => Promise<boolean>
  onReorder?: (itemIds: string[]) => Promise<boolean>
  viewMode?: 'grid' | 'list'
  compact?: boolean
  className?: string
}

export function PortfolioGrid({
  items,
  loading = false,
  onEdit,
  onDelete,
  onReorder,
  viewMode = 'grid',
  compact = false,
  className
}: PortfolioGridProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const handleDelete = async (itemId: string) => {
    if (!onDelete) return
    
    setDeletingId(itemId)
    try {
      await onDelete(itemId)
    } finally {
      setDeletingId(null)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      year: 'numeric'
    })
  }

  if (loading) {
    return (
      <div className={cn(
        viewMode === 'grid' 
          ? `grid gap-4 ${compact ? 'grid-cols-2 md:grid-cols-3' : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'}`
          : 'space-y-4',
        className
      )}>
        {Array.from({ length: compact ? 6 : 9 }).map((_, i) => (
          <Card key={i} className="overflow-hidden">
            <div className="aspect-video bg-slate-200 animate-pulse" />
            <CardContent className="p-4">
              <div className="space-y-2">
                <div className="h-4 bg-slate-200 rounded animate-pulse" />
                <div className="h-3 bg-slate-200 rounded animate-pulse w-3/4" />
                <div className="h-3 bg-slate-200 rounded animate-pulse w-1/2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (items.length === 0) {
    return (
      <Card className="p-8 text-center">
        <CardContent>
          <ImageIcon className="h-12 w-12 text-slate-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 mb-2">
            No portfolio items yet
          </h3>
          <p className="text-slate-600 mb-4">
            Start building your portfolio by adding your first project
          </p>
          <Button onClick={() => onEdit?.('')}>
            Add Your First Project
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={cn(
      viewMode === 'grid' 
        ? `grid gap-4 ${compact ? 'grid-cols-2 md:grid-cols-3' : 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'}`
        : 'space-y-4',
      className
    )}>
      {items.map((item) => (
        <Card 
          key={item.id} 
          className={cn(
            "group overflow-hidden transition-all duration-200 hover:shadow-lg",
            viewMode === 'list' && "flex flex-row",
            compact && "hover:scale-105"
          )}
        >
          {/* Image Section */}
          <div className={cn(
            "relative bg-slate-100",
            viewMode === 'grid' ? "aspect-video" : "w-48 h-32 flex-shrink-0"
          )}>
            {item.images.length > 0 ? (
              <img
                src={item.images[0].url}
                alt={item.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="w-full h-full flex items-center justify-center">
                <ImageIcon className="h-8 w-8 text-slate-400" />
              </div>
            )}
            
            {/* Overlay badges */}
            <div className="absolute top-2 left-2 flex gap-2">
              {item.featured && (
                <Badge className="bg-amber-500 text-white">
                  <Star className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              )}
              {!item.visible && (
                <Badge variant="secondary">
                  Draft
                </Badge>
              )}
            </div>

            {/* Image count */}
            {item.images.length > 1 && (
              <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded">
                +{item.images.length - 1} more
              </div>
            )}

            {/* Actions menu */}
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="secondary" size="sm" className="h-8 w-8 p-0">
                    <MoreHorizontal className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => onEdit?.(item.id)}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Eye className="h-4 w-4 mr-2" />
                    Preview
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => handleDelete(item.id)}
                    className="text-red-600"
                    disabled={deletingId === item.id}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {deletingId === item.id ? 'Deleting...' : 'Delete'}
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          {/* Content Section */}
          <CardContent className={cn(
            "p-4",
            viewMode === 'list' && "flex-1"
          )}>
            <div className="space-y-2">
              {/* Title and Category */}
              <div>
                <h3 className="font-medium text-slate-900 line-clamp-1">
                  {item.title}
                </h3>
                <Badge variant="outline" className="text-xs mt-1">
                  {item.category}
                </Badge>
              </div>

              {/* Description */}
              <p className={cn(
                "text-sm text-slate-600",
                compact ? "line-clamp-2" : "line-clamp-3"
              )}>
                {item.description}
              </p>

              {/* Project Details */}
              {!compact && (
                <div className="grid grid-cols-2 gap-2 text-xs text-slate-500">
                  {item.project_details.duration && (
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {item.project_details.duration}
                    </div>
                  )}
                  {item.project_details.budget_range && (
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-3 w-3" />
                      {item.project_details.budget_range}
                    </div>
                  )}
                  {item.project_details.client_location && (
                    <div className="flex items-center gap-1">
                      <MapPin className="h-3 w-3" />
                      {item.project_details.client_location}
                    </div>
                  )}
                  {item.project_details.completion_date && (
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      {formatDate(item.project_details.completion_date)}
                    </div>
                  )}
                </div>
              )}

              {/* Tags */}
              {item.tags.length > 0 && !compact && (
                <div className="flex flex-wrap gap-1">
                  {item.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {item.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{item.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}

              {/* Client Testimonial */}
              {item.client_testimonial && !compact && (
                <div className="bg-slate-50 p-3 rounded-lg">
                  <p className="text-xs text-slate-600 italic line-clamp-2">
                    "{item.client_testimonial.text}"
                  </p>
                  <div className="flex items-center justify-between mt-2">
                    <p className="text-xs font-medium text-slate-700">
                      - {item.client_testimonial.client_name}
                    </p>
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 5 }).map((_, i) => (
                        <Star 
                          key={i} 
                          className={cn(
                            "h-3 w-3",
                            i < item.client_testimonial!.rating 
                              ? "text-amber-400 fill-current" 
                              : "text-slate-300"
                          )} 
                        />
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
