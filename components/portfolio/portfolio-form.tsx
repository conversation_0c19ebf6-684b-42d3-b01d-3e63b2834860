"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ImageUpload } from '@/components/ui/image-upload'
import { PortfolioItem, PortfolioImage } from '@/hooks/use-portfolio'
import { useUser } from '@/contexts/user-context'
import { X, Plus, Star } from 'lucide-react'

interface PortfolioFormProps {
  item?: PortfolioItem
  onSubmit: (data: Partial<PortfolioItem>) => Promise<void>
  onClose: () => void
  onUploadImages: (files: File[], contractorId: string) => Promise<PortfolioImage[]>
  onDeleteImage: (imageId: string, itemId: string) => Promise<boolean>
  onReorderImages: (itemId: string, imageIds: string[]) => Promise<boolean>
}

const PROJECT_CATEGORIES = [
  'kitchen',
  'bathroom', 
  'flooring',
  'painting',
  'electrical',
  'plumbing',
  'roofing',
  'landscaping',
  'general'
]

const BUDGET_RANGES = [
  'Under $5,000',
  '$5,000 - $15,000',
  '$15,000 - $30,000',
  '$30,000 - $50,000',
  '$50,000 - $100,000',
  'Over $100,000'
]

export function PortfolioForm({
  item,
  onSubmit,
  onClose,
  onUploadImages,
  onDeleteImage,
  onReorderImages
}: PortfolioFormProps) {
  const { user } = useUser()
  const [loading, setLoading] = useState(false)
  const [uploadingImages, setUploadingImages] = useState(false)
  const [newTag, setNewTag] = useState('')
  
  const [formData, setFormData] = useState({
    title: item?.title || '',
    description: item?.description || '',
    category: item?.category || '',
    images: item?.images || [],
    project_details: {
      duration: item?.project_details.duration || '',
      budget_range: item?.project_details.budget_range || '',
      client_location: item?.project_details.client_location || '',
      completion_date: item?.project_details.completion_date || '',
      project_size: item?.project_details.project_size || '',
      materials_used: item?.project_details.materials_used || [],
      challenges_overcome: item?.project_details.challenges_overcome || ''
    },
    tags: item?.tags || [],
    featured: item?.featured || false,
    visible: item?.visible !== false,
    client_testimonial: item?.client_testimonial || {
      text: '',
      client_name: '',
      rating: 5
    }
  })

  const handleInputChange = (field: string, value: any) => {
    if (field.startsWith('project_details.')) {
      const detailField = field.replace('project_details.', '')
      setFormData(prev => ({
        ...prev,
        project_details: {
          ...prev.project_details,
          [detailField]: value
        }
      }))
    } else if (field.startsWith('client_testimonial.')) {
      const testimonialField = field.replace('client_testimonial.', '')
      setFormData(prev => ({
        ...prev,
        client_testimonial: {
          ...prev.client_testimonial,
          [testimonialField]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleImageUpload = async (files: File[]) => {
    if (!user) return
    
    setUploadingImages(true)
    try {
      const uploadedImages = await onUploadImages(files, user.id)
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...uploadedImages]
      }))
    } finally {
      setUploadingImages(false)
    }
  }

  const handleImageDelete = async (imageId: string) => {
    if (!item?.id) return
    
    const success = await onDeleteImage(imageId, item.id)
    if (success) {
      setFormData(prev => ({
        ...prev,
        images: prev.images.filter(img => img.id !== imageId)
      }))
    }
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const addMaterial = () => {
    const material = (document.getElementById('new-material') as HTMLInputElement)?.value
    if (material?.trim() && !formData.project_details.materials_used.includes(material.trim())) {
      setFormData(prev => ({
        ...prev,
        project_details: {
          ...prev.project_details,
          materials_used: [...prev.project_details.materials_used, material.trim()]
        }
      }))
      ;(document.getElementById('new-material') as HTMLInputElement).value = ''
    }
  }

  const removeMaterial = (materialToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      project_details: {
        ...prev.project_details,
        materials_used: prev.project_details.materials_used.filter(material => material !== materialToRemove)
      }
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.title.trim() || !formData.description.trim() || !formData.category) {
      return
    }

    setLoading(true)
    try {
      await onSubmit(formData)
      onClose()
    } finally {
      setLoading(false)
    }
  }

  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {item ? 'Edit Portfolio Item' : 'Add Portfolio Item'}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="title">Project Title *</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="e.g., Modern Kitchen Renovation"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">Category *</Label>
              <Select 
                value={formData.category} 
                onValueChange={(value) => handleInputChange('category', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {PROJECT_CATEGORIES.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category.charAt(0).toUpperCase() + category.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Project Description *</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Describe the project, challenges, and results..."
              rows={4}
              required
            />
          </div>

          {/* Project Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Project Details</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="duration">Project Duration</Label>
                <Input
                  id="duration"
                  value={formData.project_details.duration}
                  onChange={(e) => handleInputChange('project_details.duration', e.target.value)}
                  placeholder="e.g., 3 weeks"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="budget_range">Budget Range</Label>
                <Select 
                  value={formData.project_details.budget_range} 
                  onValueChange={(value) => handleInputChange('project_details.budget_range', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select budget range" />
                  </SelectTrigger>
                  <SelectContent>
                    {BUDGET_RANGES.map((range) => (
                      <SelectItem key={range} value={range}>
                        {range}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="client_location">Location</Label>
                <Input
                  id="client_location"
                  value={formData.project_details.client_location}
                  onChange={(e) => handleInputChange('project_details.client_location', e.target.value)}
                  placeholder="e.g., San Francisco, CA"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="completion_date">Completion Date</Label>
                <Input
                  id="completion_date"
                  type="date"
                  value={formData.project_details.completion_date}
                  onChange={(e) => handleInputChange('project_details.completion_date', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="project_size">Project Size</Label>
              <Input
                id="project_size"
                value={formData.project_details.project_size}
                onChange={(e) => handleInputChange('project_details.project_size', e.target.value)}
                placeholder="e.g., 200 sq ft kitchen"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="challenges">Challenges Overcome</Label>
              <Textarea
                id="challenges"
                value={formData.project_details.challenges_overcome}
                onChange={(e) => handleInputChange('project_details.challenges_overcome', e.target.value)}
                placeholder="Describe any unique challenges and how you solved them..."
                rows={3}
              />
            </div>
          </div>

          {/* Materials Used */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Materials Used</h3>
            
            <div className="flex gap-2">
              <Input
                id="new-material"
                placeholder="Add material (e.g., Quartz countertops)"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addMaterial())}
              />
              <Button type="button" onClick={addMaterial} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {formData.project_details.materials_used.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.project_details.materials_used.map((material) => (
                  <Badge key={material} variant="secondary" className="flex items-center gap-1">
                    {material}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeMaterial(material)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Tags */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Tags</h3>
            
            <div className="flex gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add tag (e.g., modern, luxury)"
                onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
              />
              <Button type="button" onClick={addTag} variant="outline">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {formData.tags.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {formData.tags.map((tag) => (
                  <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                    {tag}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => removeTag(tag)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Images */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Project Images</h3>

            <ImageUpload
              images={[]} // We'll handle this differently for portfolio
              onImagesChange={handleImageUpload}
              maxImages={10}
              maxSizePerImage={5}
              className="border-2 border-dashed border-slate-300 rounded-lg p-6"
            />

            {/* Current Images */}
            {formData.images.length > 0 && (
              <div className="space-y-4">
                <h4 className="font-medium">Current Images ({formData.images.length})</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {formData.images.map((image, index) => (
                    <div key={image.id} className="relative group">
                      <div className="aspect-square bg-slate-100 rounded-lg overflow-hidden">
                        <img
                          src={image.url}
                          alt={image.caption || `Image ${index + 1}`}
                          className="w-full h-full object-cover"
                        />
                      </div>

                      {/* Image type badge */}
                      <Badge
                        variant="secondary"
                        className="absolute top-2 left-2 text-xs"
                      >
                        {image.type}
                      </Badge>

                      {/* Delete button */}
                      <Button
                        type="button"
                        variant="destructive"
                        size="sm"
                        className="absolute top-2 right-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => handleImageDelete(image.id)}
                      >
                        <X className="h-3 w-3" />
                      </Button>

                      {/* Caption input */}
                      <Input
                        placeholder="Add caption..."
                        value={image.caption || ''}
                        onChange={(e) => {
                          const updatedImages = formData.images.map(img =>
                            img.id === image.id ? { ...img, caption: e.target.value } : img
                          )
                          setFormData(prev => ({ ...prev, images: updatedImages }))
                        }}
                        className="mt-2 text-xs"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Client Testimonial */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Client Testimonial (Optional)</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="client_name">Client Name</Label>
                <Input
                  id="client_name"
                  value={formData.client_testimonial.client_name}
                  onChange={(e) => handleInputChange('client_testimonial.client_name', e.target.value)}
                  placeholder="e.g., John Smith"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="rating">Rating</Label>
                <div className="flex items-center gap-2">
                  {Array.from({ length: 5 }).map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 cursor-pointer transition-colors ${
                        i < formData.client_testimonial.rating
                          ? 'text-amber-400 fill-current'
                          : 'text-slate-300'
                      }`}
                      onClick={() => handleInputChange('client_testimonial.rating', i + 1)}
                    />
                  ))}
                  <span className="text-sm text-slate-600 ml-2">
                    {formData.client_testimonial.rating}/5
                  </span>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="testimonial_text">Testimonial</Label>
              <Textarea
                id="testimonial_text"
                value={formData.client_testimonial.text}
                onChange={(e) => handleInputChange('client_testimonial.text', e.target.value)}
                placeholder="What did the client say about your work?"
                rows={3}
              />
            </div>
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Settings</h3>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="featured">Featured Project</Label>
                <p className="text-sm text-slate-600">Highlight this project in your portfolio</p>
              </div>
              <Switch
                id="featured"
                checked={formData.featured}
                onCheckedChange={(checked) => handleInputChange('featured', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="visible">Visible to Public</Label>
                <p className="text-sm text-slate-600">Make this project visible in your portfolio</p>
              </div>
              <Switch
                id="visible"
                checked={formData.visible}
                onCheckedChange={(checked) => handleInputChange('visible', checked)}
              />
            </div>
          </div>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? 'Saving...' : item ? 'Update Project' : 'Add Project'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
