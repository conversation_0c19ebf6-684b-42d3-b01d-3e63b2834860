"use client"

import { useState } from 'react'
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Textarea } from '@/components/ui/textarea'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { ReviewData, reviewSystemService } from '@/services/review-system'
import { useToastActions } from '@/components/ui/toast-system'
import { useUser } from '@/contexts/user-context'
import { 
  Star, 
  ThumbsUp, 
  MessageCircle, 
  Flag,
  Calendar,
  CheckCircle,
  Award,
  Users,
  Clock,
  DollarSign,
  Camera,
  Send,
  MoreHorizontal
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

interface ReviewDisplayProps {
  review: ReviewData
  onHelpfulClick?: (reviewId: string) => void
  onResponseSubmit?: (reviewId: string, response: string) => void
  canRespond?: boolean
  className?: string
}

const CATEGORY_LABELS = {
  quality: 'Quality',
  communication: 'Communication',
  timeliness: 'Timeliness',
  professionalism: 'Professionalism',
  value: 'Value'
}

const CATEGORY_ICONS = {
  quality: Award,
  communication: MessageCircle,
  timeliness: Clock,
  professionalism: Users,
  value: DollarSign
}

export function ReviewDisplay({
  review,
  onHelpfulClick,
  onResponseSubmit,
  canRespond = false,
  className
}: ReviewDisplayProps) {
  const { user } = useUser()
  const { showError, showSuccess } = useToastActions()
  const [showResponse, setShowResponse] = useState(false)
  const [responseText, setResponseText] = useState('')
  const [submittingResponse, setSubmittingResponse] = useState(false)
  const [submittingHelpful, setSubmittingHelpful] = useState(false)

  const handleHelpfulClick = async () => {
    if (!user) {
      showError('Please log in to mark reviews as helpful')
      return
    }

    setSubmittingHelpful(true)
    try {
      const response = await reviewSystemService.markReviewHelpful(review.id, user.id)
      
      if (response.success) {
        showSuccess('Thank you for your feedback!')
        onHelpfulClick?.(review.id)
      } else {
        showError(response.error || 'Failed to mark review as helpful')
      }
    } catch (error) {
      showError('An unexpected error occurred')
    } finally {
      setSubmittingHelpful(false)
    }
  }

  const handleResponseSubmit = async () => {
    if (!user || !responseText.trim()) {
      showError('Please enter a response')
      return
    }

    setSubmittingResponse(true)
    try {
      const response = await reviewSystemService.respondToReview(review.id, responseText, user.id)
      
      if (response.success) {
        showSuccess('Response submitted successfully!')
        setShowResponse(false)
        setResponseText('')
        onResponseSubmit?.(review.id, responseText)
      } else {
        showError(response.error || 'Failed to submit response')
      }
    } catch (error) {
      showError('An unexpected error occurred')
    } finally {
      setSubmittingResponse(false)
    }
  }

  const handleFlagReview = async (reason: string) => {
    if (!user) {
      showError('Please log in to report reviews')
      return
    }

    try {
      const response = await reviewSystemService.flagReview(review.id, reason, user.id)
      
      if (response.success) {
        showSuccess('Review reported successfully')
      } else {
        showError(response.error || 'Failed to report review')
      }
    } catch (error) {
      showError('An unexpected error occurred')
    }
  }

  const renderStarRating = (rating: number, size: 'sm' | 'md' = 'sm') => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5'
    }

    return (
      <div className="flex items-center gap-1">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star
            key={i}
            className={`${sizeClasses[size]} ${
              i < rating 
                ? 'text-amber-400 fill-current' 
                : 'text-slate-300'
            }`}
          />
        ))}
      </div>
    )
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <Card className={`overflow-hidden ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-4">
            <Avatar className="h-12 w-12">
              <AvatarImage src={review.reviewer?.avatar_url} />
              <AvatarFallback className="bg-blue-100 text-blue-600 font-semibold">
                {review.reviewer?.first_name?.charAt(0) || 'U'}
              </AvatarFallback>
            </Avatar>

            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h4 className="font-semibold text-slate-900">
                  {review.reviewer?.first_name} {review.reviewer?.last_name}
                </h4>
                {review.verified_purchase && (
                  <Badge variant="secondary" className="text-xs">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>

              <div className="flex items-center gap-3 mb-2">
                {renderStarRating(review.rating, 'md')}
                <span className="font-medium text-slate-900">{review.rating}/5</span>
                <span className="text-slate-500">•</span>
                <div className="flex items-center gap-1 text-sm text-slate-500">
                  <Calendar className="h-4 w-4" />
                  {formatDate(review.created_at)}
                </div>
              </div>

              <h5 className="font-medium text-slate-900 mb-2">{review.title}</h5>
            </div>
          </div>

          {/* Actions Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleFlagReview('inappropriate')}>
                <Flag className="h-4 w-4 mr-2" />
                Report Review
              </DropdownMenuItem>
              {canRespond && !review.response && (
                <>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => setShowResponse(true)}>
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Respond to Review
                  </DropdownMenuItem>
                </>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Review Comment */}
        <div>
          <p className="text-slate-700 leading-relaxed">{review.comment}</p>
        </div>

        {/* Category Ratings */}
        <div className="space-y-3">
          <h6 className="font-medium text-slate-900">Detailed Ratings</h6>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
            {Object.entries(review.categories).map(([key, rating]) => {
              const Icon = CATEGORY_ICONS[key as keyof typeof CATEGORY_ICONS]
              const label = CATEGORY_LABELS[key as keyof typeof CATEGORY_LABELS]
              
              return (
                <div key={key} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Icon className="h-4 w-4 text-slate-400" />
                    <span className="text-sm text-slate-600">{label}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {renderStarRating(rating)}
                    <span className="text-sm font-medium text-slate-700">{rating}/5</span>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Photos */}
        {review.photos && review.photos.length > 0 && (
          <div className="space-y-3">
            <h6 className="font-medium text-slate-900 flex items-center gap-2">
              <Camera className="h-4 w-4" />
              Photos ({review.photos.length})
            </h6>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {review.photos.map((photo, index) => (
                <div key={photo.id} className="relative group">
                  <div className="aspect-square bg-slate-100 rounded-lg overflow-hidden">
                    <img
                      src={photo.url}
                      alt={photo.caption || `Review photo ${index + 1}`}
                      className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                    />
                  </div>
                  {photo.caption && (
                    <p className="text-xs text-slate-600 mt-1 line-clamp-2">
                      {photo.caption}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Response from Business */}
        {review.response && (
          <div className="bg-slate-50 rounded-lg p-4 border-l-4 border-blue-500">
            <div className="flex items-center gap-2 mb-2">
              <MessageCircle className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-slate-900">Response from Business</span>
              <span className="text-sm text-slate-500">
                {formatDate(review.response.date)}
              </span>
            </div>
            <p className="text-slate-700">{review.response.text}</p>
          </div>
        )}

        {/* Response Form */}
        {showResponse && (
          <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
            <h6 className="font-medium text-slate-900 mb-3">Respond to this review</h6>
            <Textarea
              value={responseText}
              onChange={(e) => setResponseText(e.target.value)}
              placeholder="Thank the reviewer and address any concerns..."
              rows={4}
              className="mb-3"
            />
            <div className="flex items-center gap-2">
              <Button
                onClick={handleResponseSubmit}
                disabled={submittingResponse || !responseText.trim()}
                size="sm"
              >
                {submittingResponse ? 'Submitting...' : 'Submit Response'}
                <Send className="h-4 w-4 ml-2" />
              </Button>
              <Button
                variant="outline"
                onClick={() => {
                  setShowResponse(false)
                  setResponseText('')
                }}
                size="sm"
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        <Separator />

        {/* Actions */}
        <div className="flex items-center justify-between">
          <Button
            variant="ghost"
            size="sm"
            onClick={handleHelpfulClick}
            disabled={submittingHelpful}
            className="text-slate-600 hover:text-slate-900"
          >
            <ThumbsUp className="h-4 w-4 mr-2" />
            Helpful ({review.helpful_count})
          </Button>

          <div className="flex items-center gap-2 text-sm text-slate-500">
            <span>Project: {review.project?.title}</span>
            <Badge variant="outline" className="text-xs">
              {review.project?.category}
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
