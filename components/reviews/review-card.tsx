"use client"

import { useState } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent } from '@/components/ui/card'
import { 
  Star, 
  ThumbsUp, 
  MessageCircle, 
  Calendar,
  CheckCircle,
  Image as ImageIcon,
  ChevronLeft,
  ChevronRight
} from 'lucide-react'
import { format, formatDistanceToNow } from 'date-fns'
import type { Review } from '@/hooks/use-reviews'

interface ReviewCardProps {
  review: Review
  onMarkHelpful?: (reviewId: string) => Promise<boolean>
  onAddResponse?: (reviewId: string, response: string) => Promise<boolean>
  canRespond?: boolean
  showProject?: boolean
}

export function ReviewCard({
  review,
  onMarkHelpful,
  onAddResponse,
  canRespond = false,
  showProject = false
}: ReviewCardProps) {
  const [showResponse, setShowResponse] = useState(false)
  const [responseText, setResponseText] = useState('')
  const [submittingResponse, setSubmittingResponse] = useState(false)
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0)
  const [showPhotoModal, setShowPhotoModal] = useState(false)

  const handleMarkHelpful = async () => {
    if (onMarkHelpful) {
      await onMarkHelpful(review.id)
    }
  }

  const handleSubmitResponse = async () => {
    if (!responseText.trim() || !onAddResponse) return

    setSubmittingResponse(true)
    try {
      const success = await onAddResponse(review.id, responseText)
      if (success) {
        setResponseText('')
        setShowResponse(false)
      }
    } finally {
      setSubmittingResponse(false)
    }
  }

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? 'text-yellow-400 fill-current' : 'text-slate-300'
        }`}
      />
    ))
  }

  const nextPhoto = () => {
    setCurrentPhotoIndex((prev) => 
      prev === review.photos.length - 1 ? 0 : prev + 1
    )
  }

  const prevPhoto = () => {
    setCurrentPhotoIndex((prev) => 
      prev === 0 ? review.photos.length - 1 : prev - 1
    )
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4">
              <Avatar className="h-12 w-12">
                <AvatarImage src={review.customer?.avatar_url} />
                <AvatarFallback className="bg-blue-100 text-blue-600">
                  {review.customer?.name?.charAt(0) || 'U'}
                </AvatarFallback>
              </Avatar>
              
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-1">
                  <h3 className="font-semibold text-slate-900">
                    {review.customer?.name || 'Anonymous'}
                  </h3>
                  {review.verified && (
                    <Badge variant="secondary" className="text-xs">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  )}
                </div>
                
                <div className="flex items-center space-x-2 mb-2">
                  <div className="flex items-center space-x-1">
                    {renderStars(review.rating)}
                  </div>
                  <span className="text-sm text-slate-500">
                    {formatDistanceToNow(new Date(review.created_at), { addSuffix: true })}
                  </span>
                </div>

                {showProject && review.project && (
                  <div className="text-sm text-slate-600 mb-2">
                    <span className="font-medium">{review.project.title}</span>
                    <span className="text-slate-400 mx-2">•</span>
                    <span className="capitalize">{review.project.category}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Review Content */}
          <div className="space-y-3">
            {review.title && (
              <h4 className="font-medium text-slate-900">{review.title}</h4>
            )}
            
            <p className="text-slate-700 leading-relaxed">{review.content}</p>
          </div>

          {/* Photos */}
          {review.photos && review.photos.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center space-x-2 text-sm text-slate-600">
                <ImageIcon className="h-4 w-4" />
                <span>{review.photos.length} photo{review.photos.length !== 1 ? 's' : ''}</span>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                {review.photos.slice(0, 4).map((photo, index) => (
                  <div
                    key={photo.id}
                    className="relative aspect-square bg-slate-100 rounded-lg overflow-hidden cursor-pointer hover:opacity-90 transition-opacity"
                    onClick={() => {
                      setCurrentPhotoIndex(index)
                      setShowPhotoModal(true)
                    }}
                  >
                    <img
                      src={photo.url}
                      alt={photo.caption || `Review photo ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    {index === 3 && review.photos.length > 4 && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <span className="text-white font-medium">
                          +{review.photos.length - 4} more
                        </span>
                      </div>
                    )}
                    
                    {photo.type && (
                      <Badge 
                        className="absolute top-2 left-2 text-xs"
                        variant={photo.type === 'before' ? 'destructive' : 'default'}
                      >
                        {photo.type}
                      </Badge>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-slate-100">
            <div className="flex items-center space-x-4">
              {onMarkHelpful && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMarkHelpful}
                  className="text-slate-600 hover:text-slate-900"
                >
                  <ThumbsUp className="h-4 w-4 mr-1" />
                  Helpful ({review.helpful_count})
                </Button>
              )}
              
              {canRespond && !review.response && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowResponse(!showResponse)}
                  className="text-slate-600 hover:text-slate-900"
                >
                  <MessageCircle className="h-4 w-4 mr-1" />
                  Respond
                </Button>
              )}
            </div>
            
            <span className="text-xs text-slate-400">
              <Calendar className="h-3 w-3 inline mr-1" />
              {format(new Date(review.created_at), 'MMM d, yyyy')}
            </span>
          </div>

          {/* Response Form */}
          {showResponse && (
            <div className="space-y-3 pt-4 border-t border-slate-100">
              <Textarea
                placeholder="Write your response..."
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                className="min-h-[100px]"
              />
              <div className="flex justify-end space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowResponse(false)}
                >
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSubmitResponse}
                  disabled={!responseText.trim() || submittingResponse}
                >
                  {submittingResponse ? 'Posting...' : 'Post Response'}
                </Button>
              </div>
            </div>
          )}

          {/* Existing Response */}
          {review.response && (
            <div className="bg-slate-50 rounded-lg p-4 mt-4">
              <div className="flex items-start space-x-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src={review.contractor?.users?.avatar_url} />
                  <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                    {review.contractor?.business_name?.charAt(0) || 'C'}
                  </AvatarFallback>
                </Avatar>
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="font-medium text-sm text-slate-900">
                      {review.contractor?.business_name || 'Contractor'}
                    </span>
                    <Badge variant="outline" className="text-xs">
                      Response
                    </Badge>
                  </div>
                  
                  <p className="text-sm text-slate-700 leading-relaxed">
                    {review.response.content}
                  </p>
                  
                  <span className="text-xs text-slate-500 mt-2 block">
                    {formatDistanceToNow(new Date(review.response.created_at), { addSuffix: true })}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>

      {/* Photo Modal */}
      {showPhotoModal && review.photos.length > 0 && (
        <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <img
              src={review.photos[currentPhotoIndex].url}
              alt={review.photos[currentPhotoIndex].caption || 'Review photo'}
              className="max-w-full max-h-full object-contain"
            />
            
            {review.photos.length > 1 && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 text-white hover:bg-white/20"
                  onClick={prevPhoto}
                >
                  <ChevronLeft className="h-6 w-6" />
                </Button>
                
                <Button
                  variant="ghost"
                  size="sm"
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 text-white hover:bg-white/20"
                  onClick={nextPhoto}
                >
                  <ChevronRight className="h-6 w-6" />
                </Button>
              </>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              className="absolute top-4 right-4 text-white hover:bg-white/20"
              onClick={() => setShowPhotoModal(false)}
            >
              ✕
            </Button>
            
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white text-sm">
              {currentPhotoIndex + 1} / {review.photos.length}
            </div>
          </div>
        </div>
      )}
    </Card>
  )
}
