"use client"

import { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ReviewDisplay } from './review-display'
import { ReviewForm } from './review-form'
import { reviewSystemService, ReviewSummary, ReviewData, ReviewFilters } from '@/services/review-system'
import { useToastActions } from '@/components/ui/toast-system'
import { 
  Star, 
  TrendingUp, 
  Award,
  MessageCircle,
  Clock,
  Users,
  DollarSign,
  CheckCircle,
  Filter,
  Plus,
  BarChart3
} from 'lucide-react'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface ReviewSummaryProps {
  userId: string
  userType: 'contractor' | 'customer'
  canAddReview?: boolean
  projectId?: string
  projectTitle?: string
  className?: string
}

const CATEGORY_LABELS = {
  quality: 'Quality',
  communication: 'Communication',
  timeliness: 'Timeliness',
  professionalism: 'Professionalism',
  value: 'Value'
}

const CATEGORY_ICONS = {
  quality: Award,
  communication: MessageCircle,
  timeliness: Clock,
  professionalism: Users,
  value: DollarSign
}

export function ReviewSummary({
  userId,
  userType,
  canAddReview = false,
  projectId,
  projectTitle,
  className
}: ReviewSummaryProps) {
  const { showError, showSuccess } = useToastActions()
  const [summary, setSummary] = useState<ReviewSummary | null>(null)
  const [reviews, setReviews] = useState<ReviewData[]>([])
  const [loading, setLoading] = useState(true)
  const [reviewsLoading, setReviewsLoading] = useState(false)
  const [showReviewForm, setShowReviewForm] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalReviews, setTotalReviews] = useState(0)
  const [filters, setFilters] = useState<ReviewFilters>({
    sort_by: 'newest'
  })

  useEffect(() => {
    loadSummary()
  }, [userId, userType])

  useEffect(() => {
    loadReviews()
  }, [userId, userType, filters, currentPage])

  const loadSummary = async () => {
    try {
      const response = await reviewSystemService.getReviewSummary(userId, userType)
      
      if (response.success && response.data) {
        setSummary(response.data)
      } else {
        showError(response.error || 'Failed to load review summary')
      }
    } catch (error) {
      showError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const loadReviews = async () => {
    setReviewsLoading(true)
    try {
      const response = await reviewSystemService.getReviews(userId, userType, filters, currentPage, 10)
      
      if (response.success && response.data) {
        setReviews(response.data.reviews)
        setTotalReviews(response.data.total)
      } else {
        showError(response.error || 'Failed to load reviews')
      }
    } catch (error) {
      showError('An unexpected error occurred')
    } finally {
      setReviewsLoading(false)
    }
  }

  const handleFilterChange = (key: keyof ReviewFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }

  const renderStarRating = (rating: number, size: 'sm' | 'md' | 'lg' = 'md') => {
    const sizeClasses = {
      sm: 'h-4 w-4',
      md: 'h-5 w-5',
      lg: 'h-6 w-6'
    }

    return (
      <div className="flex items-center gap-1">
        {Array.from({ length: 5 }).map((_, i) => (
          <Star
            key={i}
            className={`${sizeClasses[size]} ${
              i < Math.floor(rating) 
                ? 'text-amber-400 fill-current' 
                : i < rating
                ? 'text-amber-400 fill-current opacity-50'
                : 'text-slate-300'
            }`}
          />
        ))}
      </div>
    )
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <Card className="animate-pulse">
          <CardContent className="p-6">
            <div className="h-32 bg-slate-200 rounded" />
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!summary) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <Star className="h-12 w-12 text-slate-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-slate-900 mb-2">
            No Reviews Yet
          </h3>
          <p className="text-slate-600">
            {userType === 'contractor' 
              ? 'Complete projects to start receiving reviews from customers'
              : 'Reviews from contractors will appear here'
            }
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overall Rating Summary */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Review Summary
            </CardTitle>
            {canAddReview && projectId && (
              <Button onClick={() => setShowReviewForm(true)} size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Write Review
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Overall Rating */}
            <div className="text-center">
              <div className="text-4xl font-bold text-slate-900 mb-2">
                {summary.average_rating.toFixed(1)}
              </div>
              {renderStarRating(summary.average_rating, 'lg')}
              <p className="text-sm text-slate-600 mt-2">
                Based on {summary.total_reviews} review{summary.total_reviews !== 1 ? 's' : ''}
              </p>
              <Badge variant="secondary" className="mt-2">
                <CheckCircle className="h-3 w-3 mr-1" />
                {summary.verified_percentage}% Verified
              </Badge>
            </div>

            {/* Rating Distribution */}
            <div className="space-y-2">
              <h4 className="font-medium text-slate-900 mb-3">Rating Distribution</h4>
              {[5, 4, 3, 2, 1].map((rating) => (
                <div key={rating} className="flex items-center gap-3">
                  <span className="text-sm w-6">{rating}</span>
                  <Star className="h-4 w-4 text-amber-400 fill-current" />
                  <Progress 
                    value={summary.total_reviews > 0 ? (summary.rating_distribution[rating as keyof typeof summary.rating_distribution] / summary.total_reviews) * 100 : 0} 
                    className="flex-1 h-2" 
                  />
                  <span className="text-sm text-slate-600 w-8">
                    {summary.rating_distribution[rating as keyof typeof summary.rating_distribution]}
                  </span>
                </div>
              ))}
            </div>

            {/* Category Averages */}
            <div className="space-y-3">
              <h4 className="font-medium text-slate-900 mb-3">Category Ratings</h4>
              {Object.entries(summary.category_averages).map(([key, average]) => {
                const Icon = CATEGORY_ICONS[key as keyof typeof CATEGORY_ICONS]
                const label = CATEGORY_LABELS[key as keyof typeof CATEGORY_LABELS]
                
                return (
                  <div key={key} className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Icon className="h-4 w-4 text-slate-400" />
                      <span className="text-sm text-slate-600">{label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {renderStarRating(average, 'sm')}
                      <span className="text-sm font-medium text-slate-700 w-8">
                        {average.toFixed(1)}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reviews List */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>
              All Reviews ({totalReviews})
            </CardTitle>
            
            {/* Filters */}
            <div className="flex items-center gap-2">
              <Select 
                value={filters.sort_by || 'newest'} 
                onValueChange={(value) => handleFilterChange('sort_by', value)}
              >
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="highest_rating">Highest Rating</SelectItem>
                  <SelectItem value="lowest_rating">Lowest Rating</SelectItem>
                  <SelectItem value="most_helpful">Most Helpful</SelectItem>
                </SelectContent>
              </Select>

              <Select 
                value={filters.rating?.toString() || 'all'} 
                onValueChange={(value) => handleFilterChange('rating', value === 'all' ? undefined : parseInt(value))}
              >
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="All Ratings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Ratings</SelectItem>
                  <SelectItem value="5">5 Stars</SelectItem>
                  <SelectItem value="4">4 Stars</SelectItem>
                  <SelectItem value="3">3 Stars</SelectItem>
                  <SelectItem value="2">2 Stars</SelectItem>
                  <SelectItem value="1">1 Star</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {reviewsLoading ? (
            <div className="space-y-4">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <div className="h-4 bg-slate-200 rounded w-1/3" />
                      <div className="h-3 bg-slate-200 rounded w-1/2" />
                      <div className="h-16 bg-slate-200 rounded" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : reviews.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <MessageCircle className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No reviews match your current filters</p>
            </div>
          ) : (
            <div className="space-y-6">
              {reviews.map((review) => (
                <ReviewDisplay
                  key={review.id}
                  review={review}
                  canRespond={userType === 'contractor'}
                  onHelpfulClick={() => {
                    // Refresh reviews to show updated helpful count
                    loadReviews()
                  }}
                  onResponseSubmit={() => {
                    // Refresh reviews to show the response
                    loadReviews()
                  }}
                />
              ))}

              {/* Pagination */}
              {totalReviews > 10 && (
                <div className="flex items-center justify-center gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                    disabled={currentPage === 1}
                  >
                    Previous
                  </Button>
                  <span className="text-sm text-slate-600">
                    Page {currentPage} of {Math.ceil(totalReviews / 10)}
                  </span>
                  <Button
                    variant="outline"
                    onClick={() => setCurrentPage(prev => prev + 1)}
                    disabled={currentPage >= Math.ceil(totalReviews / 10)}
                  >
                    Next
                  </Button>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Review Form Modal */}
      {showReviewForm && projectId && (
        <ReviewForm
          projectId={projectId}
          revieweeId={userId}
          revieweeType={userType}
          projectTitle={projectTitle || 'Project'}
          open={showReviewForm}
          onClose={() => setShowReviewForm(false)}
          onSuccess={() => {
            loadSummary()
            loadReviews()
          }}
        />
      )}
    </div>
  )
}
