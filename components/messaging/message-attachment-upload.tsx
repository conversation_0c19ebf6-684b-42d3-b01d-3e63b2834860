"use client"

import React, { useState, useRef, useCallback } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { useFileUpload } from '@/hooks/use-file-upload'
import { 
  Paperclip, 
  X, 
  File, 
  Image, 
  FileText, 
  Upload,
  AlertCircle, 
  CheckCircle,
  Loader2
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface MessageAttachmentUploadProps {
  onFilesSelected: (files: File[]) => void
  onFileRemoved: (index: number) => void
  maxFiles?: number
  disabled?: boolean
  className?: string
}

export function MessageAttachmentUpload({
  onFilesSelected,
  onFileRemoved,
  maxFiles = 5,
  disabled = false,
  className
}: MessageAttachmentUploadProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const {
    files,
    isUploading,
    progress,
    addFiles,
    removeFile,
    clearFiles
  } = useFileUpload({
    maxFiles,
    maxFileSize: 50 * 1024 * 1024, // 50MB for message attachments
    acceptedFileTypes: [
      'image/*',
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    ],
    autoUpload: false,
    onUploadComplete: (results) => {
      // Handle completed uploads if needed
      console.log('Upload completed:', results)
    }
  })

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    
    if (disabled) return
    
    const droppedFiles = Array.from(e.dataTransfer.files)
    if (droppedFiles.length > 0) {
      addFiles(droppedFiles)
      onFilesSelected(droppedFiles)
    }
  }, [disabled, addFiles, onFilesSelected])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    if (!disabled) {
      setIsDragOver(true)
    }
  }, [disabled])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }, [])

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])
    if (selectedFiles.length > 0) {
      addFiles(selectedFiles)
      onFilesSelected(selectedFiles)
    }
    // Reset input value to allow selecting the same file again
    e.target.value = ''
  }

  const handleRemoveFile = (index: number) => {
    removeFile(index)
    onFileRemoved(index)
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <Image className="h-4 w-4 text-blue-500" />
    } else if (file.type.includes('pdf')) {
      return <FileText className="h-4 w-4 text-red-500" />
    } else if (file.type.includes('document') || file.type.includes('word')) {
      return <FileText className="h-4 w-4 text-blue-600" />
    } else if (file.type.includes('sheet') || file.type.includes('excel')) {
      return <FileText className="h-4 w-4 text-green-600" />
    }
    return <File className="h-4 w-4 text-slate-500" />
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-3 w-3 text-green-500" />
      case 'error':
        return <AlertCircle className="h-3 w-3 text-red-500" />
      default:
        return null
    }
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Quick Upload Button */}
      <div className="flex items-center space-x-2">
        <Button
          type="button"
          size="sm"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={disabled || files.length >= maxFiles}
          className="flex items-center space-x-1"
        >
          <Paperclip className="h-4 w-4" />
          <span>Attach</span>
        </Button>

        {files.length > 0 && (
          <Button
            type="button"
            size="sm"
            variant="ghost"
            onClick={clearFiles}
            className="text-slate-500 hover:text-slate-700"
          >
            Clear All
          </Button>
        )}

        <span className="text-xs text-slate-500">
          {files.length}/{maxFiles} files
        </span>
      </div>

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept="image/*,.pdf,.doc,.docx,.xls,.xlsx,.txt"
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {/* Drag and Drop Zone (only show when no files or dragging) */}
      {(files.length === 0 || isDragOver) && (
        <Card
          className={cn(
            "border-2 border-dashed transition-colors cursor-pointer",
            isDragOver ? "border-blue-500 bg-blue-50" : "border-slate-300",
            disabled && "opacity-50 cursor-not-allowed"
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onClick={() => !disabled && fileInputRef.current?.click()}
        >
          <CardContent className="flex flex-col items-center justify-center py-6 text-center">
            <Upload className={cn(
              "h-6 w-6 mb-2",
              isDragOver ? "text-blue-500" : "text-slate-400"
            )} />
            <p className="text-sm text-slate-600">
              {isDragOver ? "Drop files here" : "Click or drag files to attach"}
            </p>
            <p className="text-xs text-slate-500 mt-1">
              Images, PDFs, Documents up to 50MB
            </p>
          </CardContent>
        </Card>
      )}

      {/* File List */}
      {files.length > 0 && (
        <div className="space-y-2">
          {files.map((fileState, index) => (
            <div
              key={index}
              className="flex items-center space-x-3 p-2 bg-slate-50 rounded-lg border"
            >
              {getFileIcon(fileState.file)}
              
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-slate-900 truncate">
                  {fileState.file.name}
                </p>
                <div className="flex items-center space-x-2">
                  <p className="text-xs text-slate-500">
                    {formatFileSize(fileState.file.size)}
                  </p>
                  {fileState.status === 'uploading' && (
                    <div className="flex items-center space-x-1">
                      <Progress value={fileState.progress} className="w-16 h-1" />
                      <span className="text-xs text-slate-500">{fileState.progress}%</span>
                    </div>
                  )}
                </div>
                {fileState.error && (
                  <p className="text-xs text-red-500 mt-1">{fileState.error}</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                {getStatusIcon(fileState.status)}
                <Badge variant={
                  fileState.status === 'completed' ? 'default' :
                  fileState.status === 'error' ? 'destructive' :
                  fileState.status === 'uploading' ? 'secondary' : 'outline'
                } className="text-xs">
                  {fileState.status}
                </Badge>
                <Button
                  type="button"
                  size="sm"
                  variant="ghost"
                  onClick={() => handleRemoveFile(index)}
                  className="h-6 w-6 p-0 text-slate-400 hover:text-slate-600"
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Overall Progress */}
      {isUploading && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-slate-600">Uploading files...</span>
            <span className="text-slate-600">{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}
    </div>
  )
}
