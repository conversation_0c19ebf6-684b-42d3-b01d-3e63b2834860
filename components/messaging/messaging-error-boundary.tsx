"use client"

import React from 'react'
import { ErrorBoundary } from '@/components/ui/error-boundary'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { MessageCircle, RefreshCw, Wifi, WifiOff } from 'lucide-react'

interface MessagingErrorFallbackProps {
  error?: Error
  resetError: () => void
  isConnected?: boolean
}

function MessagingErrorFallback({ error, resetError, isConnected = false }: MessagingErrorFallbackProps) {
  const isConnectionError = error?.message.includes('network') || 
                           error?.message.includes('connection') ||
                           error?.message.includes('fetch')

  return (
    <div className="flex flex-col items-center justify-center h-64 p-8">
      <Card className="w-full max-w-sm">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {isConnectionError ? (
              <WifiOff className="h-12 w-12 text-red-400" />
            ) : (
              <MessageCircle className="h-12 w-12 text-red-400" />
            )}
          </div>
          <CardTitle className="text-lg text-slate-900">
            {isConnectionError ? 'Connection Problem' : 'Messaging Error'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4 text-center">
          <p className="text-slate-600 text-sm">
            {isConnectionError 
              ? 'Unable to connect to messaging service. Please check your internet connection.'
              : 'There was a problem with the messaging system. Please try again.'
            }
          </p>

          {!isConnected && (
            <div className="flex items-center justify-center space-x-2 text-xs text-amber-600 bg-amber-50 p-2 rounded">
              <WifiOff className="h-3 w-3" />
              <span>Offline - Messages may not sync</span>
            </div>
          )}

          <div className="flex flex-col space-y-2">
            <Button onClick={resetError} size="sm" className="w-full">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => window.location.reload()}
              className="w-full"
            >
              Refresh Page
            </Button>
          </div>

          {process.env.NODE_ENV === 'development' && error && (
            <details className="text-left">
              <summary className="cursor-pointer text-xs text-slate-500">
                Debug Info
              </summary>
              <pre className="text-xs text-slate-600 mt-2 p-2 bg-slate-50 rounded overflow-auto">
                {error.message}
              </pre>
            </details>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

interface MessagingErrorBoundaryProps {
  children: React.ReactNode
  isConnected?: boolean
  onError?: (error: Error) => void
}

export function MessagingErrorBoundary({
  children,
  isConnected = true,
  onError
}: MessagingErrorBoundaryProps) {
  return (
    <ErrorBoundary
      fallback={
        <MessagingErrorFallback
          resetError={() => window.location.reload()}
          isConnected={isConnected}
        />
      }
    >
      {children}
    </ErrorBoundary>
  )
}

// Connection Status Component
export function ConnectionStatus({ isConnected }: { isConnected: boolean }) {
  if (isConnected) {
    return (
      <div className="flex items-center space-x-2 text-xs text-green-600 bg-green-50 px-2 py-1 rounded">
        <Wifi className="h-3 w-3" />
        <span>Connected</span>
      </div>
    )
  }

  return (
    <div className="flex items-center space-x-2 text-xs text-amber-600 bg-amber-50 px-2 py-1 rounded">
      <WifiOff className="h-3 w-3" />
      <span>Reconnecting...</span>
    </div>
  )
}



// Retry wrapper for messaging operations
export function withRetry<T extends any[], R>(
  fn: (...args: T) => Promise<R>,
  maxRetries: number = 3,
  delay: number = 1000
) {
  return async (...args: T): Promise<R> => {
    let lastError: Error

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await fn(...args)
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxRetries) {
          throw lastError
        }

        // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)))
      }
    }

    throw lastError!
  }
}
