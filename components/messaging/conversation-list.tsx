"use client"

import { useState } from 'react'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { ScrollArea } from '@/components/ui/scroll-area'
import { MessageCircle, Search, Plus, Clock } from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface Conversation {
  id: string
  project_id: string
  participants: string[]
  created_at: string
  updated_at: string
  projects?: {
    id: string
    title: string
    description?: string
    category: string
    status: string
  }
  participants_data?: Array<{
    id: string
    name: string
    avatar_url?: string
    role: string
    business_name?: string
    company_name?: string
  }>
  latestMessage?: {
    id: string
    content: string
    sent_at: string
    status: string
    type: string
    users: {
      id: string
      name: string
      avatar_url?: string
    }
  } | null
  unreadCount: number
}

interface ConversationListProps {
  conversations: Conversation[]
  selectedConversationId?: string
  onSelectConversation: (conversationId: string) => void
  onNewConversation?: () => void
  loading?: boolean
  currentUserId: string
}

export function ConversationList({
  conversations,
  selectedConversationId,
  onSelectConversation,
  onNewConversation,
  loading = false,
  currentUserId
}: ConversationListProps) {
  const [searchQuery, setSearchQuery] = useState('')

  const getOtherParticipant = (conversation: Conversation) => {
    if (conversation.participants_data && conversation.participants_data.length > 0) {
      // Find the participant who is not the current user
      const otherParticipant = conversation.participants_data.find(p => p.id !== currentUserId)
      return otherParticipant || conversation.participants_data[0]
    }
    return {
      id: conversation.participants.find(id => id !== currentUserId) || 'unknown',
      name: 'Unknown User',
      avatar_url: null,
      role: 'customer'
    }
  }

  const getConversationDisplayInfo = (conversation: Conversation) => {
    const project = conversation.projects
    const otherParticipant = getOtherParticipant(conversation)

    // Primary title: Project category or participant info
    let primaryTitle = ''
    let subtitle = ''
    let avatar = otherParticipant.avatar_url || "/placeholder.svg"
    let avatarFallback = 'P'

    if (project) {
      // Use project category as primary title
      primaryTitle = project.category ?
        project.category.charAt(0).toUpperCase() + project.category.slice(1).replace(/[_-]/g, ' ') :
        'Project Discussion'

      // Subtitle: Brief project description (first 40 characters)
      subtitle = project.title ?
        project.title.length > 40 ? project.title.substring(0, 40) + '...' : project.title :
        project.description ?
          project.description.length > 40 ? project.description.substring(0, 40) + '...' : project.description :
          'Project details'

      avatarFallback = project.category ? project.category.charAt(0).toUpperCase() : 'P'
    } else {
      // Fallback to participant information
      if (otherParticipant.name !== 'Unknown User') {
        // Check if it's a contractor with business name
        if (otherParticipant.role === 'contractor' && otherParticipant.business_name) {
          primaryTitle = otherParticipant.business_name
          subtitle = otherParticipant.name
        } else if (otherParticipant.company_name) {
          // Customer with company
          primaryTitle = otherParticipant.company_name
          subtitle = otherParticipant.name
        } else {
          // Individual
          primaryTitle = otherParticipant.name
          subtitle = otherParticipant.role === 'contractor' ? 'Contractor' : 'Customer'
        }
        avatarFallback = otherParticipant.name.charAt(0).toUpperCase()
      } else {
        primaryTitle = 'Project Discussion'
        subtitle = 'Conversation'
        avatarFallback = 'C'
      }
    }

    return {
      primaryTitle,
      subtitle,
      avatar,
      avatarFallback,
      otherParticipant
    }
  }

  const filteredConversations = conversations.filter(conversation => {
    const displayInfo = getConversationDisplayInfo(conversation)
    const searchLower = searchQuery.toLowerCase()

    return displayInfo.primaryTitle.toLowerCase().includes(searchLower) ||
           displayInfo.subtitle.toLowerCase().includes(searchLower) ||
           conversation.latestMessage?.content.toLowerCase().includes(searchLower) ||
           displayInfo.otherParticipant.name.toLowerCase().includes(searchLower) ||
           (conversation.projects?.category && conversation.projects.category.toLowerCase().includes(searchLower))
  })

  const formatMessageTime = (timestamp: string) => {
    return formatDistanceToNow(new Date(timestamp), { addSuffix: true })
  }

  const getMessagePreview = (message: Conversation['latestMessage']) => {
    if (!message) return 'No messages yet'

    if (message.type === 'image') return '📷 Image'
    if (message.type === 'file') return '📎 File'
    if (message.type === 'system') return message.content

    return message.content.length > 50
      ? message.content.substring(0, 50) + '...'
      : message.content
  }

  const truncateMessage = (content: string, maxLength: number = 50) => {
    return content.length > maxLength ? `${content.substring(0, maxLength)}...` : content
  }

  if (loading) {
    return (
      <div className="h-full flex flex-col">
        <div className="p-4 border-b">
          <div className="h-10 bg-slate-200 rounded animate-pulse mb-4"></div>
          <div className="h-8 bg-slate-200 rounded animate-pulse"></div>
        </div>
        <div className="flex-1 p-4 space-y-4">
          {Array.from({ length: 5 }).map((_, index) => (
            <div key={index} className="flex items-center space-x-3 animate-pulse">
              <div className="w-12 h-12 bg-slate-200 rounded-full"></div>
              <div className="flex-1">
                <div className="h-4 bg-slate-200 rounded mb-2"></div>
                <div className="h-3 bg-slate-200 rounded w-3/4"></div>
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Search - Mobile-Native */}
      <div className="mobile-spacing-sm border-b border-slate-100">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-10 text-sm border-slate-200 focus:border-blue-500 focus:ring-1 focus:ring-blue-500/20 bg-slate-50 rounded-xl"
          />
        </div>
      </div>

      {/* Conversation List - Mobile-Native */}
      <ScrollArea className="flex-1">
        <div className="mobile-spacing-sm list-native">
          {filteredConversations.length === 0 ? (
            <div className="text-center py-8 text-slate-500 mobile-spacing-md">
              <MessageCircle className="h-8 w-8 sm:h-10 sm:w-10 mx-auto mb-3 text-slate-300" />
              <h3 className="text-native-title mb-2">No conversations</h3>
              <p className="text-native-body mb-4">
                {searchQuery ? 'No conversations found' : 'Start a new conversation to get started'}
              </p>
              {!searchQuery && onNewConversation && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onNewConversation}
                  className="btn-native-secondary"
                >
                  Start a conversation
                </Button>
              )}
            </div>
          ) : (
            filteredConversations.map((conversation) => {
              const displayInfo = getConversationDisplayInfo(conversation)
              return (
              <div
                key={conversation.id}
                onClick={() => onSelectConversation(conversation.id)}
                className={`list-item-native cursor-pointer ${
                  selectedConversationId === conversation.id
                    ? 'bg-blue-50 border-l-2 border-blue-500'
                    : 'hover:bg-slate-50'
                }`}
              >
                <div className="flex items-start space-x-3 w-full">
                  {/* Avatar - Mobile-Native */}
                  <Avatar className="h-8 w-8 flex-shrink-0">
                    <AvatarImage src={displayInfo.avatar} />
                    <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                      {displayInfo.avatarFallback}
                    </AvatarFallback>
                  </Avatar>

                  {/* Content - Mobile-Native */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between mb-1">
                      <div className="flex flex-col">
                        <h3 className="text-sm font-medium text-slate-900 truncate">
                          {displayInfo.primaryTitle}
                        </h3>
                        <p className="text-xs text-slate-500 truncate">
                          {displayInfo.subtitle}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2 flex-shrink-0">
                        {conversation.unreadCount > 0 && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                        )}
                        {conversation.latestMessage && (
                          <span className="text-xs text-slate-500">
                            {formatMessageTime(conversation.latestMessage.sent_at)}
                          </span>
                        )}
                      </div>
                    </div>

                    {conversation.latestMessage && (
                      <p className="text-xs text-slate-600 truncate">
                        <span className="font-medium">
                          {conversation.latestMessage.users.name}:
                        </span>{' '}
                        {truncateMessage(conversation.latestMessage.content, 35)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )})
          )}
        </div>
      </ScrollArea>
    </div>
  )
}
