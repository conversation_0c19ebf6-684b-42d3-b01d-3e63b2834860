"use client"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { useUser } from "@/contexts/user-context"
import { Loader2 } from "lucide-react"

interface RouteGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireRole?: "customer" | "pro"
  redirectTo?: string
}

export function RouteGuard({ 
  children, 
  requireAuth = false, 
  requireRole,
  redirectTo 
}: RouteGuardProps) {
  const { user, loading, isAuthenticated } = useUser()
  const router = useRouter()
  const pathname = usePathname()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    if (loading) return

    const checkAccess = () => {
      // If authentication is required but user is not authenticated
      if (requireAuth && !isAuthenticated) {
        const returnUrl = encodeURIComponent(pathname)
        router.push(`/login?returnUrl=${returnUrl}`)
        return
      }

      // If specific role is required but user doesn't have it
      if (requireRole && user && user.role !== requireRole) {
        if (redirectTo) {
          router.push(redirectTo)
        } else {
          // Redirect to appropriate dashboard based on user's actual role
          const dashboardPath = user.role === 'pro' ? '/pro/dashboard' : '/dashboard'
          router.push(dashboardPath)
        }
        return
      }

      // If user is authenticated but trying to access auth pages
      if (isAuthenticated && (pathname === '/login' || pathname === '/register')) {
        const dashboardPath = user?.role === 'pro' ? '/pro/dashboard' : '/dashboard'
        router.push(dashboardPath)
        return
      }

      setIsChecking(false)
    }

    checkAccess()
  }, [loading, isAuthenticated, user, requireAuth, requireRole, pathname, router, redirectTo])

  // Show loading while checking authentication
  if (loading || isChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-white to-blue-50/30">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-slate-600">Loading...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}

// Higher-order component for protecting pages
export function withRouteGuard<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<RouteGuardProps, 'children'>
) {
  return function ProtectedComponent(props: P) {
    return (
      <RouteGuard {...options}>
        <Component {...props} />
      </RouteGuard>
    )
  }
}

// Protected route components for common use cases
export function AuthenticatedRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requireAuth={true}>
      {children}
    </RouteGuard>
  )
}

export function CustomerRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requireAuth={true} requireRole="customer">
      {children}
    </RouteGuard>
  )
}

export function ProRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requireAuth={true} requireRole="pro">
      {children}
    </RouteGuard>
  )
}

export function GuestRoute({ children }: { children: React.ReactNode }) {
  return (
    <RouteGuard requireAuth={false}>
      {children}
    </RouteGuard>
  )
}
