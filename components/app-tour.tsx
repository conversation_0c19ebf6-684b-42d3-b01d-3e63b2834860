"use client"

import { useState, useEffect, useRef } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>, ArrowLeft, Play, Pause } from "lucide-react"
import { Button } from "@/components/ui/button"

interface TourStep {
  id: string
  title: string
  description: string
  target: string // CSS selector for the element to highlight
  position: 'top' | 'bottom' | 'left' | 'right' | 'center'
  action?: 'click' | 'hover' | 'focus'
  delay?: number
}

const tourSteps: TourStep[] = [
  {
    id: 'welcome',
    title: 'Welcome to RenovHub!',
    description: 'Let\'s take a quick tour of the main features to help you get started with your renovation project.',
    target: 'body',
    position: 'center'
  },
  {
    id: 'navigation',
    title: 'Main Navigation',
    description: 'Access all main sections: Dashboard, Projects, Contractors, Messages, and your Profile.',
    target: '[data-tour="navigation"]',
    position: 'bottom'
  },
  {
    id: 'project-form',
    title: 'Describe Your Project',
    description: 'Start by describing your renovation project. Be as detailed as possible for better contractor matches.',
    target: '[data-tour="project-form"]',
    position: 'top',
    action: 'focus'
  },
  {
    id: 'voice-input',
    title: 'Voice Input Feature',
    description: 'Use voice input to quickly describe your project - just click the microphone and speak.',
    target: '[data-tour="voice-input"]',
    position: 'left'
  },
  {
    id: 'photos-button',
    title: 'Add Project Photos',
    description: 'Upload photos of your space or inspiration images to help contractors understand your vision better.',
    target: '[data-tour="photos-button"]',
    position: 'top'
  },
  {
    id: 'location-button',
    title: 'Set Your Location',
    description: 'Add your location to find qualified contractors in your area.',
    target: '[data-tour="location-button"]',
    position: 'top'
  },
  {
    id: 'get-inspired',
    title: 'Get Project Inspiration',
    description: 'Browse popular renovation categories. Click any category to auto-fill your project description.',
    target: '[data-tour="get-inspired"]',
    position: 'top'
  },
  {
    id: 'cta-button',
    title: 'Start Getting Quotes',
    description: 'Once you\'ve described your project, click here to get matched with verified contractors.',
    target: '[data-tour="cta-button"]',
    position: 'top'
  },
  {
    id: 'how-it-works',
    title: 'Our Simple Process',
    description: 'Learn about our 4-step process: Describe → Match → Compare → Hire.',
    target: '[data-tour="how-it-works"]',
    position: 'top'
  }
]

interface AppTourProps {
  isOpen: boolean
  onClose: () => void
  autoStart?: boolean
}

export function AppTour({ isOpen, onClose, autoStart = false }: AppTourProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [isPlaying, setIsPlaying] = useState(autoStart)
  const [highlightedElement, setHighlightedElement] = useState<HTMLElement | null>(null)
  const overlayRef = useRef<HTMLDivElement>(null)
  const tooltipRef = useRef<HTMLDivElement>(null)

  const currentTourStep = tourSteps[currentStep]

  // Auto-advance when playing
  useEffect(() => {
    if (!isPlaying || !isOpen) return

    const timer = setTimeout(() => {
      if (currentStep < tourSteps.length - 1) {
        setCurrentStep(prev => prev + 1)
      } else {
        setIsPlaying(false)
      }
    }, currentTourStep?.delay || 4000)

    return () => clearTimeout(timer)
  }, [currentStep, isPlaying, isOpen, currentTourStep])

  // Highlight target element
  useEffect(() => {
    if (!isOpen || !currentTourStep) return

    const targetElement = document.querySelector(currentTourStep.target) as HTMLElement
    if (targetElement) {
      setHighlightedElement(targetElement)
      
      // Scroll element into view
      targetElement.scrollIntoView({ 
        behavior: 'smooth', 
        block: 'center',
        inline: 'center'
      })

      // Perform action if specified
      if (currentTourStep.action) {
        setTimeout(() => {
          switch (currentTourStep.action) {
            case 'click':
              targetElement.click()
              break
            case 'hover':
              targetElement.dispatchEvent(new MouseEvent('mouseenter', { bubbles: true }))
              break
            case 'focus':
              if (targetElement.focus) targetElement.focus()
              break
          }
        }, 500)
      }
    }

    return () => {
      if (targetElement && currentTourStep.action === 'hover') {
        targetElement.dispatchEvent(new MouseEvent('mouseleave', { bubbles: true }))
      }
    }
  }, [currentStep, isOpen, currentTourStep])

  // Position tooltip
  useEffect(() => {
    if (!highlightedElement || !tooltipRef.current || !isOpen) return

    const updateTooltipPosition = () => {
      const rect = highlightedElement.getBoundingClientRect()
      const tooltip = tooltipRef.current!
      const tooltipRect = tooltip.getBoundingClientRect()
      
      let top = 0
      let left = 0

      switch (currentTourStep.position) {
        case 'top':
          top = rect.top - tooltipRect.height - 20
          left = rect.left + (rect.width - tooltipRect.width) / 2
          break
        case 'bottom':
          top = rect.bottom + 20
          left = rect.left + (rect.width - tooltipRect.width) / 2
          break
        case 'left':
          top = rect.top + (rect.height - tooltipRect.height) / 2
          left = rect.left - tooltipRect.width - 20
          break
        case 'right':
          top = rect.top + (rect.height - tooltipRect.height) / 2
          left = rect.right + 20
          break
        case 'center':
          top = window.innerHeight / 2 - tooltipRect.height / 2
          left = window.innerWidth / 2 - tooltipRect.width / 2
          break
      }

      // Keep tooltip within viewport
      top = Math.max(20, Math.min(top, window.innerHeight - tooltipRect.height - 20))
      left = Math.max(20, Math.min(left, window.innerWidth - tooltipRect.width - 20))

      tooltip.style.top = `${top}px`
      tooltip.style.left = `${left}px`
    }

    updateTooltipPosition()
    window.addEventListener('resize', updateTooltipPosition)
    window.addEventListener('scroll', updateTooltipPosition)

    return () => {
      window.removeEventListener('resize', updateTooltipPosition)
      window.removeEventListener('scroll', updateTooltipPosition)
    }
  }, [highlightedElement, currentTourStep])

  const nextStep = () => {
    if (currentStep < tourSteps.length - 1) {
      setCurrentStep(prev => prev + 1)
    } else {
      onClose()
    }
  }

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
    }
  }

  const togglePlay = () => {
    setIsPlaying(!isPlaying)
  }

  if (!isOpen) return null

  return (
    <>
      {/* Overlay with highlight cutout */}
      <div
        ref={overlayRef}
        className="fixed inset-0 z-50 pointer-events-none"
        style={{
          background: highlightedElement && currentTourStep.target !== 'body'
            ? `radial-gradient(circle at ${highlightedElement.getBoundingClientRect().left + highlightedElement.getBoundingClientRect().width / 2}px ${highlightedElement.getBoundingClientRect().top + highlightedElement.getBoundingClientRect().height / 2}px, transparent ${Math.max(highlightedElement.getBoundingClientRect().width, highlightedElement.getBoundingClientRect().height) / 2 + 10}px, rgba(0, 0, 0, 0.7) ${Math.max(highlightedElement.getBoundingClientRect().width, highlightedElement.getBoundingClientRect().height) / 2 + 20}px)`
            : 'rgba(0, 0, 0, 0.7)'
        }}
      />

      {/* Tooltip */}
      <div
        ref={tooltipRef}
        className="fixed z-50 bg-white rounded-2xl shadow-2xl border border-slate-200 p-6 max-w-sm pointer-events-auto"
        style={{ top: 0, left: 0 }}
      >
        <div className="flex items-start justify-between mb-4">
          <h3 className="text-lg font-bold text-slate-900 pr-4">
            {currentTourStep.title}
          </h3>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-slate-600 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        <p className="text-slate-600 mb-6 leading-relaxed">
          {currentTourStep.description}
        </p>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-slate-500">
              {currentStep + 1} of {tourSteps.length}
            </span>
            <button
              onClick={togglePlay}
              className="p-1 text-slate-400 hover:text-slate-600 transition-colors"
            >
              {isPlaying ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={prevStep}
              disabled={currentStep === 0}
              className="px-3"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Button
              size="sm"
              onClick={nextStep}
              className="bg-philippine-green hover:bg-philippine-green/90 text-white px-4"
            >
              {currentStep === tourSteps.length - 1 ? 'Finish' : 'Next'}
              {currentStep < tourSteps.length - 1 && <ArrowRight className="h-4 w-4 ml-1" />}
            </Button>
          </div>
        </div>

        {/* Progress bar */}
        <div className="mt-4 bg-slate-200 rounded-full h-2">
          <div
            className="bg-philippine-green rounded-full h-2 transition-all duration-300"
            style={{ width: `${((currentStep + 1) / tourSteps.length) * 100}%` }}
          />
        </div>
      </div>
    </>
  )
}
