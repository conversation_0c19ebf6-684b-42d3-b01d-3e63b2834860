"use client"

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useBidding } from '@/hooks/use-bidding'
import { useUser } from '@/contexts/user-context'
import { useToastActions } from '@/components/ui/toast-system'
import { DollarSign, Clock, FileText, Plus, Minus } from 'lucide-react'

interface BidSubmissionFormProps {
  projectId: string
  projectTitle: string
  onSuccess?: () => void
  onCancel?: () => void
}

interface BidBreakdownItem {
  category: string
  description: string
  amount: number
}

interface BidMilestone {
  title: string
  description: string
  duration: number
  amount: number
}

export function BidSubmissionForm({ 
  projectId, 
  projectTitle, 
  onSuccess, 
  onCancel 
}: BidSubmissionFormProps) {
  const { user } = useUser()
  const { createBid } = useBidding({ autoFetch: false })
  const { showError } = useToastActions()
  
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    amount: '',
    description: '',
    timeline: '',
    terms: '',
    warranty: {
      duration: '12',
      coverage: '',
      terms: ''
    }
  })
  
  const [breakdown, setBreakdown] = useState<BidBreakdownItem[]>([
    { category: 'Materials', description: '', amount: 0 },
    { category: 'Labor', description: '', amount: 0 }
  ])
  
  const [milestones, setMilestones] = useState<BidMilestone[]>([
    { title: 'Project Start', description: '', duration: 7, amount: 0 }
  ])

  const addBreakdownItem = () => {
    setBreakdown([...breakdown, { category: '', description: '', amount: 0 }])
  }

  const removeBreakdownItem = (index: number) => {
    setBreakdown(breakdown.filter((_, i) => i !== index))
  }

  const updateBreakdownItem = (index: number, field: keyof BidBreakdownItem, value: string | number) => {
    const updated = [...breakdown]
    updated[index] = { ...updated[index], [field]: value }
    setBreakdown(updated)
  }

  const addMilestone = () => {
    setMilestones([...milestones, { title: '', description: '', duration: 7, amount: 0 }])
  }

  const removeMilestone = (index: number) => {
    setMilestones(milestones.filter((_, i) => i !== index))
  }

  const updateMilestone = (index: number, field: keyof BidMilestone, value: string | number) => {
    const updated = [...milestones]
    updated[index] = { ...updated[index], [field]: value }
    setMilestones(updated)
  }

  const calculateTotal = () => {
    return breakdown.reduce((sum, item) => sum + (item.amount || 0), 0)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user || user.role !== 'contractor') {
      showError('You must be logged in as a contractor to submit bids')
      return
    }

    if (!formData.amount || !formData.description || !formData.timeline) {
      showError('Please fill in all required fields')
      return
    }

    setLoading(true)
    
    try {
      // Calculate expiration date (30 days from now)
      const expiresAt = new Date()
      expiresAt.setDate(expiresAt.getDate() + 30)

      const bidData = {
        project_id: projectId,
        amount: parseFloat(formData.amount),
        currency: 'USD',
        description: formData.description,
        terms: formData.terms,
        timeline: {
          duration: parseInt(formData.timeline),
          milestones: milestones.filter(m => m.title && m.amount > 0)
        },
        breakdown: breakdown.filter(b => b.category && b.amount > 0),
        warranty: {
          duration: parseInt(formData.warranty.duration),
          coverage: formData.warranty.coverage,
          terms: formData.warranty.terms
        },
        expires_at: expiresAt.toISOString()
      }

      const result = await createBid(bidData)
      
      if (result) {
        onSuccess?.()
      }
    } catch (error) {
      console.error('Error submitting bid:', error)
      showError('Failed to submit bid. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5" />
          <span>Submit Bid for "{projectTitle}"</span>
        </CardTitle>
      </CardHeader>
      
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Bid Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Total Bid Amount *
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  type="number"
                  value={formData.amount}
                  onChange={(e) => setFormData({ ...formData, amount: e.target.value })}
                  placeholder="0.00"
                  className="pl-10"
                  required
                />
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Timeline (days) *
              </label>
              <div className="relative">
                <Clock className="absolute left-3 top-3 h-4 w-4 text-slate-400" />
                <Input
                  type="number"
                  value={formData.timeline}
                  onChange={(e) => setFormData({ ...formData, timeline: e.target.value })}
                  placeholder="30"
                  className="pl-10"
                  required
                />
              </div>
            </div>
          </div>

          {/* Project Description */}
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Project Description & Approach *
            </label>
            <Textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Describe your approach to this project, materials you'll use, and what makes your bid competitive..."
              className="min-h-[120px]"
              required
            />
          </div>

          {/* Cost Breakdown */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-slate-900">Cost Breakdown</h3>
              <Button type="button" variant="outline" size="sm" onClick={addBreakdownItem}>
                <Plus className="h-4 w-4 mr-1" />
                Add Item
              </Button>
            </div>
            
            <div className="space-y-3">
              {breakdown.map((item, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-4 gap-3 p-3 border rounded-lg">
                  <Input
                    placeholder="Category"
                    value={item.category}
                    onChange={(e) => updateBreakdownItem(index, 'category', e.target.value)}
                  />
                  <Input
                    placeholder="Description"
                    value={item.description}
                    onChange={(e) => updateBreakdownItem(index, 'description', e.target.value)}
                    className="md:col-span-2"
                  />
                  <div className="flex space-x-2">
                    <Input
                      type="number"
                      placeholder="Amount"
                      value={item.amount || ''}
                      onChange={(e) => updateBreakdownItem(index, 'amount', parseFloat(e.target.value) || 0)}
                    />
                    {breakdown.length > 1 && (
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeBreakdownItem(index)}
                      >
                        <Minus className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
            
            <div className="mt-4 p-3 bg-slate-50 rounded-lg">
              <div className="flex justify-between items-center">
                <span className="font-medium">Total from breakdown:</span>
                <span className="text-lg font-semibold">${calculateTotal().toLocaleString()}</span>
              </div>
            </div>
          </div>

          {/* Terms and Conditions */}
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">
              Terms & Conditions
            </label>
            <Textarea
              value={formData.terms}
              onChange={(e) => setFormData({ ...formData, terms: e.target.value })}
              placeholder="Payment terms, change order policies, cleanup responsibilities, etc."
              className="min-h-[80px]"
            />
          </div>

          {/* Warranty Information */}
          <div>
            <h3 className="text-lg font-medium text-slate-900 mb-4">Warranty Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Duration (months)
                </label>
                <Input
                  type="number"
                  value={formData.warranty.duration}
                  onChange={(e) => setFormData({
                    ...formData,
                    warranty: { ...formData.warranty, duration: e.target.value }
                  })}
                />
              </div>
              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Coverage Details
                </label>
                <Input
                  value={formData.warranty.coverage}
                  onChange={(e) => setFormData({
                    ...formData,
                    warranty: { ...formData.warranty, coverage: e.target.value }
                  })}
                  placeholder="What does your warranty cover?"
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4 pt-6 border-t">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading ? 'Submitting...' : 'Submit Bid'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
