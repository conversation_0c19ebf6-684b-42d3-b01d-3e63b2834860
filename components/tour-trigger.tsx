"use client"

import { useState, useEffect } from "react"
import { HelpCircle, Play, X } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { AppTour } from "@/components/app-tour"
import { useUser } from "@/contexts/user-context"

interface TourTriggerProps {
  autoStart?: boolean
  showWelcome?: boolean
}

export function TourTrigger({ autoStart = false, showWelcome = true }: TourTriggerProps) {
  const { user } = useUser()
  const [isTourOpen, setIsTourOpen] = useState(false)
  const [showWelcomePrompt, setShowWelcomePrompt] = useState(false)
  const [hasSeenTour, setHasSeenTour] = useState(false)

  useEffect(() => {
    // Only show tour for signed-in users
    if (!user) return

    // Check if user has seen the tour before
    const tourSeen = localStorage.getItem(`renovhub-tour-seen-${user.id}`)
    setHasSeenTour(!!tourSeen)

    // Show welcome prompt for new users
    if (showWelcome && !tourSeen && !autoStart) {
      const timer = setTimeout(() => {
        setShowWelcomePrompt(true)
      }, 3000) // Show after 3 seconds for signed-in users

      return () => clearTimeout(timer)
    }

    // Auto-start tour if requested
    if (autoStart && !tourSeen) {
      const timer = setTimeout(() => {
        setIsTourOpen(true)
      }, 1500)

      return () => clearTimeout(timer)
    }
  }, [autoStart, showWelcome, user])

  const startTour = () => {
    setIsTourOpen(true)
    setShowWelcomePrompt(false)
  }

  const closeTour = () => {
    setIsTourOpen(false)
    if (user) {
      localStorage.setItem(`renovhub-tour-seen-${user.id}`, 'true')
    }
    setHasSeenTour(true)
  }

  const dismissWelcome = () => {
    setShowWelcomePrompt(false)
    if (user) {
      localStorage.setItem(`renovhub-tour-seen-${user.id}`, 'true')
    }
    setHasSeenTour(true)
  }

  // Don't show tour components for non-signed-in users
  if (!user) return null

  return (
    <>
      {/* Floating Tour Button */}
      <div className="fixed bottom-4 left-4 z-40">
        <Button
          onClick={startTour}
          className="bg-philippine-green hover:bg-philippine-green/90 text-white rounded-full p-3 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110"
          title="Take a tour of RenovHub"
        >
          <HelpCircle className="h-5 w-5" />
        </Button>
      </div>

      {/* Welcome Prompt */}
      {showWelcomePrompt && (
        <div className="fixed bottom-20 left-4 z-40 bg-white rounded-2xl shadow-2xl border border-slate-200 p-6 max-w-sm animate-in slide-in-from-bottom-4 duration-500">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-philippine-green rounded-full flex items-center justify-center">
                <Play className="h-4 w-4 text-white" />
              </div>
              <h3 className="font-bold text-slate-900">Welcome to RenovHub!</h3>
            </div>
            <button
              onClick={dismissWelcome}
              className="text-slate-400 hover:text-slate-600 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
          
          <p className="text-slate-600 mb-4 text-sm leading-relaxed">
            New here? Take a quick tour to learn how to get started with your renovation project.
          </p>
          
          <div className="flex space-x-2">
            <Button
              onClick={startTour}
              className="bg-philippine-green hover:bg-philippine-green/90 text-white text-sm px-4 py-2 flex-1"
            >
              Start Tour
            </Button>
            <Button
              onClick={dismissWelcome}
              variant="outline"
              className="text-sm px-4 py-2"
            >
              Skip
            </Button>
          </div>
        </div>
      )}

      {/* Tour Component */}
      <AppTour
        isOpen={isTourOpen}
        onClose={closeTour}
        autoStart={false}
      />
    </>
  )
}
